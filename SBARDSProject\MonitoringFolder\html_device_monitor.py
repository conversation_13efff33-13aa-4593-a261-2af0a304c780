"""
HTML Device Monitor for SBARDS

This script monitors all connected devices and storage volumes and generates an HTML report.
"""

import os
import sys
import json
import time
import shutil
import psutil
import datetime
import threading
import logging
import string
import win32api
import win32file
import win32con
import webbrowser
from typing import Dict, List, Any, Set

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/device_monitor.log', mode='a')
    ]
)

logger = logging.getLogger("SBARDS.DeviceMonitor")

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

# Create reports directory if it doesn't exist
reports_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'reports')
os.makedirs(reports_dir, exist_ok=True)

# Load configuration
try:
    config_path = 'SBARDSProject/config.json'
    if not os.path.exists(config_path):
        config_path = 'config.json'
        if not os.path.exists(config_path):
            logger.error("Configuration file not found")
            sys.exit(1)

    with open(config_path, 'r') as f:
        config = json.load(f)
    logger.info("Configuration loaded successfully")
except Exception as e:
    logger.error(f"Error loading configuration: {e}")
    sys.exit(1)

class HTMLDeviceMonitor:
    """HTML Device Monitor for all connected devices."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize the device monitor."""
        self.config = config
        self.logger = logging.getLogger("SBARDS.DeviceMonitor")

        # Device tracking
        self.devices = {}
        self.suspicious_files = []

        # Get suspicious extensions from config
        self.suspicious_extensions = config.get("monitoring", {}).get("filesystem_monitoring", {}).get(
            "suspicious_extensions", [
                ".encrypted", ".locked", ".crypted", ".crypt", ".crypto", ".enc", ".ransomware",
                ".wcry", ".wncry", ".locky", ".zepto", ".cerber", ".coverton", ".enigma", ".pays"
            ]
        )

        # Directories to skip
        self.skip_dirs = [
            "System Volume Information",
            "$RECYCLE.BIN",
            "Windows",
            "Program Files",
            "Program Files (x86)",
            "ProgramData"
        ]

        # Paths to skip (partial matches)
        self.skip_paths = [
            "encoding",
            "Encodings",
            "tcl8",
            "perl5",
            "mingw",
            "git"
        ]

        # Monitoring thread
        self.monitoring_thread = None
        self.stop_event = threading.Event()
        self.is_running = False

        # HTML report path
        self.report_path = os.path.join(reports_dir, f'scan_report_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.html')

        # Initial scan
        self.detect_devices()

    def detect_devices(self) -> None:
        """Detect all connected devices."""
        self.logger.info("Detecting connected devices...")

        try:
            # Get all drives
            drives = []

            # Get all drive letters
            for letter in string.ascii_uppercase:
                drive = f"{letter}:\\"
                if os.path.exists(drive):
                    try:
                        drive_type = win32file.GetDriveType(drive)
                        drive_info = {
                            "path": drive,
                            "type": self._get_drive_type_name(drive_type),
                            "label": self._get_drive_label(drive)
                        }

                        # Get disk usage if available
                        try:
                            usage = shutil.disk_usage(drive)
                            drive_info["total"] = usage.total
                            drive_info["used"] = usage.used
                            drive_info["free"] = usage.free
                            drive_info["percent_used"] = (usage.used / usage.total) * 100 if usage.total > 0 else 0
                        except Exception as e:
                            self.logger.error(f"Error getting disk usage for {drive}: {e}")

                        drives.append(drive_info)
                        self.logger.info(f"Detected drive: {drive} ({drive_info['type']}, {drive_info.get('label', 'No Label')})")
                    except Exception as e:
                        self.logger.error(f"Error detecting drive {drive}: {e}")

            # Update devices
            self.devices = {drive["path"]: drive for drive in drives}

            # Log summary
            self.logger.info(f"Detected {len(drives)} drives")
            for drive in drives:
                if "total" in drive:
                    self.logger.info(f"  {drive['path']} ({drive['type']}): {drive.get('label', 'No Label')}, "
                                    f"{drive['total'] / (1024**3):.2f} GB total, "
                                    f"{drive['free'] / (1024**3):.2f} GB free, "
                                    f"{drive['percent_used']:.2f}% used")
                else:
                    self.logger.info(f"  {drive['path']} ({drive['type']}): {drive.get('label', 'No Label')}")

        except Exception as e:
            self.logger.error(f"Error detecting devices: {e}")

    def _get_drive_type_name(self, drive_type: int) -> str:
        """Get drive type name."""
        drive_types = {
            win32con.DRIVE_UNKNOWN: "Unknown",
            win32con.DRIVE_NO_ROOT_DIR: "No Root Directory",
            win32con.DRIVE_REMOVABLE: "Removable",
            win32con.DRIVE_FIXED: "Fixed",
            win32con.DRIVE_REMOTE: "Network",
            win32con.DRIVE_CDROM: "CD-ROM",
            win32con.DRIVE_RAMDISK: "RAM Disk"
        }
        return drive_types.get(drive_type, "Unknown")

    def _get_drive_label(self, drive: str) -> str:
        """Get drive label."""
        try:
            volume_name = win32api.GetVolumeInformation(drive)[0]
            return volume_name
        except Exception:
            return ""

    def scan_device(self, device_path: str) -> None:
        """Scan a specific device."""
        self.logger.info(f"Scanning device: {device_path}")

        try:
            # Check if device exists
            if not os.path.exists(device_path):
                self.logger.error(f"Device {device_path} not found")
                return

            # Get disk usage
            try:
                disk_usage = shutil.disk_usage(device_path)
                self.logger.info(f"Total space: {disk_usage.total / (1024**3):.2f} GB")
                self.logger.info(f"Used space: {disk_usage.used / (1024**3):.2f} GB")
                self.logger.info(f"Free space: {disk_usage.free / (1024**3):.2f} GB")
                self.logger.info(f"Usage percentage: {disk_usage.used / disk_usage.total * 100:.2f}%")
            except Exception as e:
                self.logger.error(f"Error getting disk usage for {device_path}: {e}")

            # List top-level directories
            try:
                self.logger.info(f"Top-level directories in {device_path}:")
                for item in os.listdir(device_path):
                    item_path = os.path.join(device_path, item)
                    if os.path.isdir(item_path):
                        self.logger.info(f"  Directory: {item}")
                    else:
                        self.logger.info(f"  File: {item}")
            except Exception as e:
                self.logger.error(f"Error listing top-level directories for {device_path}: {e}")

            # Scan for suspicious files
            self.scan_for_suspicious_files(device_path)

        except Exception as e:
            self.logger.error(f"Error scanning device {device_path}: {e}")

    def scan_for_suspicious_files(self, device_path: str) -> None:
        """Scan for suspicious files on a device."""
        self.logger.info(f"Scanning for suspicious files on {device_path}...")

        try:
            # Check if device exists
            if not os.path.exists(device_path):
                self.logger.error(f"Device {device_path} not found")
                return

            # Reset suspicious files for this device
            device_suspicious_files = []

            # Walk through the device
            for root, dirs, files in os.walk(device_path):
                # Skip directories
                skip = False
                for skip_dir in self.skip_dirs:
                    if skip_dir in root:
                        skip = True
                        break

                for skip_path in self.skip_paths:
                    if skip_path.lower() in root.lower():
                        skip = True
                        break

                if skip:
                    # Skip this directory
                    dirs[:] = []  # Don't recurse into subdirectories
                    continue

                for file in files:
                    file_path = os.path.join(root, file)

                    try:
                        # Check file extension
                        _, ext = os.path.splitext(file_path)
                        if ext.lower() in self.suspicious_extensions:
                            # Skip encoding files
                            if ext.lower() == ".enc" and ("encoding" in file_path.lower() or "encodings" in file_path.lower()):
                                continue

                            # Get file size
                            try:
                                file_size = os.path.getsize(file_path)
                            except:
                                file_size = 0

                            # Get file modification time
                            try:
                                file_modified = datetime.datetime.fromtimestamp(os.path.getmtime(file_path)).isoformat()
                            except:
                                file_modified = "unknown"

                            file_info = {
                                "path": file_path,
                                "extension": ext,
                                "size": file_size,
                                "modified": file_modified
                            }
                            device_suspicious_files.append(file_info)
                            self.logger.warning(f"Suspicious file found: {file_path}")

                    except Exception as e:
                        self.logger.error(f"Error processing file {file_path}: {e}")

            # Update suspicious files
            self.suspicious_files.extend(device_suspicious_files)

            self.logger.info(f"Found {len(device_suspicious_files)} suspicious files on {device_path}")

        except Exception as e:
            self.logger.error(f"Error scanning for suspicious files on {device_path}: {e}")

    def start_monitoring(self) -> bool:
        """Start monitoring."""
        if self.is_running:
            self.logger.warning("Monitoring is already running")
            return True

        self.logger.info("Starting device monitoring")
        self.stop_event.clear()

        # Start monitoring thread
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self.monitoring_thread.start()

        self.is_running = True
        return True

    def stop_monitoring(self) -> bool:
        """Stop monitoring."""
        if not self.is_running:
            return True

        self.logger.info("Stopping device monitoring")
        self.stop_event.set()

        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=10.0)

        self.is_running = False
        return True

    def _monitoring_loop(self) -> None:
        """Monitoring loop."""
        device_scan_interval = 30  # Scan devices every 30 seconds
        full_scan_interval = 300  # Full scan every 5 minutes
        report_interval = 60  # Generate report every 60 seconds

        last_full_scan = 0
        last_report_time = 0

        while not self.stop_event.is_set():
            try:
                # Detect devices
                self.detect_devices()

                current_time = time.time()

                # Check if it's time for a full scan
                if current_time - last_full_scan >= full_scan_interval:
                    self.logger.info("Performing full scan of all devices")

                    # Scan all devices
                    for device_path in self.devices:
                        if self.stop_event.is_set():
                            break
                        self.scan_device(device_path)

                    last_full_scan = current_time

                # Check if it's time to generate a report
                if current_time - last_report_time >= report_interval:
                    self.logger.info("Generating HTML report")
                    self.generate_html_report()
                    last_report_time = current_time

                # Wait for next scan
                for _ in range(device_scan_interval):
                    if self.stop_event.is_set():
                        break
                    time.sleep(1)

            except Exception as e:
                self.logger.error(f"Error during monitoring: {e}")
                # Wait a bit before retrying
                self.stop_event.wait(1.0)

        self.logger.info("Device monitoring stopped")

    def generate_html_report(self) -> None:
        """Generate HTML report."""
        self.logger.info(f"Generating HTML report: {self.report_path}")

        try:
            # Create HTML report
            with open(self.report_path, 'w', encoding='utf-8') as f:
                f.write(self._get_html_report())

            self.logger.info(f"HTML report generated: {self.report_path}")

            # Open report in browser
            report_url = f"file://{os.path.abspath(self.report_path)}"
            self.logger.info(f"Opening report in browser: {report_url}")

            try:
                webbrowser.open(report_url)
            except Exception as e:
                self.logger.error(f"Error opening report in browser: {e}")
                self.logger.info(f"Please open the report manually at: {self.report_path}")

        except Exception as e:
            self.logger.error(f"Error generating HTML report: {e}")

            # Try to create a simpler report in case of error
            try:
                simple_report_path = os.path.join(os.path.dirname(self.report_path), 'simple_report.html')
                with open(simple_report_path, 'w', encoding='utf-8') as f:
                    f.write(f"""<!DOCTYPE html>
<html>
<head>
    <title>SBARDS Simple Report</title>
</head>
<body>
    <h1>SBARDS Device Monitoring Report</h1>
    <p>Generated on: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
    <h2>Detected Devices:</h2>
    <ul>
        {"".join([f"<li>{path} ({device.get('type', 'Unknown')})</li>" for path, device in self.devices.items()])}
    </ul>
    <h2>Suspicious Files:</h2>
    <ul>
        {"".join([f"<li>{file['path']} ({file['extension']})</li>" for file in self.suspicious_files])}
    </ul>
</body>
</html>""")
                self.logger.info(f"Simple report generated: {simple_report_path}")

                try:
                    webbrowser.open(f"file://{os.path.abspath(simple_report_path)}")
                except:
                    pass
            except Exception as e2:
                self.logger.error(f"Error generating simple report: {e2}")

    def _get_html_report(self) -> str:
        """Get HTML report content."""
        # Get current time
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Get devices
        devices = self.get_devices()

        # Get suspicious files
        suspicious_files = self.get_suspicious_files()

        # Create HTML content
        html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SBARDS Device Monitoring Report</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }}
        h1, h2, h3 {{
            color: #2c3e50;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
        }}
        .header {{
            background-color: #3498db;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }}
        .section {{
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }}
        th, td {{
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        th {{
            background-color: #3498db;
            color: white;
        }}
        tr:hover {{
            background-color: #f5f5f5;
        }}
        .alert {{
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }}
        .success {{
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }}
        .progress-bar {{
            height: 20px;
            background-color: #e0e0e0;
            border-radius: 10px;
            margin-bottom: 10px;
        }}
        .progress {{
            height: 100%;
            background-color: #3498db;
            border-radius: 10px;
            text-align: center;
            color: white;
            line-height: 20px;
        }}
        .danger {{
            background-color: #e74c3c;
        }}
        .warning {{
            background-color: #f39c12;
        }}
        .safe {{
            background-color: #2ecc71;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>SBARDS Device Monitoring Report</h1>
            <p>Generated on: {current_time}</p>
        </div>

        <div class="section">
            <h2>Device Summary</h2>
            <p>Total devices detected: {len(devices)}</p>
            <table>
                <tr>
                    <th>Drive</th>
                    <th>Type</th>
                    <th>Label</th>
                    <th>Total Space</th>
                    <th>Free Space</th>
                    <th>Usage</th>
                </tr>
"""

        # Add device rows
        for path, device in devices.items():
            if "total" in device:
                total_gb = device["total"] / (1024**3)
                free_gb = device["free"] / (1024**3)
                percent_used = device["percent_used"]

                # Determine progress bar class
                progress_class = "safe"
                if percent_used > 90:
                    progress_class = "danger"
                elif percent_used > 70:
                    progress_class = "warning"

                html += f"""
                <tr>
                    <td>{path}</td>
                    <td>{device["type"]}</td>
                    <td>{device.get("label", "No Label")}</td>
                    <td>{total_gb:.2f} GB</td>
                    <td>{free_gb:.2f} GB</td>
                    <td>
                        <div class="progress-bar">
                            <div class="progress {progress_class}" style="width: {percent_used}%">
                                {percent_used:.2f}%
                            </div>
                        </div>
                    </td>
                </tr>
"""
            else:
                html += f"""
                <tr>
                    <td>{path}</td>
                    <td>{device["type"]}</td>
                    <td>{device.get("label", "No Label")}</td>
                    <td>N/A</td>
                    <td>N/A</td>
                    <td>N/A</td>
                </tr>
"""

        html += """
            </table>
        </div>
"""

        # Add suspicious files section
        html += f"""
        <div class="section">
            <h2>Suspicious Files</h2>
"""

        if suspicious_files:
            html += f"""
            <div class="alert">
                <strong>Warning!</strong> Found {len(suspicious_files)} suspicious files that may indicate ransomware or malicious activity.
            </div>
            <table>
                <tr>
                    <th>File Path</th>
                    <th>Extension</th>
                    <th>Size</th>
                    <th>Last Modified</th>
                </tr>
"""

            # Add suspicious file rows
            for file in suspicious_files:
                # Format file size
                if file["size"] < 1024:
                    size_str = f"{file['size']} bytes"
                elif file["size"] < 1024**2:
                    size_str = f"{file['size'] / 1024:.2f} KB"
                else:
                    size_str = f"{file['size'] / (1024**2):.2f} MB"

                html += f"""
                <tr>
                    <td>{file["path"]}</td>
                    <td>{file["extension"]}</td>
                    <td>{size_str}</td>
                    <td>{file["modified"]}</td>
                </tr>
"""

            html += """
            </table>
"""
        else:
            html += """
            <div class="success">
                <strong>Good news!</strong> No suspicious files were found.
            </div>
"""

        html += """
        </div>

        <div class="section">
            <h2>Monitoring Information</h2>
            <p>The SBARDS monitoring system is actively scanning your devices for suspicious files and activities.</p>
            <p>If suspicious files are detected, they will be displayed in this report.</p>
            <p>This report is automatically updated periodically.</p>
        </div>
    </div>
</body>
</html>
"""

        return html

    def get_devices(self) -> Dict[str, Dict[str, Any]]:
        """Get detected devices."""
        return self.devices

    def get_suspicious_files(self) -> List[Dict[str, Any]]:
        """Get suspicious files."""
        return self.suspicious_files

def main():
    """Main entry point."""
    logger.info("Starting HTML Device Monitor")

    # Create monitor
    monitor = HTMLDeviceMonitor(config)

    # Generate initial report
    monitor.generate_html_report()

    # Start monitoring
    monitor.start_monitoring()

    try:
        # Keep running until interrupted
        logger.info("Press Ctrl+C to stop monitoring")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Stopping monitoring")
    finally:
        # Stop monitoring
        monitor.stop_monitoring()
        logger.info("Monitoring stopped")

        # Generate final report
        monitor.generate_html_report()

        # Print summary
        devices = monitor.get_devices()
        logger.info(f"Monitored {len(devices)} devices:")
        for path, device in devices.items():
            if "total" in device:
                logger.info(f"  {path} ({device['type']}): {device.get('label', 'No Label')}, "
                           f"{device['total'] / (1024**3):.2f} GB total, "
                           f"{device['free'] / (1024**3):.2f} GB free, "
                           f"{device['percent_used']:.2f}% used")
            else:
                logger.info(f"  {path} ({device['type']}): {device.get('label', 'No Label')}")

        suspicious_files = monitor.get_suspicious_files()
        logger.info(f"Found {len(suspicious_files)} suspicious files:")
        for file in suspicious_files:
            logger.info(f"  {file['path']} ({file['extension']}, {file['size']} bytes, modified: {file['modified']})")

        logger.info(f"HTML report available at: {monitor.report_path}")

if __name__ == "__main__":
    main()
