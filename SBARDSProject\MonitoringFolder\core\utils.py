"""
Utility Functions for SBARDS

This module provides utility functions used across all phases of the SBARDS project.
"""

import os
import sys
import platform
import subprocess
from typing import List, Dict, Any, Optional, Tuple

def get_platform() -> str:
    """
    Get the current platform.
    
    Returns:
        str: Platform name (windows, linux, darwin)
    """
    return platform.system().lower()

def is_windows() -> bool:
    """
    Check if the current platform is Windows.
    
    Returns:
        bool: True if Windows, False otherwise
    """
    return platform.system().lower() == "windows"

def is_linux() -> bool:
    """
    Check if the current platform is Linux.
    
    Returns:
        bool: True if Linux, False otherwise
    """
    return platform.system().lower() == "linux"

def is_macos() -> bool:
    """
    Check if the current platform is macOS.
    
    Returns:
        bool: True if macOS, False otherwise
    """
    return platform.system().lower() == "darwin"

def run_command(command: List[str], timeout: int = 30) -> <PERSON>ple[int, str, str]:
    """
    Run a command and return the result.
    
    Args:
        command (List[str]): Command to run
        timeout (int): Timeout in seconds
        
    Returns:
        Tuple[int, str, str]: Return code, stdout, stderr
    """
    try:
        process = subprocess.run(
            command,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        
        return process.returncode, process.stdout, process.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Command timed out"
    except Exception as e:
        return -1, "", str(e)

def check_program_installed(program: str) -> bool:
    """
    Check if a program is installed.
    
    Args:
        program (str): Program name
        
    Returns:
        bool: True if installed, False otherwise
    """
    if is_windows():
        # Check using where command
        result = run_command(["where", program])
        return result[0] == 0
    else:
        # Check using which command
        result = run_command(["which", program])
        return result[0] == 0

def get_file_extension(file_path: str) -> str:
    """
    Get the file extension.
    
    Args:
        file_path (str): Path to the file
        
    Returns:
        str: File extension
    """
    _, ext = os.path.splitext(file_path)
    return ext.lower()

def calculate_file_hash(file_path: str, algorithm: str = "sha256") -> Optional[str]:
    """
    Calculate file hash.
    
    Args:
        file_path (str): Path to the file
        algorithm (str): Hash algorithm (md5, sha1, sha256)
        
    Returns:
        Optional[str]: File hash or None if error
    """
    import hashlib
    
    algorithms = {
        "md5": hashlib.md5,
        "sha1": hashlib.sha1,
        "sha256": hashlib.sha256
    }
    
    if algorithm.lower() not in algorithms:
        return None
    
    try:
        hash_obj = algorithms[algorithm.lower()]()
        
        with open(file_path, "rb") as f:
            # Read in chunks to handle large files
            for chunk in iter(lambda: f.read(4096), b""):
                hash_obj.update(chunk)
                
        return hash_obj.hexdigest()
    except Exception:
        return None
