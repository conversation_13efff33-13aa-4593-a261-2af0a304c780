"""
Monitor Manager for SBARDS

This module provides the main coordination point for all monitoring activities.
"""

import os
import time
import logging
import threading
import platform
from typing import Dict, List, Any, Optional

class MonitorManager:
    """
    Main coordinator for all monitoring activities in the SBARDS project.

    This class manages the lifecycle of all monitoring components and provides
    a unified interface for starting, stopping, and querying monitoring activities.
    """

    def __init__(self, config: Dict[str, Any], scanner=None):
        """
        Initialize the monitor manager.

        Args:
            config (Dict[str, Any]): Configuration dictionary
            scanner: Optional scanner instance for integration with pre-inspection layer
        """
        self.config = config
        self.scanner = scanner
        self.logger = logging.getLogger("SBARDS.MonitorManager")

        # Get monitoring configuration
        self.monitoring_config = config.get("monitoring", {})
        self.enabled = self.monitoring_config.get("enabled", False)

        # Initialize components
        self.process_monitor = None
        self.filesystem_monitor = None
        self.network_monitor = None
        self.event_correlator = None
        self.alert_manager = None
        self.response_manager = None

        # Component threads
        self.threads = []

        # Monitoring state
        self.is_running = False
        self.stop_event = threading.Event()

        # Initialize components if monitoring is enabled
        if self.enabled:
            self._initialize_components()

        self.logger.info(f"Monitor Manager initialized on {platform.system()} platform")

    def _initialize_components(self):
        """Initialize all monitoring components based on configuration."""
        from .process_monitor import ProcessMonitor
        from .filesystem_monitor import FilesystemMonitor
        from .network_monitor import NetworkMonitor
        from .event_correlator import EventCorrelator
        from .alert_manager import AlertManager
        from .response_manager import ResponseManager

        # Initialize alert manager first so other components can use it
        alert_config = self.monitoring_config.get("alert", {})
        self.alert_manager = AlertManager(alert_config)

        # Initialize monitoring components if enabled
        if self.monitoring_config.get("process_monitoring", {}).get("enabled", False):
            process_config = self.monitoring_config.get("process_monitoring", {})
            self.process_monitor = ProcessMonitor(process_config, self.alert_manager)
            self.logger.info("Process monitoring initialized")

        if self.monitoring_config.get("filesystem_monitoring", {}).get("enabled", False):
            filesystem_config = self.monitoring_config.get("filesystem_monitoring", {})
            self.filesystem_monitor = FilesystemMonitor(filesystem_config, self.alert_manager)
            self.logger.info("Filesystem monitoring initialized")

        if self.monitoring_config.get("network_monitoring", {}).get("enabled", False):
            network_config = self.monitoring_config.get("network_monitoring", {})
            self.network_monitor = NetworkMonitor(network_config, self.alert_manager)
            self.logger.info("Network monitoring initialized")

        # Initialize event correlator with references to all monitors
        self.event_correlator = EventCorrelator(
            self.monitoring_config,
            self.alert_manager,
            process_monitor=self.process_monitor,
            filesystem_monitor=self.filesystem_monitor,
            network_monitor=self.network_monitor
        )
        self.logger.info("Event correlator initialized")

        # Initialize response manager
        response_config = self.monitoring_config.get("response", {})
        self.response_manager = ResponseManager(response_config, self.alert_manager)
        self.logger.info("Response manager initialized")

        # Connect alert manager to response manager for automatic responses
        if self.alert_manager and self.response_manager:
            # This would normally be done through a callback or event system
            # For simplicity, we'll just store a reference to the response manager in the alert manager
            setattr(self.alert_manager, "_response_manager", self.response_manager)
            self.logger.info("Alert manager connected to response manager")

    def start_monitoring(self):
        """Start all monitoring components."""
        if not self.enabled:
            self.logger.warning("Monitoring is not enabled in configuration")
            return False

        if self.is_running:
            self.logger.warning("Monitoring is already running")
            return True

        self.logger.info("Starting monitoring components")
        self.stop_event.clear()

        # Start alert manager
        if self.alert_manager:
            self.alert_manager.start()

        # Start monitoring components
        if self.process_monitor:
            thread = threading.Thread(
                target=self.process_monitor.start_monitoring,
                args=(self.stop_event,),
                daemon=True
            )
            thread.start()
            self.threads.append(thread)

        if self.filesystem_monitor:
            thread = threading.Thread(
                target=self.filesystem_monitor.start_monitoring,
                args=(self.stop_event,),
                daemon=True
            )
            thread.start()
            self.threads.append(thread)

        if self.network_monitor:
            thread = threading.Thread(
                target=self.network_monitor.start_monitoring,
                args=(self.stop_event,),
                daemon=True
            )
            thread.start()
            self.threads.append(thread)

        # Start event correlator
        if self.event_correlator:
            thread = threading.Thread(
                target=self.event_correlator.start_correlation,
                args=(self.stop_event,),
                daemon=True
            )
            thread.start()
            self.threads.append(thread)

        self.is_running = True
        self.logger.info("All monitoring components started")
        return True

    def stop_monitoring(self):
        """Stop all monitoring components."""
        if not self.is_running:
            self.logger.warning("Monitoring is not running")
            return True

        self.logger.info("Stopping monitoring components")
        self.stop_event.set()

        # Wait for all threads to complete
        for thread in self.threads:
            thread.join(timeout=5.0)

        self.threads = []

        # Stop alert manager last
        if self.alert_manager:
            self.alert_manager.stop()

        self.is_running = False
        self.logger.info("All monitoring components stopped")
        return True

    def get_status(self) -> Dict[str, Any]:
        """
        Get the current status of all monitoring components.

        Returns:
            Dict[str, Any]: Status information for all components
        """
        status = {
            "is_running": self.is_running,
            "components": {
                "process_monitor": self.process_monitor is not None and self.is_running,
                "filesystem_monitor": self.filesystem_monitor is not None and self.is_running,
                "network_monitor": self.network_monitor is not None and self.is_running,
                "event_correlator": self.event_correlator is not None and self.is_running,
                "alert_manager": self.alert_manager is not None,
                "response_manager": self.response_manager is not None
            }
        }

        # Add additional status information if available
        if self.alert_manager:
            recent_alerts = self.alert_manager.get_recent_alerts(5)
            status["recent_alerts"] = recent_alerts

        if self.response_manager:
            recent_responses = self.response_manager.get_response_history(5)
            status["recent_responses"] = recent_responses

        return status
