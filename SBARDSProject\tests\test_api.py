"""
Tests for the API module.
"""

import os
import json
import unittest
from fastapi.testclient import TestClient
from api.main import app

class TestAPI(unittest.TestCase):
    """Tests for the API module."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create test client
        self.client = TestClient(app)
    
    def test_root_endpoint(self):
        """Test the root endpoint."""
        # Make request
        response = self.client.get("/")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["message"], "Welcome to SBARDS API")
    
    def test_health_endpoint(self):
        """Test the health endpoint."""
        # Make request
        response = self.client.get("/health")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["status"], "ok")
    
    def test_system_info_endpoint(self):
        """Test the system info endpoint."""
        # Make request
        response = self.client.get("/api/system/info")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertIn("platform", response.json())
        self.assertIn("cpu_count", response.json())
        self.assertIn("memory_total", response.json())
        self.assertIn("disk_usage", response.json())
    
    def test_system_processes_endpoint(self):
        """Test the system processes endpoint."""
        # Make request
        response = self.client.get("/api/system/processes")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response.json(), list)
        
        # Check if there are processes
        if response.json():
            process = response.json()[0]
            self.assertIn("pid", process)
            self.assertIn("name", process)
            self.assertIn("cpu_percent", process)
            self.assertIn("memory_percent", process)
    
    def test_system_memory_endpoint(self):
        """Test the system memory endpoint."""
        # Make request
        response = self.client.get("/api/system/memory")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertIn("total", response.json())
        self.assertIn("available", response.json())
        self.assertIn("used", response.json())
        self.assertIn("percent", response.json())
    
    def test_system_cpu_endpoint(self):
        """Test the system CPU endpoint."""
        # Make request
        response = self.client.get("/api/system/cpu")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertIn("count", response.json())
        self.assertIn("percent", response.json())
        self.assertIn("average", response.json())
    
    def test_system_disk_endpoint(self):
        """Test the system disk endpoint."""
        # Make request
        response = self.client.get("/api/system/disk")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response.json(), dict)
        
        # Check if there are disks
        if response.json():
            disk = next(iter(response.json().values()))
            self.assertIn("total", disk)
            self.assertIn("used", disk)
            self.assertIn("free", disk)
            self.assertIn("percent", disk)

if __name__ == "__main__":
    unittest.main()
