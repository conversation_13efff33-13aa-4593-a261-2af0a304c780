#include <iostream>
#include <fstream>
#include <string>
#include <algorithm>
#include <locale>
#include <codecvt>
#include <vector>

#ifdef _WIN32
#include <windows.h>
#include <fcntl.h>
#include <io.h>
#endif

// This is a simplified mock version of the YARA scanner for demonstration purposes
// It simulates scanning a file for specific patterns without requiring the YARA library

// Set up console for Unicode output
void setup_console() {
#ifdef _WIN32
    // Set console code page to UTF-8
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);

    // Set stdin/stdout to binary mode to avoid Windows line ending conversions
    _setmode(_fileno(stdin), _O_BINARY);
    _setmode(_fileno(stdout), _O_BINARY);
    _setmode(_fileno(stderr), _O_BINARY);
#endif
}

// Convert UTF-8 to wide string (for Windows file operations)
std::wstring utf8_to_wstring(const std::string& str) {
    std::wstring_convert<std::codecvt_utf8_utf16<wchar_t>> converter;
    return converter.from_bytes(str);
}

// Convert wide string to UTF-8 (for output)
std::string wstring_to_utf8(const std::wstring& wstr) {
    std::wstring_convert<std::codecvt_utf8_utf16<wchar_t>> converter;
    return converter.to_bytes(wstr);
}

// Case-insensitive string search for cross-platform consistency
bool contains_pattern(const std::string& content, const std::string& pattern) {
    std::string content_lower = content;
    std::string pattern_lower = pattern;

    // Convert to lowercase for case-insensitive comparison
    std::transform(content_lower.begin(), content_lower.end(), content_lower.begin(),
                  [](unsigned char c){ return std::tolower(c); });
    std::transform(pattern_lower.begin(), pattern_lower.end(), pattern_lower.begin(),
                  [](unsigned char c){ return std::tolower(c); });

    return content_lower.find(pattern_lower) != std::string::npos;
}

// Read file with Unicode support
bool read_file(const std::string& file_path, std::string& content) {
#ifdef _WIN32
    // On Windows, use wide strings for file paths
    std::wstring wide_path = utf8_to_wstring(file_path);
    std::ifstream file(wide_path, std::ios::binary);
#else
    // On other platforms, use UTF-8 directly
    std::ifstream file(file_path, std::ios::binary);
#endif

    if (!file.is_open()) {
        std::cerr << "Could not open target file: " << file_path << "\n";
        return false;
    }

    // Read the entire file into a buffer
    file.seekg(0, std::ios::end);
    size_t size = file.tellg();
    file.seekg(0, std::ios::beg);

    std::vector<char> buffer(size);
    if (size > 0) {
        file.read(buffer.data(), size);
    }
    file.close();

    // Convert to string
    content.assign(buffer.begin(), buffer.end());
    return true;
}

int main(int argc, char* argv[]) {
    // Set up console for Unicode output
    setup_console();

    if (argc < 3) {
        std::cerr << "Usage: " << (argc > 0 ? argv[0] : "mock_scanner") << " <rules_file> <target_file>\n";
        return 1;
    }

    const char* rules_file = argv[1];
    const char* target_file = argv[2];

    std::cout << "Mock YARA Scanner (Unicode-enabled)\n";
    std::cout << "Rules file: " << rules_file << "\n";
    std::cout << "Target file: " << target_file << "\n";

    // Read the target file with Unicode support
    std::string content;
    if (!read_file(target_file, content)) {
        return 1;
    }

    // Simulate rule matching
    bool matched_malicious = contains_pattern(content, "malicious");
    bool matched_encrypt = contains_pattern(content, "encrypt");
    bool matched_decrypt = contains_pattern(content, "decrypt");
    bool matched_ransom = contains_pattern(content, "ransom");
    bool matched_bitcoin = contains_pattern(content, "bitcoin");
    bool matched_payment = contains_pattern(content, "payment");

    if (matched_malicious) {
        std::cout << "Matched rule: ExampleRule (found 'malicious')\n";
    }

    if (matched_encrypt && matched_decrypt) {
        std::cout << "Matched rule: PotentialRansomware (found 'encrypt' and 'decrypt')\n";
    }

    if (matched_ransom || (matched_bitcoin && matched_payment)) {
        std::cout << "Matched rule: Ransomware_Generic (found ransomware indicators)\n";
    }

    return 0;
}
