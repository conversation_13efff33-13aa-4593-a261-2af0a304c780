"""
Security utilities for the SBARDS Backend API.

This module provides security utilities for the SBARDS Backend API,
including API key validation, JWT authentication, and request rate limiting.
"""

import hashlib
import secrets
import time
import jwt
from datetime import datetime, timedelta, timezone
from typing import Dict, Optional, Any, Union

from fastapi import Depends, HTTPException, Request, status, Security
from fastapi.security import APIKeyHeader, APIKeyQuery, OAuth2PasswordBearer
from fastapi.security.api_key import APIKey
from pydantic import ValidationError

from .config import settings
from .logging import logger


# API key security schemes
API_KEY_NAME = "X-API-Key"
API_KEY_HEADER = APIKeyHeader(name=API_KEY_NAME, auto_error=False)
api_key_query = APIKeyQuery(name="api_key", auto_error=False)

# OAuth2 scheme for token authentication
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/token", auto_error=False)

# Rate limiting
RATE_LIMIT_DURATION = 60  # 1 minute
RATE_LIMIT_REQUESTS = 100  # 100 requests per minute
rate_limit_store: Dict[str, Dict[str, int]] = {}


async def get_api_key(
    api_key_header: str = Security(API_KEY_HEADER),
    api_key_query: str = Security(api_key_query),
) -> str:
    """
    Get API key from header or query parameter.

    Args:
        api_key_header (str): API key from header.
        api_key_query (str): API key from query parameter.

    Returns:
        str: API key.

    Raises:
        HTTPException: If API key is invalid or missing.
    """
    if api_key_header:
        return api_key_header
    if api_key_query:
        return api_key_query

    # For development, allow requests without API key
    # In production, this should be more restrictive
    if not settings.API_KEY:
        logger.warning("No API key configured, allowing all requests")
        return "development_key"

    raise HTTPException(
        status_code=status.HTTP_403_FORBIDDEN,
        detail="Could not validate API key"
    )


async def verify_api_key(api_key: str = Depends(get_api_key)) -> str:
    """
    Verify API key.

    Args:
        api_key (str): API key.

    Returns:
        str: API key if valid.

    Raises:
        HTTPException: If API key is invalid.
    """
    # In a real application, you would check the API key against a database
    # For this example, we'll use a simple environment variable
    valid_api_key = settings.API_KEY

    if not valid_api_key:
        # If no API key is configured, allow all requests (for development)
        logger.warning("No API key configured, allowing all requests")
        return api_key

    if api_key == valid_api_key:
        return api_key

    raise HTTPException(
        status_code=status.HTTP_403_FORBIDDEN,
        detail="Invalid API key"
    )


async def rate_limit(request: Request, api_key: str = Depends(get_api_key)) -> None:
    """
    Rate limit requests based on client IP or API key.

    Args:
        request (Request): FastAPI request object.
        api_key (Optional[str]): API key from header.

    Raises:
        HTTPException: If rate limit is exceeded.
    """
    # Use API key if available, otherwise use client IP
    client_id = api_key or request.client.host

    # Get current timestamp
    now = int(time.time())

    # Initialize rate limit entry if not exists
    if client_id not in rate_limit_store:
        rate_limit_store[client_id] = {"count": 0, "reset_at": now + RATE_LIMIT_DURATION}

    # Reset count if duration has passed
    if rate_limit_store[client_id]["reset_at"] <= now:
        rate_limit_store[client_id] = {"count": 0, "reset_at": now + RATE_LIMIT_DURATION}

    # Increment count
    rate_limit_store[client_id]["count"] += 1

    # Check if rate limit is exceeded
    if rate_limit_store[client_id]["count"] > RATE_LIMIT_REQUESTS:
        logger.warning(f"Rate limit exceeded for client: {client_id}")
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Rate limit exceeded. Please try again later."
        )


def calculate_file_hash(file_content: bytes) -> str:
    """
    Calculate SHA-256 hash of file content.

    Args:
        file_content (bytes): File content.

    Returns:
        str: SHA-256 hash of file content.
    """
    sha256_hash = hashlib.sha256()
    sha256_hash.update(file_content)
    return sha256_hash.hexdigest()


def generate_scan_id() -> str:
    """
    Generate a unique scan ID.

    Returns:
        str: Unique scan ID.
    """
    timestamp = int(time.time())
    random_part = secrets.token_hex(4)
    return f"scan_{timestamp}_{random_part}"


def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """
    Create JWT access token.

    Args:
        data (Dict[str, Any]): Token data.
        expires_delta (Optional[timedelta]): Token expiration time.

    Returns:
        str: JWT token.
    """
    to_encode = data.copy()

    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm="HS256")

    return encoded_jwt


async def verify_token(token: str = Depends(oauth2_scheme)) -> Dict[str, Any]:
    """
    Verify JWT token.

    Args:
        token (str): JWT token.

    Returns:
        Dict[str, Any]: Token payload.

    Raises:
        HTTPException: If token is invalid.
    """
    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated",
            headers={"WWW-Authenticate": "Bearer"},
        )

    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
        return payload
    except jwt.PyJWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def verify_websocket_token(token: str) -> Dict[str, Any]:
    """
    Verify WebSocket token.

    Args:
        token (str): JWT token.

    Returns:
        Dict[str, Any]: Token payload.

    Raises:
        Exception: If token is invalid.
    """
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
        return payload
    except jwt.PyJWTError as e:
        raise Exception(f"Invalid token: {e}")
