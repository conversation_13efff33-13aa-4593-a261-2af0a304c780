"""
Filesystem Monitor for SBARDS

This module provides filesystem monitoring for the monitoring phase of the SBARDS project.
"""

import os
import time
import logging
import threading
from typing import Dict, List, Any, Optional, Set

class FilesystemMonitor:
    """
    Filesystem monitor for the monitoring phase.
    
    This class provides mechanisms for:
    1. Monitoring filesystem activity
    2. Detecting suspicious file operations
    3. Tracking file changes
    4. Monitoring file permissions
    """
    
    def __init__(self, config: Dict[str, Any], alert_manager):
        """
        Initialize filesystem monitor.
        
        Args:
            config (Dict[str, Any]): Filesystem monitoring configuration
            alert_manager: Alert manager
        """
        self.config = config
        self.enabled = config.get("enabled", True)
        self.watch_directories = config.get("watch_directories", [])
        self.detect_mass_operations = config.get("detect_mass_operations", True)
        self.mass_operation_threshold = config.get("mass_operation_threshold", 5)
        self.mass_operation_time_window_seconds = config.get("mass_operation_time_window_seconds", 10)
        self.entropy_check = config.get("entropy_check", True)
        self.entropy_threshold = config.get("entropy_threshold", 7.5)
        self.suspicious_extensions = config.get("suspicious_extensions", [])
        self.monitor_file_permissions = config.get("monitor_file_permissions", True)
        
        # Set up logging
        self.logger = logging.getLogger("FilesystemMonitor")
        
        # Set alert manager
        self.alert_manager = alert_manager
        
        # Initialize file operation storage
        self.file_operations = []
        self.monitored_files = set()
        self.operation_count = 0
        
        # Initialize monitoring state
        self.is_running_flag = False
        self.stop_event = threading.Event()
        self.monitoring_thread = None
        self.observer = None
    
    def start(self) -> bool:
        """
        Start filesystem monitoring.
        
        Returns:
            bool: True if started successfully, False otherwise
        """
        if not self.enabled:
            self.logger.warning("Filesystem monitoring is not enabled in configuration")
            return False
        
        if self.is_running_flag:
            self.logger.warning("Filesystem monitoring is already running")
            return True
        
        self.logger.info("Starting filesystem monitoring")
        
        try:
            # Initialize watchdog observer
            try:
                from watchdog.observers import Observer
                from watchdog.events import FileSystemEventHandler
                
                class EventHandler(FileSystemEventHandler):
                    def __init__(self, monitor):
                        self.monitor = monitor
                    
                    def on_created(self, event):
                        self.monitor._handle_file_created(event.src_path)
                    
                    def on_deleted(self, event):
                        self.monitor._handle_file_deleted(event.src_path)
                    
                    def on_modified(self, event):
                        self.monitor._handle_file_modified(event.src_path)
                    
                    def on_moved(self, event):
                        self.monitor._handle_file_moved(event.src_path, event.dest_path)
                
                self.observer = Observer()
                event_handler = EventHandler(self)
                
                # Add watch directories
                for directory in self.watch_directories:
                    if os.path.exists(directory) and os.path.isdir(directory):
                        self.observer.schedule(event_handler, directory, recursive=True)
                        self.logger.info(f"Watching directory: {directory}")
                    else:
                        self.logger.warning(f"Directory not found: {directory}")
                
                # Start observer
                self.observer.start()
                
            except ImportError:
                self.logger.warning("watchdog module not found. Using fallback monitoring method.")
                
                # Reset stop event
                self.stop_event.clear()
                
                # Start monitoring thread
                self.monitoring_thread = threading.Thread(
                    target=self._monitoring_loop,
                    daemon=True
                )
                self.monitoring_thread.start()
            
            self.is_running_flag = True
            self.logger.info("Filesystem monitoring started")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error starting filesystem monitoring: {e}")
            return False
    
    def stop(self) -> bool:
        """
        Stop filesystem monitoring.
        
        Returns:
            bool: True if stopped successfully, False otherwise
        """
        if not self.is_running_flag:
            self.logger.warning("Filesystem monitoring is not running")
            return True
        
        self.logger.info("Stopping filesystem monitoring")
        
        try:
            # Stop observer
            if self.observer:
                self.observer.stop()
                self.observer.join()
                self.observer = None
            
            # Set stop event
            self.stop_event.set()
            
            # Wait for monitoring thread to stop
            if self.monitoring_thread:
                self.monitoring_thread.join(timeout=5.0)
            
            self.is_running_flag = False
            self.logger.info("Filesystem monitoring stopped")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error stopping filesystem monitoring: {e}")
            return False
    
    def is_running(self) -> bool:
        """
        Check if filesystem monitoring is running.
        
        Returns:
            bool: True if running, False otherwise
        """
        return self.is_running_flag
    
    def _monitoring_loop(self) -> None:
        """Filesystem monitoring loop."""
        # This is a fallback monitoring method when watchdog is not available
        # It periodically scans the watch directories for changes
        
        # Initialize file state
        file_state = {}
        
        # Initialize scan interval
        scan_interval = 5.0
        
        while not self.stop_event.is_set():
            try:
                # Scan watch directories
                new_file_state = {}
                
                for directory in self.watch_directories:
                    if os.path.exists(directory) and os.path.isdir(directory):
                        for root, _, files in os.walk(directory):
                            for file in files:
                                file_path = os.path.join(root, file)
                                try:
                                    # Get file stats
                                    stats = os.stat(file_path)
                                    new_file_state[file_path] = {
                                        "mtime": stats.st_mtime,
                                        "size": stats.st_size
                                    }
                                    
                                    # Check if file is new or modified
                                    if file_path not in file_state:
                                        self._handle_file_created(file_path)
                                    elif (
                                        file_state[file_path]["mtime"] != stats.st_mtime or
                                        file_state[file_path]["size"] != stats.st_size
                                    ):
                                        self._handle_file_modified(file_path)
                                except Exception as e:
                                    self.logger.debug(f"Error checking file {file_path}: {e}")
                
                # Check for deleted files
                for file_path in file_state:
                    if file_path not in new_file_state:
                        self._handle_file_deleted(file_path)
                
                # Update file state
                file_state = new_file_state
                
                # Wait for next scan
                self.stop_event.wait(scan_interval)
                
            except Exception as e:
                self.logger.error(f"Error in filesystem monitoring loop: {e}")
                # Wait a bit before retrying
                self.stop_event.wait(1.0)
    
    def _handle_file_created(self, file_path: str) -> None:
        """
        Handle file created event.
        
        Args:
            file_path (str): File path
        """
        # Add to file operations
        operation = {
            "id": str(len(self.file_operations) + 1),
            "timestamp": time.time(),
            "operation": "created",
            "path": file_path
        }
        
        self.file_operations.append(operation)
        self.operation_count += 1
        
        # Keep only the last 1000 operations
        if len(self.file_operations) > 1000:
            self.file_operations.pop(0)
        
        # Check for suspicious extensions
        _, ext = os.path.splitext(file_path)
        if ext.lower() in self.suspicious_extensions:
            # Create alert
            self.alert_manager.add_alert(
                level="warning",
                source="filesystem_monitor",
                message=f"Suspicious file created: {file_path}",
                details={
                    "path": file_path,
                    "operation": "created",
                    "extension": ext.lower()
                }
            )
        
        # Check for mass operations
        if self.detect_mass_operations:
            self._check_mass_operations("created")
    
    def _handle_file_deleted(self, file_path: str) -> None:
        """
        Handle file deleted event.
        
        Args:
            file_path (str): File path
        """
        # Add to file operations
        operation = {
            "id": str(len(self.file_operations) + 1),
            "timestamp": time.time(),
            "operation": "deleted",
            "path": file_path
        }
        
        self.file_operations.append(operation)
        self.operation_count += 1
        
        # Keep only the last 1000 operations
        if len(self.file_operations) > 1000:
            self.file_operations.pop(0)
        
        # Check for mass operations
        if self.detect_mass_operations:
            self._check_mass_operations("deleted")
    
    def _handle_file_modified(self, file_path: str) -> None:
        """
        Handle file modified event.
        
        Args:
            file_path (str): File path
        """
        # Add to file operations
        operation = {
            "id": str(len(self.file_operations) + 1),
            "timestamp": time.time(),
            "operation": "modified",
            "path": file_path
        }
        
        self.file_operations.append(operation)
        self.operation_count += 1
        
        # Keep only the last 1000 operations
        if len(self.file_operations) > 1000:
            self.file_operations.pop(0)
        
        # Check for monitored files
        if file_path in self.monitored_files:
            # Create alert
            self.alert_manager.add_alert(
                level="info",
                source="filesystem_monitor",
                message=f"Monitored file modified: {file_path}",
                details={
                    "path": file_path,
                    "operation": "modified"
                }
            )
        
        # Check for mass operations
        if self.detect_mass_operations:
            self._check_mass_operations("modified")
    
    def _handle_file_moved(self, src_path: str, dest_path: str) -> None:
        """
        Handle file moved event.
        
        Args:
            src_path (str): Source path
            dest_path (str): Destination path
        """
        # Add to file operations
        operation = {
            "id": str(len(self.file_operations) + 1),
            "timestamp": time.time(),
            "operation": "moved",
            "path": src_path,
            "dest_path": dest_path
        }
        
        self.file_operations.append(operation)
        self.operation_count += 1
        
        # Keep only the last 1000 operations
        if len(self.file_operations) > 1000:
            self.file_operations.pop(0)
        
        # Check for suspicious extensions
        _, ext = os.path.splitext(dest_path)
        if ext.lower() in self.suspicious_extensions:
            # Create alert
            self.alert_manager.add_alert(
                level="warning",
                source="filesystem_monitor",
                message=f"File moved to suspicious extension: {dest_path}",
                details={
                    "src_path": src_path,
                    "dest_path": dest_path,
                    "operation": "moved",
                    "extension": ext.lower()
                }
            )
        
        # Check for mass operations
        if self.detect_mass_operations:
            self._check_mass_operations("moved")
    
    def _check_mass_operations(self, operation_type: str) -> None:
        """
        Check for mass file operations.
        
        Args:
            operation_type (str): Operation type
        """
        # Get recent operations
        now = time.time()
        window_start = now - self.mass_operation_time_window_seconds
        
        # Count operations of the specified type in the time window
        recent_operations = [
            op for op in self.file_operations
            if op["operation"] == operation_type and op["timestamp"] >= window_start
        ]
        
        # Check if threshold is exceeded
        if len(recent_operations) >= self.mass_operation_threshold:
            # Create alert
            self.alert_manager.add_alert(
                level="warning",
                source="filesystem_monitor",
                message=f"Mass file {operation_type} operations detected: {len(recent_operations)} files in {self.mass_operation_time_window_seconds} seconds",
                details={
                    "operation": operation_type,
                    "count": len(recent_operations),
                    "time_window_seconds": self.mass_operation_time_window_seconds,
                    "recent_files": [op["path"] for op in recent_operations[:10]]
                }
            )
    
    def get_recent_operations(self, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Get recent file operations.
        
        Args:
            limit (int, optional): Maximum number of operations to return
            
        Returns:
            List[Dict[str, Any]]: Recent file operations
        """
        # Sort operations by timestamp (newest first)
        sorted_operations = sorted(self.file_operations, key=lambda op: op["timestamp"], reverse=True)
        
        # Return limited number of operations
        return sorted_operations[:limit]
    
    def get_operation_count(self) -> int:
        """
        Get operation count.
        
        Returns:
            int: Operation count
        """
        return self.operation_count
    
    def monitor_file(self, file_path: str) -> None:
        """
        Monitor a specific file.
        
        Args:
            file_path (str): File path
        """
        if not self.is_running_flag:
            self.logger.warning("Filesystem monitoring is not running")
            return
        
        # Add to monitored files
        self.monitored_files.add(file_path)
        
        self.logger.info(f"Monitoring file: {file_path}")
        
        # Check if file exists
        if os.path.exists(file_path) and os.path.isfile(file_path):
            self.logger.info(f"File {file_path} is being monitored")
        else:
            self.logger.warning(f"File not found: {file_path}")
