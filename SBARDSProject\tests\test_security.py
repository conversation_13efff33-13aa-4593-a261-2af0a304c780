"""
Tests for the security utilities.
"""

import os
import sys
import json
import unittest
from typing import Dict, Any

# Add parent directory to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from utils.security import (
    validate_path, validate_filename, is_dangerous_file,
    sanitize_path, sanitize_filename, validate_email,
    validate_ip_address, validate_url, validate_json_input
)

class TestSecurity(unittest.TestCase):
    """Tests for the security utilities."""
    
    def test_validate_path(self):
        """Test path validation."""
        # Valid paths
        self.assertTrue(validate_path("path/to/file.txt"))
        self.assertTrue(validate_path("C:\\path\\to\\file.txt"))
        self.assertTrue(validate_path("/path/to/file.txt"))
        
        # Invalid paths
        self.assertFalse(validate_path(""))
        self.assertFalse(validate_path(None))
        self.assertFalse(validate_path("path/with/../traversal"))
        self.assertFalse(validate_path("path/with/invalid/chars/!@#$%^&*()"))
        
    def test_validate_filename(self):
        """Test filename validation."""
        # Valid filenames
        self.assertTrue(validate_filename("file.txt"))
        self.assertTrue(validate_filename("file_name.txt"))
        self.assertTrue(validate_filename("file-name.txt"))
        
        # Invalid filenames
        self.assertFalse(validate_filename(""))
        self.assertFalse(validate_filename(None))
        self.assertFalse(validate_filename("file/name.txt"))
        self.assertFalse(validate_filename("file\\name.txt"))
        self.assertFalse(validate_filename("file name.txt"))
        self.assertFalse(validate_filename("file!@#$%^&*().txt"))
        
    def test_is_dangerous_file(self):
        """Test dangerous file detection."""
        # Dangerous files
        self.assertTrue(is_dangerous_file("file.exe"))
        self.assertTrue(is_dangerous_file("file.dll"))
        self.assertTrue(is_dangerous_file("file.bat"))
        self.assertTrue(is_dangerous_file("file.ps1"))
        self.assertTrue(is_dangerous_file("file.js"))
        
        # Safe files
        self.assertFalse(is_dangerous_file("file.txt"))
        self.assertFalse(is_dangerous_file("file.jpg"))
        self.assertFalse(is_dangerous_file("file.png"))
        self.assertFalse(is_dangerous_file("file.pdf"))
        
        # Edge cases
        self.assertFalse(is_dangerous_file(""))
        self.assertFalse(is_dangerous_file(None))
        
    def test_sanitize_path(self):
        """Test path sanitization."""
        # Path with traversal
        self.assertEqual(sanitize_path("path/with/../traversal"), "path/traversal")
        
        # Path with multiple traversals
        self.assertEqual(sanitize_path("path/with/../../traversal"), "traversal")
        
        # Path with absolute reference
        self.assertEqual(sanitize_path("/absolute/path"), "absolute/path")
        
        # Edge cases
        self.assertEqual(sanitize_path(""), "")
        self.assertEqual(sanitize_path(None), "")
        
    def test_sanitize_filename(self):
        """Test filename sanitization."""
        # Filename with spaces
        self.assertEqual(sanitize_filename("file name.txt"), "file_name.txt")
        
        # Filename with special characters
        self.assertEqual(sanitize_filename("file!@#$%^&*().txt"), "file_________.txt")
        
        # Edge cases
        self.assertEqual(sanitize_filename(""), "")
        self.assertEqual(sanitize_filename(None), "")
        
    def test_validate_email(self):
        """Test email validation."""
        # Valid emails
        self.assertTrue(validate_email("<EMAIL>"))
        self.assertTrue(validate_email("<EMAIL>"))
        self.assertTrue(validate_email("<EMAIL>"))
        self.assertTrue(validate_email("<EMAIL>"))
        
        # Invalid emails
        self.assertFalse(validate_email(""))
        self.assertFalse(validate_email(None))
        self.assertFalse(validate_email("user"))
        self.assertFalse(validate_email("user@"))
        self.assertFalse(validate_email("@example.com"))
        self.assertFalse(validate_email("user@example"))
        self.assertFalse(validate_email("user@.com"))
        
    def test_validate_ip_address(self):
        """Test IP address validation."""
        # Valid IP addresses
        self.assertTrue(validate_ip_address("***********"))
        self.assertTrue(validate_ip_address("********"))
        self.assertTrue(validate_ip_address("**********"))
        self.assertTrue(validate_ip_address("127.0.0.1"))
        
        # Invalid IP addresses
        self.assertFalse(validate_ip_address(""))
        self.assertFalse(validate_ip_address(None))
        self.assertFalse(validate_ip_address("192.168.1"))
        self.assertFalse(validate_ip_address("***********.1"))
        self.assertFalse(validate_ip_address("192.168.1.256"))
        self.assertFalse(validate_ip_address("192.168.1.-1"))
        self.assertFalse(validate_ip_address("192.168.1.a"))
        
    def test_validate_url(self):
        """Test URL validation."""
        # Valid URLs
        self.assertTrue(validate_url("http://example.com"))
        self.assertTrue(validate_url("https://example.com"))
        self.assertTrue(validate_url("http://subdomain.example.com"))
        self.assertTrue(validate_url("http://example.com/path"))
        self.assertTrue(validate_url("http://example.com/path?query=value"))
        
        # Invalid URLs
        self.assertFalse(validate_url(""))
        self.assertFalse(validate_url(None))
        self.assertFalse(validate_url("example.com"))
        self.assertFalse(validate_url("ftp://example.com"))
        
    def test_validate_json_input(self):
        """Test JSON input validation."""
        # Valid JSON
        is_valid, parsed = validate_json_input('{"key": "value"}')
        self.assertTrue(is_valid)
        self.assertEqual(parsed, {"key": "value"})
        
        is_valid, parsed = validate_json_input('{"key": 123}')
        self.assertTrue(is_valid)
        self.assertEqual(parsed, {"key": 123})
        
        is_valid, parsed = validate_json_input('{"key": [1, 2, 3]}')
        self.assertTrue(is_valid)
        self.assertEqual(parsed, {"key": [1, 2, 3]})
        
        # Invalid JSON
        is_valid, parsed = validate_json_input('')
        self.assertFalse(is_valid)
        self.assertIsNone(parsed)
        
        is_valid, parsed = validate_json_input(None)
        self.assertFalse(is_valid)
        self.assertIsNone(parsed)
        
        is_valid, parsed = validate_json_input('{"key": value}')
        self.assertFalse(is_valid)
        self.assertIsNone(parsed)
        
        is_valid, parsed = validate_json_input('{"key": "value"')
        self.assertFalse(is_valid)
        self.assertIsNone(parsed)
        
if __name__ == '__main__':
    unittest.main()
