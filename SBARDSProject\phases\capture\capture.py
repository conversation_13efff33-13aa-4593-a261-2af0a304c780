"""
SBARDS Capture Layer (طبقة الالتقاط)

This module implements the file capture layer that handles incoming files
and prepares them for analysis through the SBARDS pipeline.
"""

import os
import hashlib
import shutil
import logging

from datetime import datetime
from typing import Dict, Any, Optional, Union, BinaryIO
from pathlib import Path
import magic
import json


class CaptureLayer:
    """
    File Capture Layer for SBARDS

    Handles file ingestion, initial validation, and preparation for analysis.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the Capture Layer.

        Args:
            config (Dict[str, Any]): Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger("SBARDS.CaptureLayer")

        # Setup directories
        self.capture_dir = Path(config.get("capture", {}).get("directory", "captured_files"))
        self.temp_dir = Path(config.get("capture", {}).get("temp_directory", "temp"))
        self.quarantine_dir = Path(config.get("capture", {}).get("quarantine_directory", "quarantine"))

        # Create directories if they don't exist
        for directory in [self.capture_dir, self.temp_dir, self.quarantine_dir]:
            directory.mkdir(parents=True, exist_ok=True)

        # File size limits
        self.max_file_size = config.get("capture", {}).get("max_file_size_mb", 100) * 1024 * 1024
        self.min_file_size = config.get("capture", {}).get("min_file_size_bytes", 1)

        # Allowed/blocked file types
        self.allowed_extensions = set(config.get("capture", {}).get("allowed_extensions", []))
        self.blocked_extensions = set(config.get("capture", {}).get("blocked_extensions", []))

        # Initialize file type detector
        try:
            self.magic = magic.Magic(mime=True)
        except Exception as e:
            self.logger.warning(f"Could not initialize python-magic: {e}")
            self.magic = None

    def capture_file(self, file_input: Union[str, BinaryIO, bytes],
                    filename: Optional[str] = None,
                    metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Capture and process a file for analysis.

        Args:
            file_input: File path, file-like object, or bytes
            filename: Original filename (if not provided in file_input)
            metadata: Additional metadata about the file

        Returns:
            Dict[str, Any]: Capture result with file information
        """
        try:
            # Generate unique capture ID
            capture_id = self._generate_capture_id()

            # Process the file input
            file_info = self._process_file_input(file_input, filename, capture_id)

            # Validate file
            validation_result = self._validate_file(file_info)
            if not validation_result["valid"]:
                return {
                    "success": False,
                    "capture_id": capture_id,
                    "error": validation_result["error"],
                    "file_info": file_info
                }

            # Calculate file hash and metadata
            file_metadata = self._extract_metadata(file_info["temp_path"])

            # Move file to capture directory
            final_path = self._move_to_capture_directory(file_info, capture_id)

            # Create capture record
            capture_record = {
                "capture_id": capture_id,
                "timestamp": datetime.now().isoformat(),
                "original_filename": file_info["original_filename"],
                "captured_path": str(final_path),
                "file_size": file_metadata["size"],
                "file_hash": file_metadata["hash"],
                "mime_type": file_metadata["mime_type"],
                "file_extension": file_metadata["extension"],
                "metadata": metadata or {},
                "validation": validation_result,
                "status": "captured"
            }

            # Save capture record
            self._save_capture_record(capture_record)

            self.logger.info(f"Successfully captured file: {capture_id}")

            return {
                "success": True,
                "capture_id": capture_id,
                "file_path": str(final_path),
                "file_info": capture_record
            }

        except Exception as e:
            self.logger.error(f"Error capturing file: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def capture_directory(self, directory_path: str,
                         recursive: bool = True,
                         max_files: int = 1000,
                         metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Capture all files in a directory for analysis.

        Args:
            directory_path: Path to directory to scan
            recursive: Whether to scan subdirectories
            max_files: Maximum number of files to process
            metadata: Additional metadata about the scan

        Returns:
            Dict[str, Any]: Directory capture results
        """
        try:
            if not os.path.exists(directory_path):
                raise FileNotFoundError(f"Directory not found: {directory_path}")

            if not os.path.isdir(directory_path):
                raise ValueError(f"Path is not a directory: {directory_path}")

            # Generate unique scan ID
            scan_id = f"dirscan_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"

            # Collect files to process
            files_to_process = []

            if recursive:
                for root, dirs, files in os.walk(directory_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        files_to_process.append(file_path)
                        if len(files_to_process) >= max_files:
                            break
                    if len(files_to_process) >= max_files:
                        break
            else:
                for item in os.listdir(directory_path):
                    item_path = os.path.join(directory_path, item)
                    if os.path.isfile(item_path):
                        files_to_process.append(item_path)
                        if len(files_to_process) >= max_files:
                            break

            # Process each file
            captured_files = []
            failed_files = []

            self.logger.info(f"Processing {len(files_to_process)} files from directory: {directory_path}")

            for file_path in files_to_process:
                try:
                    result = self.capture_file(file_path, metadata=metadata)
                    if result.get("success"):
                        captured_files.append(result)
                    else:
                        failed_files.append({
                            "file_path": file_path,
                            "error": result.get("error", "Unknown error")
                        })
                except Exception as e:
                    failed_files.append({
                        "file_path": file_path,
                        "error": str(e)
                    })

            # Create directory scan record
            scan_record = {
                "scan_id": scan_id,
                "timestamp": datetime.now().isoformat(),
                "directory_path": directory_path,
                "recursive": recursive,
                "total_files_found": len(files_to_process),
                "files_captured": len(captured_files),
                "files_failed": len(failed_files),
                "captured_files": captured_files,
                "failed_files": failed_files,
                "metadata": metadata or {},
                "status": "completed"
            }

            # Save scan record
            self._save_directory_scan_record(scan_record)

            self.logger.info(f"Directory scan completed: {scan_id}")
            self.logger.info(f"  - Files captured: {len(captured_files)}")
            self.logger.info(f"  - Files failed: {len(failed_files)}")

            return {
                "success": True,
                "scan_id": scan_id,
                "directory_path": directory_path,
                "files_captured": len(captured_files),
                "files_failed": len(failed_files),
                "captured_files": captured_files,
                "failed_files": failed_files,
                "scan_record": scan_record
            }

        except Exception as e:
            self.logger.error(f"Error capturing directory: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _generate_capture_id(self) -> str:
        """Generate unique capture ID."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        return f"capture_{timestamp}"

    def _process_file_input(self, file_input: Union[str, BinaryIO, bytes],
                           filename: Optional[str], capture_id: str) -> Dict[str, Any]:
        """Process different types of file input."""
        if isinstance(file_input, str):
            # File path
            if not os.path.exists(file_input):
                raise FileNotFoundError(f"File not found: {file_input}")

            # Check if it's a directory
            if os.path.isdir(file_input):
                raise ValueError(f"Directory provided instead of file: {file_input}. Use capture_directory() method for directories.")

            original_filename = filename or os.path.basename(file_input)
            temp_path = self.temp_dir / f"{capture_id}_{original_filename}"
            shutil.copy2(file_input, temp_path)

        elif isinstance(file_input, bytes):
            # Bytes data
            original_filename = filename or f"{capture_id}.bin"
            temp_path = self.temp_dir / f"{capture_id}_{original_filename}"

            with open(temp_path, 'wb') as f:
                f.write(file_input)

        else:
            # File-like object
            original_filename = filename or getattr(file_input, 'name', f"{capture_id}.bin")
            temp_path = self.temp_dir / f"{capture_id}_{os.path.basename(original_filename)}"

            with open(temp_path, 'wb') as f:
                shutil.copyfileobj(file_input, f)

        return {
            "original_filename": original_filename,
            "temp_path": temp_path
        }

    def _validate_file(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """Validate captured file."""
        temp_path = file_info["temp_path"]

        # Check if file exists
        if not temp_path.exists():
            return {"valid": False, "error": "File does not exist"}

        # Check file size
        file_size = temp_path.stat().st_size
        if file_size < self.min_file_size:
            return {"valid": False, "error": f"File too small: {file_size} bytes"}

        if file_size > self.max_file_size:
            return {"valid": False, "error": f"File too large: {file_size} bytes"}

        # Check file extension
        extension = temp_path.suffix.lower()
        if self.blocked_extensions and extension in self.blocked_extensions:
            return {"valid": False, "error": f"Blocked file extension: {extension}"}

        if self.allowed_extensions and extension not in self.allowed_extensions:
            return {"valid": False, "error": f"File extension not allowed: {extension}"}

        return {"valid": True, "file_size": file_size, "extension": extension}

    def _extract_metadata(self, file_path: Path) -> Dict[str, Any]:
        """Extract file metadata."""
        # Calculate SHA-256 hash
        sha256_hash = hashlib.sha256()
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)

        # Get file stats
        stat = file_path.stat()

        # Detect MIME type
        mime_type = "application/octet-stream"
        if self.magic:
            try:
                mime_type = self.magic.from_file(str(file_path))
            except Exception as e:
                self.logger.warning(f"Could not detect MIME type: {e}")

        return {
            "size": stat.st_size,
            "hash": sha256_hash.hexdigest(),
            "mime_type": mime_type,
            "extension": file_path.suffix.lower(),
            "created_time": datetime.fromtimestamp(getattr(stat, 'st_birthtime', stat.st_ctime)).isoformat(),
            "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat()
        }

    def _move_to_capture_directory(self, file_info: Dict[str, Any], capture_id: str) -> Path:
        """Move file from temp to capture directory."""
        temp_path = file_info["temp_path"]

        # Create subdirectory based on date
        date_dir = self.capture_dir / datetime.now().strftime("%Y/%m/%d")
        date_dir.mkdir(parents=True, exist_ok=True)

        # Final filename
        final_filename = f"{capture_id}_{file_info['original_filename']}"
        final_path = date_dir / final_filename

        # Move file
        shutil.move(str(temp_path), str(final_path))

        return final_path

    def _save_capture_record(self, record: Dict[str, Any]) -> None:
        """Save capture record to JSON file."""
        records_dir = self.capture_dir / "records"
        records_dir.mkdir(exist_ok=True)

        record_file = records_dir / f"{record['capture_id']}.json"

        with open(record_file, 'w') as f:
            json.dump(record, f, indent=2)

    def _save_directory_scan_record(self, record: Dict[str, Any]) -> None:
        """Save directory scan record to JSON file."""
        records_dir = self.capture_dir / "directory_scans"
        records_dir.mkdir(exist_ok=True)

        record_file = records_dir / f"{record['scan_id']}.json"

        with open(record_file, 'w') as f:
            json.dump(record, f, indent=2)

    def get_capture_record(self, capture_id: str) -> Optional[Dict[str, Any]]:
        """Get capture record by ID."""
        record_file = self.capture_dir / "records" / f"{capture_id}.json"

        if not record_file.exists():
            return None

        try:
            with open(record_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Error reading capture record {capture_id}: {e}")
            return None

    def cleanup_temp_files(self, max_age_hours: int = 24) -> int:
        """Clean up old temporary files."""
        cleaned = 0
        cutoff_time = datetime.now().timestamp() - (max_age_hours * 3600)

        for temp_file in self.temp_dir.glob("*"):
            if temp_file.stat().st_mtime < cutoff_time:
                try:
                    temp_file.unlink()
                    cleaned += 1
                except Exception as e:
                    self.logger.warning(f"Could not delete temp file {temp_file}: {e}")

        self.logger.info(f"Cleaned up {cleaned} temporary files")
        return cleaned


def Capture(file_input: Union[str, BinaryIO, bytes],
           config: Dict[str, Any],
           filename: Optional[str] = None,
           metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Main capture function for backward compatibility.

    Args:
        file_input: File to capture
        config: Configuration dictionary
        filename: Optional filename
        metadata: Optional metadata

    Returns:
        Dict[str, Any]: Capture result
    """
    capture_layer = CaptureLayer(config)
    return capture_layer.capture_file(file_input, filename, metadata)