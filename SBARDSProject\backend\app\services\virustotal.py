"""
VirusTotal integration service.

This module provides integration with the VirusTotal API.
"""

import json
from typing import Dict, Any

import aiohttp

from ..core.config import settings
from ..core.logging import logger


async def check_file_hash(file_hash: str) -> Dict[str, Any]:
    """
    Check a file hash against VirusTotal API.
    
    Args:
        file_hash (str): SHA-256 hash of the file.
        
    Returns:
        Dict[str, Any]: VirusTotal API response.
    """
    if not settings.VIRUSTOTAL_API_KEY:
        logger.warning("VirusTotal API key not configured")
        return {"error": "VirusTotal API key not configured"}
    
    try:
        headers = {
            "x-apikey": settings.VIRUSTOTAL_API_KEY
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{settings.VIRUSTOTAL_API_URL}/files/{file_hash}",
                headers=headers
            ) as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 404:
                    return {"error": "File not found in VirusTotal database"}
                else:
                    error_text = await response.text()
                    logger.error(f"VirusTotal API error: {response.status} - {error_text}")
                    return {"error": f"VirusTotal API error: {response.status}"}
    except Exception as e:
        logger.error(f"Error checking VirusTotal: {e}")
        return {"error": str(e)}


async def submit_file(file_content: bytes, filename: str) -> Dict[str, Any]:
    """
    Submit a file to VirusTotal for analysis.
    
    Args:
        file_content (bytes): Content of the file.
        filename (str): Name of the file.
        
    Returns:
        Dict[str, Any]: VirusTotal API response.
    """
    if not settings.VIRUSTOTAL_API_KEY:
        logger.warning("VirusTotal API key not configured")
        return {"error": "VirusTotal API key not configured"}
    
    try:
        headers = {
            "x-apikey": settings.VIRUSTOTAL_API_KEY
        }
        
        files = {
            "file": (filename, file_content)
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{settings.VIRUSTOTAL_API_URL}/files",
                headers=headers,
                data=files
            ) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    logger.error(f"VirusTotal API error: {response.status} - {error_text}")
                    return {"error": f"VirusTotal API error: {response.status}"}
    except Exception as e:
        logger.error(f"Error submitting file to VirusTotal: {e}")
        return {"error": str(e)}


def parse_virustotal_result(vt_result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parse VirusTotal result to extract relevant information.
    
    Args:
        vt_result (Dict[str, Any]): VirusTotal API response.
        
    Returns:
        Dict[str, Any]: Parsed VirusTotal result.
    """
    if "error" in vt_result:
        return vt_result
    
    try:
        result = {
            "raw": vt_result
        }
        
        if "data" in vt_result and "attributes" in vt_result["data"]:
            attributes = vt_result["data"]["attributes"]
            
            # Extract basic information
            result["type_description"] = attributes.get("type_description", "Unknown")
            result["size"] = attributes.get("size", 0)
            result["md5"] = attributes.get("md5", "")
            result["sha1"] = attributes.get("sha1", "")
            result["sha256"] = attributes.get("sha256", "")
            
            # Extract analysis stats
            if "last_analysis_stats" in attributes:
                stats = attributes["last_analysis_stats"]
                result["stats"] = {
                    "malicious": stats.get("malicious", 0),
                    "suspicious": stats.get("suspicious", 0),
                    "undetected": stats.get("undetected", 0),
                    "harmless": stats.get("harmless", 0)
                }
            
            # Extract dates
            result["first_seen"] = attributes.get("first_submission_date", "")
            result["last_seen"] = attributes.get("last_analysis_date", "")
            
            # Extract detection details
            if "last_analysis_results" in attributes:
                result["detections"] = []
                for engine, detection in attributes["last_analysis_results"].items():
                    if detection.get("category") == "malicious":
                        result["detections"].append({
                            "engine": engine,
                            "result": detection.get("result", ""),
                            "method": detection.get("method", ""),
                            "engine_version": detection.get("engine_version", "")
                        })
        
        return result
    except Exception as e:
        logger.error(f"Error parsing VirusTotal result: {e}")
        return {"error": f"Error parsing VirusTotal result: {str(e)}"}
