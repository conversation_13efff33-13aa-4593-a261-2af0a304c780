"""
Configuration Loader for SBARDS

This module provides configuration loading and management functionality.
"""

import os
import json
import platform
import logging
from typing import Dict, Any, Optional

class ConfigLoader:
    """
    Configuration loader for the SBARDS project.
    
    This class handles loading and validating configuration from JSON files.
    """
    
    def __init__(self, config_path: str = "config.json"):
        """
        Initialize the configuration loader.
        
        Args:
            config_path (str): Path to the configuration file
        """
        self.config_path = config_path
        self.logger = logging.getLogger("SBARDS.ConfigLoader")
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """
        Load configuration from file.
        
        Returns:
            Dict[str, Any]: Configuration dictionary
        """
        # Check if config file exists
        if not os.path.exists(self.config_path):
            self.logger.warning(f"Configuration file not found: {self.config_path}")
            self.logger.info("Creating default configuration")
            return self._create_default_config()
            
        # Load configuration
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            # Validate configuration
            self._validate_config(config)
            
            return config
        except Exception as e:
            self.logger.error(f"Error loading configuration: {e}")
            self.logger.info("Creating default configuration")
            return self._create_default_config()
    
    def _create_default_config(self) -> Dict[str, Any]:
        """
        Create default configuration.
        
        Returns:
            Dict[str, Any]: Default configuration dictionary
        """
        # Default configuration
        config = {
            "scanner": {
                "target_directory": "samples",
                "recursive": True,
                "max_depth": 5,
                "exclude_dirs": [],
                "exclude_extensions": [],
                "max_file_size_mb": 100
            },
            "rules": {
                "rule_files": [
                    "rules/custom_rules.yar"
                ],
                "enable_categories": [
                    "all"
                ]
            },
            "output": {
                "log_directory": "logs",
                "output_directory": "output",
                "json_output": True,
                "csv_output": False,
                "html_report": False,
                "log_level": "info"
            },
            "performance": {
                "threads": 1,
                "batch_size": 10,
                "timeout_seconds": 30
            },
            "features": {
                "monitor_downloads": True,
                "hash_optimization": True
            }
        }
        
        # Save default configuration
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4)
                
            self.logger.info(f"Default configuration saved to {self.config_path}")
        except Exception as e:
            self.logger.error(f"Error saving default configuration: {e}")
            
        return config
    
    def _validate_config(self, config: Dict[str, Any]) -> None:
        """
        Validate configuration.
        
        Args:
            config (Dict[str, Any]): Configuration dictionary
        """
        # Check required sections
        required_sections = ["scanner", "rules", "output", "performance"]
        for section in required_sections:
            if section not in config:
                self.logger.warning(f"Missing required section in configuration: {section}")
                config[section] = self._create_default_config()[section]
    
    def get_config(self) -> Dict[str, Any]:
        """
        Get the configuration dictionary.
        
        Returns:
            Dict[str, Any]: Configuration dictionary
        """
        return self.config
    
    def save_config(self, config: Dict[str, Any]) -> bool:
        """
        Save configuration to file.
        
        Args:
            config (Dict[str, Any]): Configuration dictionary
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4)
                
            self.logger.info(f"Configuration saved to {self.config_path}")
            self.config = config
            return True
        except Exception as e:
            self.logger.error(f"Error saving configuration: {e}")
            return False
    
    def get_platform_config(self) -> Dict[str, Any]:
        """
        Get platform-specific configuration.
        
        Returns:
            Dict[str, Any]: Platform-specific configuration dictionary
        """
        # Get current platform
        current_platform = platform.system().lower()
        
        # Get platform-specific configuration
        platform_config = self.config.get(current_platform, {})
        
        return platform_config
