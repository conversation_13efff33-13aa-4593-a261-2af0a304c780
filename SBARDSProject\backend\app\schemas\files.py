"""
Pydantic schemas for file operations.

This module provides Pydantic schemas for file operations.
"""

from typing import Dict, Optional, Any

from pydantic import BaseModel, Field


class FileHashCheck(BaseModel):
    """Schema for file hash check requests."""
    
    file_hash: str = Field(..., description="SHA-256 hash of the file")


class VirusTotalSummary(BaseModel):
    """Schema for VirusTotal summary."""
    
    malicious: int = Field(0, description="Number of engines that detected the file as malicious")
    suspicious: int = Field(0, description="Number of engines that detected the file as suspicious")
    undetected: int = Field(0, description="Number of engines that did not detect the file")
    harmless: int = Field(0, description="Number of engines that detected the file as harmless")


class FileCheckResponse(BaseModel):
    """Schema for file check responses."""
    
    filename: str = Field(..., description="Name of the file")
    file_hash: str = Field(..., description="SHA-256 hash of the file")
    virustotal_result: Dict[str, Any] = Field(..., description="VirusTotal result")
    summary: Optional[VirusTotalSummary] = Field(None, description="Summary of VirusTotal results")
