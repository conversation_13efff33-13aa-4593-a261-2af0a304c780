"""
Response Manager for SBARDS

This module provides automated response capabilities for the monitoring layer.
"""

import os
import re
import time
import logging
import threading
import subprocess
import platform
from typing import Dict, List, Any
from datetime import datetime

class ResponseManager:
    """
    Manages automated responses to security incidents.

    This class provides capabilities to automatically respond to security
    incidents detected by the monitoring layer, such as:
    1. Terminating suspicious processes
    2. Isolating infected files
    3. Blocking network connections
    4. Notifying administrators
    """

    def __init__(self, config: Dict[str, Any], alert_manager=None):
        """
        Initialize the response manager.

        Args:
            config (Dict[str, Any]): Response configuration
            alert_manager: Alert manager instance for generating alerts
        """
        self.config = config
        self.alert_manager = alert_manager
        self.logger = logging.getLogger("SBARDS.ResponseManager")

        # Response configuration
        self.enabled = config.get("enabled", True)
        self.auto_respond = config.get("auto_respond", False)
        self.response_level = config.get("response_level", "medium")

        # Response actions configuration
        self.actions_config = config.get("actions", {})

        # Response history
        self.response_history = []

        # Platform-specific settings
        self.platform = platform.system().lower()

        # Quarantine directory
        self.quarantine_dir = os.path.join("output", "quarantine")
        os.makedirs(self.quarantine_dir, exist_ok=True)

        self.logger.info(f"Response Manager initialized on {platform.system()}")

    def respond_to_alert(self, alert: Dict[str, Any]) -> Dict[str, Any]:
        """
        Respond to an alert based on its type and severity.

        Args:
            alert (Dict[str, Any]): Alert to respond to

        Returns:
            Dict[str, Any]: Response details
        """
        if not self.enabled:
            return {"status": "disabled", "message": "Response manager is disabled"}

        # Check if we should auto-respond
        if not self.auto_respond and alert.get("severity") != "critical":
            return {"status": "skipped", "message": "Auto-respond is disabled for non-critical alerts"}

        # Determine response actions based on alert type and severity
        alert_type = alert.get("type", "unknown")
        severity = alert.get("severity", "info")

        response_details = {
            "timestamp": time.time(),
            "datetime": datetime.now().isoformat(),
            "alert_id": alert.get("id", "unknown"),
            "alert_type": alert_type,
            "severity": severity,
            "actions_taken": []
        }

        try:
            # Handle different alert types
            if alert_type == "ransomware_behavior":
                response_details = self._respond_to_ransomware(alert, response_details)
            elif alert_type == "suspicious_process":
                response_details = self._respond_to_suspicious_process(alert, response_details)
            elif alert_type == "suspicious_connection":
                response_details = self._respond_to_suspicious_connection(alert, response_details)
            elif alert_type == "high_entropy_file":
                response_details = self._respond_to_high_entropy_file(alert, response_details)
            elif alert_type == "mass_file_operations":
                response_details = self._respond_to_mass_file_operations(alert, response_details)
            elif alert_type == "data_exfiltration":
                response_details = self._respond_to_data_exfiltration(alert, response_details)
            elif alert_type == "suspicious_process_file_access":
                response_details = self._respond_to_suspicious_file_access(alert, response_details)
            else:
                response_details["status"] = "skipped"
                response_details["message"] = f"No response defined for alert type: {alert_type}"

            # Record the response
            self.response_history.append(response_details)

            # Log the response
            self.logger.info(
                f"Response to {alert_type} alert: "
                f"Status: {response_details.get('status')}, "
                f"Actions: {len(response_details.get('actions_taken', []))}"
            )

            return response_details

        except Exception as e:
            error_details = {
                "status": "error",
                "message": f"Error responding to alert: {str(e)}",
                "error": str(e)
            }
            response_details.update(error_details)
            self.logger.error(f"Error responding to alert: {e}")
            return response_details

    def _respond_to_ransomware(self, alert: Dict[str, Any], response_details: Dict[str, Any]) -> Dict[str, Any]:
        """Respond to ransomware behavior alert."""
        actions_taken = []

        # Get alert details
        details = alert.get("details", {})
        suspicious_processes = details.get("suspicious_processes", [])
        high_entropy_files = details.get("high_entropy_files", [])

        # Terminate suspicious processes
        if self.actions_config.get("terminate_processes", False):
            for process in suspicious_processes:
                if isinstance(process, dict) and "pid" in process:
                    pid = process["pid"]
                elif isinstance(process, str) and ":" in process:
                    pid = process.split(":")[1]
                else:
                    continue

                try:
                    self._terminate_process(pid)
                    actions_taken.append({
                        "action": "terminate_process",
                        "target": pid,
                        "status": "success"
                    })
                except Exception as e:
                    actions_taken.append({
                        "action": "terminate_process",
                        "target": pid,
                        "status": "error",
                        "error": str(e)
                    })

        # Quarantine high entropy files
        if self.actions_config.get("quarantine_files", False):
            for file_path in high_entropy_files:
                try:
                    self._quarantine_file(file_path)
                    actions_taken.append({
                        "action": "quarantine_file",
                        "target": file_path,
                        "status": "success"
                    })
                except Exception as e:
                    actions_taken.append({
                        "action": "quarantine_file",
                        "target": file_path,
                        "status": "error",
                        "error": str(e)
                    })

        # Send notification
        if self.actions_config.get("send_notification", False):
            try:
                self._send_notification(
                    "Ransomware Activity Detected",
                    f"Ransomware activity detected with confidence {details.get('confidence_score', 0):.2f}. "
                    f"Terminated {len(suspicious_processes)} processes and quarantined {len(high_entropy_files)} files."
                )
                actions_taken.append({
                    "action": "send_notification",
                    "status": "success"
                })
            except Exception as e:
                actions_taken.append({
                    "action": "send_notification",
                    "status": "error",
                    "error": str(e)
                })

        response_details["actions_taken"] = actions_taken
        response_details["status"] = "success" if actions_taken else "no_action"
        response_details["message"] = f"Responded to ransomware alert with {len(actions_taken)} actions"

        return response_details

    def _respond_to_suspicious_process(self, alert: Dict[str, Any], response_details: Dict[str, Any]) -> Dict[str, Any]:
        """Respond to suspicious process alert."""
        actions_taken = []

        # Get alert details
        details = alert.get("details", {})
        process_name = details.get("process_name", "unknown")
        process_id = details.get("process_id", "unknown")

        # Terminate suspicious process
        if self.actions_config.get("terminate_processes", False):
            try:
                self._terminate_process(process_id)
                actions_taken.append({
                    "action": "terminate_process",
                    "target": process_id,
                    "status": "success"
                })
            except Exception as e:
                actions_taken.append({
                    "action": "terminate_process",
                    "target": process_id,
                    "status": "error",
                    "error": str(e)
                })

        response_details["actions_taken"] = actions_taken
        response_details["status"] = "success" if actions_taken else "no_action"
        response_details["message"] = f"Responded to suspicious process alert with {len(actions_taken)} actions"

        return response_details

    def _respond_to_suspicious_connection(self, alert: Dict[str, Any], response_details: Dict[str, Any]) -> Dict[str, Any]:
        """Respond to suspicious connection alert."""
        actions_taken = []

        # Get alert details
        details = alert.get("details", {})
        remote = details.get("remote", "unknown")

        # Block connection
        if self.actions_config.get("block_connections", False):
            try:
                self._block_connection(remote)
                actions_taken.append({
                    "action": "block_connection",
                    "target": remote,
                    "status": "success"
                })
            except Exception as e:
                actions_taken.append({
                    "action": "block_connection",
                    "target": remote,
                    "status": "error",
                    "error": str(e)
                })

        response_details["actions_taken"] = actions_taken
        response_details["status"] = "success" if actions_taken else "no_action"
        response_details["message"] = f"Responded to suspicious connection alert with {len(actions_taken)} actions"

        return response_details

    def _respond_to_high_entropy_file(self, alert: Dict[str, Any], response_details: Dict[str, Any]) -> Dict[str, Any]:
        """Respond to high entropy file alert."""
        actions_taken = []

        # Get alert details
        details = alert.get("details", {})
        file_path = details.get("file_path", "unknown")

        # Quarantine file
        if self.actions_config.get("quarantine_files", False):
            try:
                self._quarantine_file(file_path)
                actions_taken.append({
                    "action": "quarantine_file",
                    "target": file_path,
                    "status": "success"
                })
            except Exception as e:
                actions_taken.append({
                    "action": "quarantine_file",
                    "target": file_path,
                    "status": "error",
                    "error": str(e)
                })

        response_details["actions_taken"] = actions_taken
        response_details["status"] = "success" if actions_taken else "no_action"
        response_details["message"] = f"Responded to high entropy file alert with {len(actions_taken)} actions"

        return response_details

    def _respond_to_mass_file_operations(self, alert: Dict[str, Any], response_details: Dict[str, Any]) -> Dict[str, Any]:
        """Respond to mass file operations alert."""
        actions_taken = []

        # Get alert details
        details = alert.get("details", {})
        operation_type = details.get("operation_type", "unknown")

        # Send notification
        if self.actions_config.get("send_notification", False):
            try:
                self._send_notification(
                    "Mass File Operations Detected",
                    f"Mass file {operation_type} operations detected. "
                    f"This could indicate ransomware activity."
                )
                actions_taken.append({
                    "action": "send_notification",
                    "status": "success"
                })
            except Exception as e:
                actions_taken.append({
                    "action": "send_notification",
                    "status": "error",
                    "error": str(e)
                })

        response_details["actions_taken"] = actions_taken
        response_details["status"] = "success" if actions_taken else "no_action"
        response_details["message"] = f"Responded to mass file operations alert with {len(actions_taken)} actions"

        return response_details

    def _respond_to_data_exfiltration(self, alert: Dict[str, Any], response_details: Dict[str, Any]) -> Dict[str, Any]:
        """Respond to data exfiltration alert."""
        actions_taken = []

        # Get alert details
        details = alert.get("details", {})
        suspicious_connections = details.get("suspicious_connections", [])
        suspicious_processes = details.get("suspicious_processes", [])

        # Block connections
        if self.actions_config.get("block_connections", False):
            for conn in suspicious_connections:
                if isinstance(conn, dict) and "remote" in conn:
                    remote = conn["remote"]
                else:
                    continue

                try:
                    self._block_connection(remote)
                    actions_taken.append({
                        "action": "block_connection",
                        "target": remote,
                        "status": "success"
                    })
                except Exception as e:
                    actions_taken.append({
                        "action": "block_connection",
                        "target": remote,
                        "status": "error",
                        "error": str(e)
                    })

        # Terminate suspicious processes
        if self.actions_config.get("terminate_processes", False):
            for process in suspicious_processes:
                if isinstance(process, dict) and "pid" in process:
                    pid = process["pid"]
                elif isinstance(process, str) and ":" in process:
                    pid = process.split(":")[1]
                else:
                    continue

                try:
                    self._terminate_process(pid)
                    actions_taken.append({
                        "action": "terminate_process",
                        "target": pid,
                        "status": "success"
                    })
                except Exception as e:
                    actions_taken.append({
                        "action": "terminate_process",
                        "target": pid,
                        "status": "error",
                        "error": str(e)
                    })

        # Send notification
        if self.actions_config.get("send_notification", False):
            try:
                self._send_notification(
                    "Data Exfiltration Detected",
                    f"Possible data exfiltration detected with confidence {details.get('confidence_score', 0):.2f}. "
                    f"Blocked {len(suspicious_connections)} connections and terminated {len(suspicious_processes)} processes."
                )
                actions_taken.append({
                    "action": "send_notification",
                    "status": "success"
                })
            except Exception as e:
                actions_taken.append({
                    "action": "send_notification",
                    "status": "error",
                    "error": str(e)
                })

        response_details["actions_taken"] = actions_taken
        response_details["status"] = "success" if actions_taken else "no_action"
        response_details["message"] = f"Responded to data exfiltration alert with {len(actions_taken)} actions"

        return response_details

    def _respond_to_suspicious_file_access(self, alert: Dict[str, Any], response_details: Dict[str, Any]) -> Dict[str, Any]:
        """Respond to suspicious file access alert."""
        actions_taken = []

        # Get alert details
        details = alert.get("details", {})
        sensitive_file_accesses = details.get("sensitive_file_accesses", [])
        suspicious_processes = details.get("suspicious_processes", [])

        # Terminate suspicious processes
        if self.actions_config.get("terminate_processes", False):
            for process in suspicious_processes:
                if isinstance(process, dict) and "pid" in process:
                    pid = process["pid"]
                else:
                    continue

                try:
                    self._terminate_process(pid)
                    actions_taken.append({
                        "action": "terminate_process",
                        "target": pid,
                        "status": "success"
                    })
                except Exception as e:
                    actions_taken.append({
                        "action": "terminate_process",
                        "target": pid,
                        "status": "error",
                        "error": str(e)
                    })

        # Send notification
        if self.actions_config.get("send_notification", False):
            try:
                self._send_notification(
                    "Suspicious File Access Detected",
                    f"Suspicious file access detected with confidence {details.get('confidence_score', 0):.2f}. "
                    f"Terminated {len(suspicious_processes)} processes."
                )
                actions_taken.append({
                    "action": "send_notification",
                    "status": "success"
                })
            except Exception as e:
                actions_taken.append({
                    "action": "send_notification",
                    "status": "error",
                    "error": str(e)
                })

        response_details["actions_taken"] = actions_taken
        response_details["status"] = "success" if actions_taken else "no_action"
        response_details["message"] = f"Responded to suspicious file access alert with {len(actions_taken)} actions"

        return response_details

    def _terminate_process(self, pid: str):
        """
        Terminate a process by its PID.

        Args:
            pid (str): Process ID to terminate
        """
        self.logger.info(f"Terminating process with PID: {pid}")

        try:
            if self.platform == "windows":
                subprocess.run(["taskkill", "/F", "/PID", str(pid)], check=True)
            else:
                subprocess.run(["kill", "-9", str(pid)], check=True)
        except subprocess.SubprocessError as e:
            self.logger.error(f"Error terminating process {pid}: {e}")
            raise

    def _quarantine_file(self, file_path: str):
        """
        Quarantine a file by moving it to the quarantine directory.

        Args:
            file_path (str): Path to the file to quarantine
        """
        self.logger.info(f"Quarantining file: {file_path}")

        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File not found: {file_path}")

            # Create a unique filename in the quarantine directory
            filename = os.path.basename(file_path)
            quarantine_path = os.path.join(self.quarantine_dir, f"{int(time.time())}_{filename}")

            # Move the file to quarantine
            os.rename(file_path, quarantine_path)

            # Create a metadata file with information about the quarantined file
            metadata_path = f"{quarantine_path}.meta"
            with open(metadata_path, "w") as f:
                f.write(f"Original path: {file_path}\n")
                f.write(f"Quarantine time: {datetime.now().isoformat()}\n")
                f.write(f"Quarantine reason: Security alert\n")

            return quarantine_path

        except Exception as e:
            self.logger.error(f"Error quarantining file {file_path}: {e}")
            raise

    def _block_connection(self, remote: str):
        """
        Block a network connection to a remote host.

        Args:
            remote (str): Remote host to block
        """
        self.logger.info(f"Blocking connection to: {remote}")

        # This is a simplified implementation
        # In a real-world scenario, this would use the system's firewall
        try:
            if self.platform == "windows":
                # Add a firewall rule to block the connection
                rule_name = f"SBARDS_Block_{remote.replace('.', '_')}"
                subprocess.run([
                    "netsh", "advfirewall", "firewall", "add", "rule",
                    f"name={rule_name}", "dir=out", "action=block",
                    f"remoteip={remote}"
                ], check=True)
            else:
                # Add an iptables rule to block the connection
                subprocess.run([
                    "iptables", "-A", "OUTPUT", "-d", remote, "-j", "DROP"
                ], check=True)
        except subprocess.SubprocessError as e:
            self.logger.error(f"Error blocking connection to {remote}: {e}")
            raise

    def _send_notification(self, title: str, message: str):
        """
        Send a notification to the administrator.

        Args:
            title (str): Notification title
            message (str): Notification message
        """
        self.logger.info(f"Sending notification: {title}")

        # This is a simplified implementation
        # In a real-world scenario, this would use email, SMS, or other notification methods
        try:
            if self.platform == "windows":
                # Use PowerShell to show a notification
                ps_script = f'powershell -Command "& {{Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.MessageBox]::Show(\'{message}\', \'{title}\', \'OK\', \'Warning\')}}"'
                subprocess.run(ps_script, shell=True, check=True)
            else:
                # Use notify-send on Linux
                subprocess.run(["notify-send", title, message], check=True)

            # Log the notification
            self.logger.info(f"Notification sent: {title} - {message}")

        except subprocess.SubprocessError as e:
            self.logger.error(f"Error sending notification: {e}")
            raise

    def get_response_history(self, count: int = None) -> List[Dict[str, Any]]:
        """
        Get response history.

        Args:
            count (int, optional): Number of history entries to retrieve

        Returns:
            List[Dict[str, Any]]: List of response history entries
        """
        if count is None:
            return self.response_history
        else:
            return self.response_history[-count:]