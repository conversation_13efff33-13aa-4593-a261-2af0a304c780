<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SBARDS Monitoring Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding-top: 20px;
            background-color: #f5f5f5;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            font-weight: bold;
            background-color: #f8f9fa;
        }
        .alert-critical {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .alert-warning {
            background-color: #fff3cd;
            border-color: #ffeeba;
            color: #856404;
        }
        .alert-info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .status-running {
            color: #28a745;
        }
        .status-stopped {
            color: #dc3545;
        }
        .refresh-btn {
            cursor: pointer;
        }
        .data-table {
            font-size: 0.9rem;
        }
        .data-table th {
            background-color: #f8f9fa;
        }
        .timestamp {
            font-size: 0.8rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="d-flex justify-content-between align-items-center mb-4">
            <h1>SBARDS Monitoring Dashboard</h1>
            <div>
                <button id="startBtn" class="btn btn-success me-2">Start Monitoring</button>
                <button id="stopBtn" class="btn btn-danger">Stop Monitoring</button>
            </div>
        </header>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>System Status</span>
                        <span class="refresh-btn" onclick="refreshStatus()">🔄</span>
                    </div>
                    <div class="card-body">
                        <div id="statusContent">
                            <p>Loading status...</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>Alerts</span>
                        <span class="refresh-btn" onclick="refreshAlerts()">🔄</span>
                    </div>
                    <div class="card-body">
                        <div id="alertsContent">
                            <p>Loading alerts...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>Correlated Events</span>
                        <span class="refresh-btn" onclick="refreshCorrelatedEvents()">🔄</span>
                    </div>
                    <div class="card-body">
                        <div id="correlatedEventsContent">
                            <p>Loading correlated events...</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>Automatic Responses</span>
                        <span class="refresh-btn" onclick="refreshResponses()">🔄</span>
                    </div>
                    <div class="card-body">
                        <div id="responsesContent">
                            <p>Loading responses...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>File Operations</span>
                        <span class="refresh-btn" onclick="refreshFileOperations()">🔄</span>
                    </div>
                    <div class="card-body">
                        <div id="fileOperationsContent">
                            <p>Loading file operations...</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>Network Connections</span>
                        <span class="refresh-btn" onclick="refreshConnections()">🔄</span>
                    </div>
                    <div class="card-body">
                        <div id="connectionsContent">
                            <p>Loading connections...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Refresh intervals (in milliseconds)
        const REFRESH_INTERVAL = 5000;

        // Start automatic refresh
        document.addEventListener('DOMContentLoaded', function() {
            refreshAll();
            setInterval(refreshAll, REFRESH_INTERVAL);

            // Add event listeners to buttons
            document.getElementById('startBtn').addEventListener('click', startMonitoring);
            document.getElementById('stopBtn').addEventListener('click', stopMonitoring);
        });

        // Refresh all data
        function refreshAll() {
            refreshStatus();
            refreshAlerts();
            refreshCorrelatedEvents();
            refreshResponses();
            refreshFileOperations();
            refreshConnections();
        }

        // Refresh status
        function refreshStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    const statusContent = document.getElementById('statusContent');
                    let html = '<div class="table-responsive"><table class="table table-sm data-table">';
                    
                    // Overall status
                    html += `<tr><th>Monitoring Status</th><td><span class="${data.is_running ? 'status-running' : 'status-stopped'}">${data.is_running ? 'Running' : 'Stopped'}</span></td></tr>`;
                    
                    // Component status
                    if (data.components) {
                        for (const [component, status] of Object.entries(data.components)) {
                            html += `<tr><th>${formatComponentName(component)}</th><td>${status ? '✅ Enabled' : '❌ Disabled'}</td></tr>`;
                        }
                    }
                    
                    html += '</table></div>';
                    statusContent.innerHTML = html;
                })
                .catch(error => {
                    console.error('Error fetching status:', error);
                    document.getElementById('statusContent').innerHTML = '<p class="text-danger">Error loading status</p>';
                });
        }

        // Refresh alerts
        function refreshAlerts() {
            fetch('/api/alerts')
                .then(response => response.json())
                .then(data => {
                    const alertsContent = document.getElementById('alertsContent');
                    if (data.length === 0) {
                        alertsContent.innerHTML = '<p>No alerts</p>';
                        return;
                    }
                    
                    let html = '';
                    data.forEach(alert => {
                        const alertClass = `alert-${alert.severity}`;
                        html += `
                            <div class="alert ${alertClass} mb-2">
                                <div class="d-flex justify-content-between">
                                    <strong>${alert.source}: ${alert.message}</strong>
                                    <span class="badge bg-secondary">${alert.severity}</span>
                                </div>
                                <div class="timestamp">${formatTimestamp(alert.datetime)}</div>
                            </div>
                        `;
                    });
                    
                    alertsContent.innerHTML = html;
                })
                .catch(error => {
                    console.error('Error fetching alerts:', error);
                    document.getElementById('alertsContent').innerHTML = '<p class="text-danger">Error loading alerts</p>';
                });
        }

        // Refresh correlated events
        function refreshCorrelatedEvents() {
            fetch('/api/correlated_events')
                .then(response => response.json())
                .then(data => {
                    const correlatedEventsContent = document.getElementById('correlatedEventsContent');
                    if (data.length === 0) {
                        correlatedEventsContent.innerHTML = '<p>No correlated events</p>';
                        return;
                    }
                    
                    let html = '<div class="table-responsive"><table class="table table-sm data-table">';
                    html += '<thead><tr><th>Type</th><th>Confidence</th><th>Time</th><th>Details</th></tr></thead><tbody>';
                    
                    data.forEach(event => {
                        html += `
                            <tr>
                                <td>${event.type}</td>
                                <td>${event.confidence_score ? event.confidence_score.toFixed(2) : 'N/A'}</td>
                                <td>${formatTimestamp(event.datetime)}</td>
                                <td><button class="btn btn-sm btn-outline-secondary" onclick="alert('${JSON.stringify(event.details).replace(/"/g, '&quot;')}')">View</button></td>
                            </tr>
                        `;
                    });
                    
                    html += '</tbody></table></div>';
                    correlatedEventsContent.innerHTML = html;
                })
                .catch(error => {
                    console.error('Error fetching correlated events:', error);
                    document.getElementById('correlatedEventsContent').innerHTML = '<p class="text-danger">Error loading correlated events</p>';
                });
        }

        // Refresh responses
        function refreshResponses() {
            fetch('/api/responses')
                .then(response => response.json())
                .then(data => {
                    const responsesContent = document.getElementById('responsesContent');
                    if (data.length === 0) {
                        responsesContent.innerHTML = '<p>No automatic responses</p>';
                        return;
                    }
                    
                    let html = '<div class="table-responsive"><table class="table table-sm data-table">';
                    html += '<thead><tr><th>Alert Type</th><th>Status</th><th>Time</th><th>Actions</th></tr></thead><tbody>';
                    
                    data.forEach(response => {
                        html += `
                            <tr>
                                <td>${response.alert_type}</td>
                                <td>${response.status}</td>
                                <td>${formatTimestamp(response.datetime)}</td>
                                <td>${response.actions_taken ? response.actions_taken.length : 0} actions</td>
                            </tr>
                        `;
                    });
                    
                    html += '</tbody></table></div>';
                    responsesContent.innerHTML = html;
                })
                .catch(error => {
                    console.error('Error fetching responses:', error);
                    document.getElementById('responsesContent').innerHTML = '<p class="text-danger">Error loading responses</p>';
                });
        }

        // Refresh file operations
        function refreshFileOperations() {
            fetch('/api/file_operations')
                .then(response => response.json())
                .then(data => {
                    const fileOperationsContent = document.getElementById('fileOperationsContent');
                    if (data.length === 0) {
                        fileOperationsContent.innerHTML = '<p>No file operations</p>';
                        return;
                    }
                    
                    let html = '<div class="table-responsive"><table class="table table-sm data-table">';
                    html += '<thead><tr><th>Operation</th><th>Path</th><th>Time</th></tr></thead><tbody>';
                    
                    data.forEach(operation => {
                        html += `
                            <tr>
                                <td>${operation.type}</td>
                                <td>${truncateText(operation.path, 30)}</td>
                                <td>${formatTimestamp(operation.datetime)}</td>
                            </tr>
                        `;
                    });
                    
                    html += '</tbody></table></div>';
                    fileOperationsContent.innerHTML = html;
                })
                .catch(error => {
                    console.error('Error fetching file operations:', error);
                    document.getElementById('fileOperationsContent').innerHTML = '<p class="text-danger">Error loading file operations</p>';
                });
        }

        // Refresh connections
        function refreshConnections() {
            fetch('/api/connections')
                .then(response => response.json())
                .then(data => {
                    const connectionsContent = document.getElementById('connectionsContent');
                    if (data.length === 0) {
                        connectionsContent.innerHTML = '<p>No network connections</p>';
                        return;
                    }
                    
                    let html = '<div class="table-responsive"><table class="table table-sm data-table">';
                    html += '<thead><tr><th>Connection</th></tr></thead><tbody>';
                    
                    data.forEach(conn => {
                        html += `
                            <tr>
                                <td>${conn.connection}</td>
                            </tr>
                        `;
                    });
                    
                    html += '</tbody></table></div>';
                    connectionsContent.innerHTML = html;
                })
                .catch(error => {
                    console.error('Error fetching connections:', error);
                    document.getElementById('connectionsContent').innerHTML = '<p class="text-danger">Error loading connections</p>';
                });
        }

        // Start monitoring
        function startMonitoring() {
            fetch('/api/start_monitoring', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'started') {
                    alert('Monitoring started successfully');
                    refreshStatus();
                } else if (data.status === 'already_running') {
                    alert('Monitoring is already running');
                } else {
                    alert('Error starting monitoring: ' + (data.message || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error starting monitoring:', error);
                alert('Error starting monitoring');
            });
        }

        // Stop monitoring
        function stopMonitoring() {
            fetch('/api/stop_monitoring', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'stopped') {
                    alert('Monitoring stopped successfully');
                    refreshStatus();
                } else if (data.status === 'not_running') {
                    alert('Monitoring is not running');
                } else {
                    alert('Error stopping monitoring: ' + (data.message || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error stopping monitoring:', error);
                alert('Error stopping monitoring');
            });
        }

        // Helper functions
        function formatTimestamp(timestamp) {
            if (!timestamp) return 'N/A';
            
            // If it's an ISO string, convert to local time
            if (typeof timestamp === 'string' && timestamp.includes('T')) {
                const date = new Date(timestamp);
                return date.toLocaleString();
            }
            
            return timestamp;
        }

        function formatComponentName(name) {
            return name
                .replace(/_/g, ' ')
                .replace(/\b\w/g, l => l.toUpperCase());
        }

        function truncateText(text, maxLength) {
            if (!text) return '';
            if (text.length <= maxLength) return text;
            return text.substring(0, maxLength) + '...';
        }
    </script>
</body>
</html>
