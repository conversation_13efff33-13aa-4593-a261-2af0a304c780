"""
Tests for the stats API endpoints.

This module provides tests for the stats API endpoints.
"""

import pytest
from fastapi import status

from app.db.models import ScanReport, FileResult
from app.core.config import settings


def test_get_stats(client, db):
    """Test getting statistics."""
    # Create some scan reports
    for i in range(3):
        db_report = ScanReport(
            scan_id=f"test_scan_{i}",
            scan_path=f"/test/path_{i}",
            files_scanned=10,
            threats_found=i,
            report_path=f"/test/path_{i}/report.html",
            report_content=f"<html><body>Test Report {i}</body></html>"
        )
        db.add(db_report)
        db.commit()
        
        # Add file results
        for j in range(i):
            db_file_result = FileResult(
                scan_report_id=db_report.id,
                file_path=f"/test/path_{i}/file{j}.txt",
                file_hash=f"abcdef{i}{j}",
                is_threat=True,
                threat_type=f"Test Threat {j}"
            )
            db.add(db_file_result)
        db.commit()
    
    # Get statistics
    response = client.get(f"{settings.API_V1_STR}/stats/")
    
    # Check response
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["total_scans"] == 3
    assert data["total_files_scanned"] == 30  # 3 reports * 10 files
    assert data["total_threats"] == 3  # 0 + 1 + 2
    assert "recent_scans" in data
    assert "recent_threats" in data
    assert "top_threat_types" in data
