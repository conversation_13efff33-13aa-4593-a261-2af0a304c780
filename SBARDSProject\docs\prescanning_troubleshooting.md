# SBARDS Pre-scanning Phase: Troubleshooting Guide

## Table of Contents
1. [Common Issues and Solutions](#common-issues-and-solutions)
2. [YARA Rule Problems](#yara-rule-problems)
3. [Unicode and Encoding Issues](#unicode-and-encoding-issues)
4. [Memory Management Issues](#memory-management-issues)
5. [Performance Problems](#performance-problems)
6. [Cross-Platform Compatibility](#cross-platform-compatibility)
7. [Logging and Debugging](#logging-and-debugging)
8. [Advanced Troubleshooting](#advanced-troubleshooting)

## Common Issues and Solutions

### Issue: Pre-scanning process fails to start

**Symptoms:**
- Error message: "Could not load configuration"
- Process exits immediately after starting

**Possible Causes:**
1. Missing or invalid configuration file
2. Incorrect file permissions
3. Syntax error in the configuration file

**Solutions:**
1. Verify that `config.json` exists in the project root
2. Check file permissions (read access required)
3. Validate the JSON syntax of the configuration file
4. Create a new configuration file from the template if necessary

### Issue: No files are discovered for scanning

**Symptoms:**
- Message: "Found 0 files to scan"
- Process completes quickly without scanning

**Possible Causes:**
1. Incorrect target directory path
2. Target directory is empty
3. All files are excluded by extension or directory filters
4. Insufficient permissions to access the directory

**Solutions:**
1. Verify the target directory path in `config.json`
2. Check that the target directory contains files
3. Review the excluded extensions and directories in the configuration
4. Verify that the process has permission to access the directory

### Issue: YARA rules fail to compile

**Symptoms:**
- Error message: "YARA compilation error"
- Process exits with an error

**Possible Causes:**
1. Syntax errors in YARA rules
2. Missing rule files
3. Incorrect file paths in configuration
4. YARA library not installed or incompatible version

**Solutions:**
1. Check YARA rule syntax (see [YARA Rule Problems](#yara-rule-problems))
2. Verify that all rule files exist
3. Check file paths in the configuration
4. Reinstall the YARA library and yara-python package

## YARA Rule Problems

### Issue: Syntax errors in YARA rules

**Symptoms:**
- Error message: "syntax error, unexpected..."
- Error message: "unterminated string"
- Error message: "unterminated regular expression"

**Common Syntax Errors:**
1. Missing semicolons after string definitions
2. Missing commas in meta sections
3. Unclosed brackets or parentheses
4. Invalid regular expressions
5. Incorrect condition syntax

**Solutions:**

1. **Missing semicolons after string definitions:**
   ```yara
   // Incorrect
   strings:
       $a = "malicious" ascii wide
       $b = "suspicious" ascii wide
   
   // Correct
   strings:
       $a = "malicious" ascii wide;
       $b = "suspicious" ascii wide;
   ```

2. **Missing commas in meta sections:**
   ```yara
   // Incorrect
   meta:
       description = "Detects malicious behavior"
       author = "SBARDS Project"
       date = "2025-05-13"
   
   // Correct
   meta:
       description = "Detects malicious behavior",
       author = "SBARDS Project",
       date = "2025-05-13"
   ```

3. **Unclosed brackets or parentheses:**
   ```yara
   // Incorrect
   rule Example {
       strings:
           $a = "test";
       condition:
           $a and (filesize < 1MB
   
   // Correct
   rule Example {
       strings:
           $a = "test";
       condition:
           $a and (filesize < 1MB)
   }
   ```

### Issue: Rules not matching expected files

**Symptoms:**
- No matches found for files that should match
- Fewer matches than expected

**Possible Causes:**
1. Incorrect string patterns
2. Overly restrictive conditions
3. Case sensitivity issues
4. Encoding issues in the files

**Solutions:**
1. Test rules with known samples using the `yara` command-line tool
2. Add the `nocase` modifier to string definitions
3. Use `ascii wide` modifiers for cross-encoding support
4. Simplify conditions for testing, then refine

**Example of improved rule:**
```yara
rule Improved_Detection {
    meta:
        description = "Improved detection with better modifiers",
        author = "SBARDS Project"
    strings:
        $str1 = "malicious" nocase ascii wide;
        $str2 = "suspicious" nocase ascii wide;
        $str3 = /password\s*=\s*["'][^"']+["']/ nocase;
    condition:
        any of them
}
```

## Unicode and Encoding Issues

### Issue: Unicode filenames causing errors

**Symptoms:**
- Error message: "UnicodeDecodeError" or "UnicodeEncodeError"
- Process crashes when scanning files with non-ASCII characters in names

**Solutions:**

1. **Use file-based communication instead of console output:**
   ```python
   # Instead of capturing subprocess output directly
   result = subprocess.run([cmd], capture_output=True, text=True)
   
   # Use file-based communication
   output_file = "temp_output.json"
   subprocess.run([cmd, "--output-file", output_file], capture_output=False)
   with open(output_file, "r", encoding="utf-8") as f:
       result_data = json.load(f)
   ```

2. **Set proper environment variables:**
   ```python
   env_vars = dict(os.environ)
   env_vars["PYTHONIOENCODING"] = "utf-8"
   
   if os.name == 'nt':  # Windows
       env_vars["PYTHONUTF8"] = "1"
       os.system("chcp 65001 > nul")  # Set console code page to UTF-8
   
   subprocess.run([cmd], env=env_vars)
   ```

3. **Use UTF-8 encoding for all file operations:**
   ```python
   # Reading files
   with open(file_path, "r", encoding="utf-8") as f:
       content = f.read()
   
   # Writing files
   with open(file_path, "w", encoding="utf-8") as f:
       f.write(content)
   ```

4. **Handle binary files properly:**
   ```python
   # Reading binary files
   with open(file_path, "rb") as f:
       binary_content = f.read()
   
   # Try different encodings
   for encoding in ["utf-8", "latin-1", "cp1252"]:
       try:
           text_content = binary_content.decode(encoding)
           break
       except UnicodeDecodeError:
           continue
   ```

## Memory Management Issues

### Issue: Out of memory errors during scanning

**Symptoms:**
- Error message: "MemoryError"
- Process crashes when scanning large directories
- System becomes unresponsive

**Solutions:**

1. **Reduce batch size in configuration:**
   ```json
   "performance": {
       "batch_size": 10,  // Reduce from higher value
       "threads": 4       // Reduce if necessary
   }
   ```

2. **Enable adaptive threading:**
   ```json
   "performance": {
       "adaptive_threading": true
   }
   ```

3. **Implement garbage collection triggers:**
   ```python
   import gc
   
   # Check memory usage
   memory_percent = psutil.virtual_memory().percent
   if memory_percent > 85:
       gc.collect()  # Force garbage collection
   ```

4. **Limit file size for scanning:**
   ```json
   "scanner": {
       "max_file_size_mb": 50  // Reduce from higher value
   }
   ```

5. **Process files in order of size (smallest first):**
   ```python
   # Sort files by size
   files_with_size = [(f, os.path.getsize(f)) for f in files]
   sorted_files = [f for f, size in sorted(files_with_size, key=lambda x: x[1])]
   ```

## Performance Problems

### Issue: Scanning is too slow

**Symptoms:**
- Process takes a long time to complete
- CPU usage is low during scanning

**Solutions:**

1. **Increase thread count for faster systems:**
   ```json
   "performance": {
       "threads": 8  // Increase based on CPU cores
   }
   ```

2. **Optimize file discovery:**
   ```python
   # Use os.scandir instead of os.walk for better performance
   def fast_scan_directory(directory):
       files = []
       for entry in os.scandir(directory):
           if entry.is_file():
               files.append(entry.path)
           elif entry.is_dir():
               files.extend(fast_scan_directory(entry.path))
       return files
   ```

3. **Prioritize high-risk file types:**
   ```json
   "performance": {
       "priority_extensions": [".doc", ".exe", ".pdf", ".zip"]
   }
   ```

4. **Use compiled C++ scanner for performance-critical operations:**
   ```python
   # Use C++ scanner for binary files
   if file_path.endswith((".exe", ".dll", ".sys")):
       return run_cpp_scanner(rules_file, file_path)
   else:
       return run_python_scanner(rules_file, file_path)
   ```

5. **Exclude unnecessary directories:**
   ```json
   "scanner": {
       "excluded_directories": ["node_modules", ".git", "venv", "__pycache__"]
   }
   ```

## Cross-Platform Compatibility

### Issue: Path handling problems across platforms

**Symptoms:**
- File not found errors on one platform but not another
- Incorrect path separators in logs or reports

**Solutions:**

1. **Use os.path functions for path manipulation:**
   ```python
   # Instead of hardcoded separators
   path = directory + "\\" + filename  # Windows-specific
   
   # Use platform-independent approach
   path = os.path.join(directory, filename)
   ```

2. **Use pathlib for modern path handling:**
   ```python
   from pathlib import Path
   
   # Platform-independent path handling
   path = Path(directory) / filename
   if path.exists():
       with open(path, "r") as f:
           content = f.read()
   ```

3. **Normalize paths before comparison:**
   ```python
   # Normalize paths for comparison
   path1 = os.path.normpath(path1)
   path2 = os.path.normpath(path2)
   if path1 == path2:
       print("Paths are the same")
   ```

4. **Handle drive letters on Windows:**
   ```python
   if os.name == 'nt':  # Windows
       # Handle Windows drive letters
       if os.path.isabs(path) and path[1:3] == ':\\':
           # This is a Windows absolute path with drive letter
           drive, rest = os.path.splitdrive(path)
   ```

### Issue: File permission problems on Linux

**Symptoms:**
- Permission denied errors on Linux but not Windows
- Process works when run as administrator/root but not as regular user

**Solutions:**

1. **Check file permissions before access:**
   ```python
   if not os.access(file_path, os.R_OK):
       logger.warning(f"No read permission for {file_path}")
       return []
   ```

2. **Handle permission errors gracefully:**
   ```python
   try:
       with open(file_path, "r") as f:
           content = f.read()
   except PermissionError:
       logger.warning(f"Permission denied for {file_path}")
       return []
   ```

3. **Set appropriate file permissions when creating files:**
   ```python
   # Create file with specific permissions
   with open(output_file, "w") as f:
       f.write(content)
   
   # Set permissions (readable by all, writable by owner)
   os.chmod(output_file, 0o644)
   ```

## Logging and Debugging

### Issue: Insufficient logging information

**Symptoms:**
- Difficult to diagnose problems
- No clear indication of what went wrong

**Solutions:**

1. **Enable detailed logging:**
   ```json
   "logging": {
       "log_level": "debug",
       "log_to_console": true
   }
   ```

2. **Add context to log messages:**
   ```python
   logger.debug(f"Processing file {file_path} (size: {file_size_mb:.2f} MB)")
   logger.info(f"Found {len(matches)} matches in {file_path}")
   logger.error(f"Error scanning {file_path}: {e}", exc_info=True)
   ```

3. **Log system information:**
   ```python
   logger.info(f"Platform: {platform.system()} {platform.release()}")
   logger.info(f"Python version: {platform.python_version()}")
   logger.info(f"CPU cores: {os.cpu_count()}")
   logger.info(f"Memory: {psutil.virtual_memory().total / (1024**3):.2f} GB")
   ```

4. **Log performance metrics:**
   ```python
   start_time = time.time()
   # ... perform operation ...
   elapsed_time = time.time() - start_time
   logger.info(f"Operation completed in {elapsed_time:.2f} seconds")
   ```

## Advanced Troubleshooting

### Issue: Intermittent failures

**Symptoms:**
- Process sometimes succeeds, sometimes fails
- No clear pattern to failures

**Solutions:**

1. **Add retry logic for operations that might fail:**
   ```python
   def retry_operation(func, max_retries=3, delay=1):
       """Retry an operation with exponential backoff"""
       retries = 0
       while retries < max_retries:
           try:
               return func()
           except Exception as e:
               retries += 1
               if retries == max_retries:
                   raise
               logger.warning(f"Operation failed, retrying ({retries}/{max_retries}): {e}")
               time.sleep(delay * (2 ** (retries - 1)))  # Exponential backoff
   ```

2. **Implement circuit breaker pattern:**
   ```python
   class CircuitBreaker:
       def __init__(self, failure_threshold=5, reset_timeout=60):
           self.failure_count = 0
           self.failure_threshold = failure_threshold
           self.reset_timeout = reset_timeout
           self.last_failure_time = 0
           self.state = "CLOSED"  # CLOSED, OPEN, HALF-OPEN
           
       def execute(self, func, *args, **kwargs):
           if self.state == "OPEN":
               if time.time() - self.last_failure_time > self.reset_timeout:
                   self.state = "HALF-OPEN"
               else:
                   raise Exception("Circuit breaker is open")
                   
           try:
               result = func(*args, **kwargs)
               if self.state == "HALF-OPEN":
                   self.state = "CLOSED"
                   self.failure_count = 0
               return result
           except Exception as e:
               self.last_failure_time = time.time()
               self.failure_count += 1
               if self.failure_count >= self.failure_threshold:
                   self.state = "OPEN"
               raise e
   ```

3. **Implement health checks:**
   ```python
   def check_system_health():
       """Check system health before operations"""
       # Check disk space
       disk_usage = psutil.disk_usage('/')
       if disk_usage.percent > 95:
           logger.warning(f"Low disk space: {disk_usage.percent}%")
           return False
           
       # Check memory
       memory_usage = psutil.virtual_memory()
       if memory_usage.percent > 95:
           logger.warning(f"Low memory: {memory_usage.percent}%")
           return False
           
       # Check CPU load
       cpu_percent = psutil.cpu_percent(interval=1)
       if cpu_percent > 95:
           logger.warning(f"High CPU usage: {cpu_percent}%")
           return False
           
       return True
   ```

4. **Implement graceful degradation:**
   ```python
   def scan_with_fallback(file_path):
       """Scan with fallback to simpler methods if advanced scanning fails"""
       try:
           # Try advanced scanning first
           return advanced_scan(file_path)
       except MemoryError:
           logger.warning(f"Memory error during advanced scan, falling back to basic scan for {file_path}")
           return basic_scan(file_path)
       except Exception as e:
           logger.error(f"Error during advanced scan: {e}")
           return basic_scan(file_path)
   ```
