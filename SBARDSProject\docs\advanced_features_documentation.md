# SBARDS Project - Advanced Features Documentation

This document provides detailed information about the advanced features added to the SBARDS Project, including ransomware detection, download monitoring, and hash-based optimization.

## Table of Contents

1. [Advanced Ransomware Detection](#1-advanced-ransomware-detection)
   - [Permission-Based Detection](#11-permission-based-detection)
   - [Shadow Copy Detection](#12-shadow-copy-detection)
   - [Mass File Permission Changes](#13-mass-file-permission-changes)
   - [Privilege Escalation Detection](#14-privilege-escalation-detection)

2. [Ransomware Test Files](#2-ransomware-test-files)
   - [Test File Structure](#21-test-file-structure)
   - [Using Test Files](#22-using-test-files)

3. [Download Monitoring System](#3-download-monitoring-system)
   - [Supported Download Sources](#31-supported-download-sources)
   - [Configuration Options](#32-configuration-options)
   - [Running the Monitor](#33-running-the-monitor)

4. [Hash-Based Optimization](#4-hash-based-optimization)
   - [How It Works](#41-how-it-works)
   - [Database Management](#42-database-management)
   - [Performance Benefits](#43-performance-benefits)

5. [Integration with Orchestrator](#5-integration-with-orchestrator)
   - [Command-Line Options](#51-command-line-options)
   - [API Changes](#52-api-changes)

---

## 1. Advanced Ransomware Detection

The SBARDS Project now includes advanced ransomware detection capabilities through specialized YARA rules that focus on behavioral patterns typical of ransomware.

### 1.1 Permission-Based Detection

Ransomware often manipulates file permissions before encryption to ensure it has full access to files and to prevent security software from intervening.

**Key Detection Points:**
- Modification of file security attributes
- Changes to access control lists (ACLs)
- Permission elevation on multiple files
- Combination of permission changes with encryption functions

**Implementation:**
- Located in `rules/ransomware_advanced_rules.yar`
- Rule name: `Ransomware_Permission_Manipulation`

**Example Rule Snippet:**
```yara
rule Ransomware_Permission_Manipulation {
    meta:
        description = "Detects ransomware that manipulates file permissions before encryption"
        author = "SBARDS Project"
        date = "2023-08-15"
        category = "ransomware"
        severity = "critical"
        compatibility = "python,cpp"

    strings:
        // Windows permission APIs
        $win_perm1 = "SetFileSecurity" ascii wide
        $win_perm2 = "SetNamedSecurityInfo" ascii wide
        // ...

    condition:
        (2 of ($win_perm*) or 2 of ($unix_perm*)) and
        (3 of ($file_op*)) and
        (1 of ($encrypt*))
}
```

### 1.2 Shadow Copy Detection

Ransomware typically attempts to delete Volume Shadow Copies to prevent recovery of encrypted files.

**Key Detection Points:**
- Commands to delete shadow copies
- Use of vssadmin, wmic, or bcdedit
- Disabling of recovery options

**Implementation:**
- Located in `rules/ransomware_advanced_rules.yar`
- Rule name: `Ransomware_Shadow_Copy_Deletion`

### 1.3 Mass File Permission Changes

This detection focuses on ransomware that changes permissions on many files at once.

**Key Detection Points:**
- File enumeration followed by permission changes
- Loop constructs for iterating through files
- Focus on common document file extensions

**Implementation:**
- Located in `rules/ransomware_advanced_rules.yar`
- Rule name: `Ransomware_Mass_File_Permission_Changes`

### 1.4 Privilege Escalation Detection

Detects ransomware that attempts to escalate privileges before encryption.

**Key Detection Points:**
- Use of privilege escalation APIs
- Requesting of specific privileges like SeDebugPrivilege
- Combination with encryption functions

**Implementation:**
- Located in `rules/ransomware_advanced_rules.yar`
- Rule name: `Ransomware_Privilege_Escalation_Before_Encryption`

## 2. Ransomware Test Files

Test files have been added to safely test the ransomware detection capabilities without using actual malicious code.

### 2.1 Test File Structure

The test files are located in the `samples/test_ransomware/` directory:

- `permission_test.txt`: Contains strings that trigger permission-based detection rules
- `shadow_copy_test.txt`: Contains strings related to shadow copy deletion
- `bitcoin_payment.html`: Simulates a ransomware payment page
- `encrypted_file.docx.locked`: Simulates an encrypted file with a ransomware extension

### 2.2 Using Test Files

These files can be used to verify that the YARA rules are working correctly:

```bash
python run_prescanning.py --target samples/test_ransomware
```

The scan should identify these files as potential ransomware based on the strings they contain, without any risk of actual infection.

## 3. Download Monitoring System

The download monitoring system automatically scans files as they are downloaded from browsers or WhatsApp.

### 3.1 Supported Download Sources

The system monitors the following locations:

**Windows:**
- User's Downloads folder
- Chrome download directory
- Edge download directory
- Firefox download directories
- WhatsApp download directories

**Linux:**
- User's Downloads folder
- Chrome/Chromium download directories
- Firefox download directories
- WhatsApp download directories

### 3.2 Configuration Options

To enable download monitoring, add the following to your `config.json`:

```json
"features": {
    "monitor_downloads": true,
    "hash_optimization": true
}
```

### 3.3 Running the Monitor

The download monitor is fully integrated into the pre-scanning phase and can be run in several ways:

**As part of the normal pre-scanning process:**
```bash
python run_prescanning.py
```
The download monitor will automatically start if enabled in the configuration.

**Download monitoring only mode:**
```bash
python run_prescanning.py --download-monitor-only
```
This runs only the download monitoring component of the pre-scanning phase.

**Disable download monitoring during pre-scanning:**
```bash
python run_prescanning.py --no-download-monitor
```
This runs the pre-scanning phase without starting the download monitor.

The monitor runs in the background and automatically scans new downloads as they appear.

## 4. Hash-Based Optimization

Hash-based optimization prevents rescanning of previously scanned files, improving performance.

### 4.1 How It Works

1. When a file is scanned, its SHA-256 hash is calculated
2. The hash and scan results are stored in a database
3. When a file is encountered again, its hash is checked against the database
4. If the hash exists in the database, the previous scan results are used instead of rescanning

### 4.2 Database Management

The hash database is automatically managed:

- Stored in `output/hash_database.json`
- Cleaned up periodically (default: every 24 hours)
- Entries older than 30 days are removed
- Maximum entries can be configured (default: 10,000)

### 4.3 Performance Benefits

Hash-based optimization provides several benefits:

- Reduces CPU usage by avoiding redundant scans
- Speeds up scanning of directories with duplicate files
- Provides immediate results for previously scanned files
- Particularly effective for download monitoring where the same file might be downloaded multiple times

## 5. Integration with Pre-scanning Phase

The download monitoring and hash-based optimization features are fully integrated with the SBARDS pre-scanning phase.

### 5.1 Command-Line Options

The pre-scanning script now supports the following command-line options:

```bash
# Run pre-scanning with all components including download monitoring
python run_prescanning.py

# Run only the download monitoring component of pre-scanning
python run_prescanning.py --download-monitor-only

# Run pre-scanning without download monitoring
python run_prescanning.py --no-download-monitor

# Skip the legacy scan component
python run_prescanning.py --skip-legacy

# Skip the C++ scanner test
python run_prescanning.py --skip-cpp-test

# Specify a different configuration file
python run_prescanning.py --config custom_config.json
```

### 5.2 API Changes

The Orchestrator class has been extended with the following methods:

- `start_download_monitoring()`: Starts the download monitoring service
- `stop_download_monitoring()`: Stops the download monitoring service

Example usage in custom scripts:

```python
from scanner_core.python.orchestrator import Orchestrator

# Initialize the orchestrator
orchestrator = Orchestrator("config.json")

# Start download monitoring as part of pre-scanning
orchestrator.start_download_monitoring()

# ... do other tasks ...

# Stop download monitoring when done
orchestrator.stop_download_monitoring()
```

---

## Conclusion

These advanced features significantly enhance the SBARDS Project's capabilities for detecting ransomware and automatically monitoring downloaded files. The hash-based optimization improves performance by avoiding redundant scans, making the system more efficient for continuous monitoring scenarios.

All these features are fully integrated into the pre-scanning phase of the SBARDS Project. The download monitoring is not a separate component but an integral part of the pre-scanning process, which can be enabled or disabled as needed. This integration ensures a cohesive security approach where all components work together seamlessly.

The pre-scanning phase now provides a comprehensive security solution that:
1. Scans directories for potential threats
2. Monitors downloads in real-time
3. Optimizes performance through hash-based caching
4. Detects sophisticated ransomware through advanced rules

For more information on using these features, refer to the main README.md file and the other documentation in the docs directory.
