"""
Logging middleware for the SBARDS Backend API.

This module provides logging middleware for the SBARDS Backend API.
"""

import time
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGI<PERSON>pp

from ..core.logging import logger


class LoggingMiddleware(BaseHTTPMiddleware):
    """Logging middleware."""
    
    def __init__(self, app: ASGIApp):
        """
        Initialize logging middleware.
        
        Args:
            app (ASGIApp): ASGI application.
        """
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Dispatch request with logging.
        
        Args:
            request (Request): FastAPI request.
            call_next (Callable): Next middleware or route handler.
            
        Returns:
            Response: FastAPI response.
        """
        # Skip logging for certain paths
        if self._should_skip_logging(request.url.path):
            return await call_next(request)
        
        # Log request
        start_time = time.time()
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("User-Agent", "unknown")
        
        logger.debug(f"Request: {request.method} {request.url.path} - Client: {client_ip} - User-Agent: {user_agent}")
        
        # Process request
        response = await call_next(request)
        
        # Log response
        process_time = (time.time() - start_time) * 1000
        logger.debug(f"Response: {request.method} {request.url.path} - Status: {response.status_code} - Time: {process_time:.2f}ms")
        
        return response
    
    def _should_skip_logging(self, path: str) -> bool:
        """
        Check if logging should be skipped for a path.
        
        Args:
            path (str): Request path.
            
        Returns:
            bool: True if logging should be skipped, False otherwise.
        """
        # Skip logging for static files
        skip_paths = ["/static/"]
        return any(path.startswith(skip_path) for skip_path in skip_paths)
