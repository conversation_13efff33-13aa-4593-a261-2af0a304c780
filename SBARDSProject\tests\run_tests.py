#!/usr/bin/env python3
"""
Test Runner for SBARDS

This script runs the tests for the SBARDS project.
"""

import os
import sys
import unittest
import argparse

def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Run tests for SBARDS")
    parser.add_argument("--unit", action="store_true", help="Run unit tests")
    parser.add_argument("--integration", action="store_true", help="Run integration tests")
    parser.add_argument("--all", action="store_true", help="Run all tests")
    parser.add_argument("--verbose", action="store_true", help="Run tests in verbose mode")
    return parser.parse_args()

def run_unit_tests(verbose=False):
    """Run unit tests."""
    print("Running unit tests...")
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.defaultTestLoader.loadTestsFromName("test_config"))
    test_suite.addTest(unittest.defaultTestLoader.loadTestsFromName("test_utils"))
    test_suite.addTest(unittest.defaultTestLoader.loadTestsFromName("test_orchestrator"))
    test_suite.addTest(unittest.defaultTestLoader.loadTestsFromName("test_monitor_manager"))
    test_suite.addTest(unittest.defaultTestLoader.loadTestsFromName("test_phase_coordinator"))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2 if verbose else 1)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()

def run_integration_tests(verbose=False):
    """Run integration tests."""
    print("Running integration tests...")
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.defaultTestLoader.loadTestsFromName("test_api"))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2 if verbose else 1)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()

def run_all_tests(verbose=False):
    """Run all tests."""
    print("Running all tests...")
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.defaultTestLoader.discover("tests", pattern="test_*.py"))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2 if verbose else 1)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()

def main():
    """Main function."""
    # Parse arguments
    args = parse_args()
    
    # Add tests directory to Python path
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    # Run tests
    if args.unit:
        success = run_unit_tests(args.verbose)
    elif args.integration:
        success = run_integration_tests(args.verbose)
    elif args.all:
        success = run_all_tests(args.verbose)
    else:
        # Default to running unit tests
        success = run_unit_tests(args.verbose)
    
    # Return exit code
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
