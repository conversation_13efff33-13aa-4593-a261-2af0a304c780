"""
API v1 router for the SBARDS Backend API.

This module provides the main API router for version 1 of the SBARDS Backend API.
"""

from fastapi import APIRouter

from ..endpoints import reports, files, stats
from .endpoints import scan, websocket

# Create API router
api_router = APIRouter()

# Include routers from endpoints
api_router.include_router(reports.router, prefix="/reports", tags=["reports"])
api_router.include_router(files.router, prefix="/files", tags=["files"])
api_router.include_router(stats.router, prefix="/stats", tags=["stats"])
api_router.include_router(scan.router, prefix="/scan", tags=["scan"])
api_router.include_router(websocket.router, prefix="/ws", tags=["websocket"])
