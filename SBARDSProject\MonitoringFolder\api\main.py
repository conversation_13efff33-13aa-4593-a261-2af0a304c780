"""
Main API for SBARDS

This module provides the main FastAPI application for the SBARDS project.
"""

import os
import logging
from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from typing import Dict, List, Any, Optional

# Create FastAPI app
app = FastAPI(
    title="SBARDS API",
    description="API for the SBARDS project",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Import routers
from api.routers import prescanning, monitoring

# Add routers
app.include_router(prescanning.router, prefix="/api/prescanning", tags=["prescanning"])
app.include_router(monitoring.router, prefix="/api/monitoring", tags=["monitoring"])

# Mount static files
try:
    app.mount("/dashboard", StaticFiles(directory="ui/dashboard", html=True), name="dashboard")
except Exception as e:
    logging.warning(f"Could not mount dashboard: {e}")

# Root endpoint
@app.get("/", tags=["root"])
async def root():
    """
    Root endpoint.
    
    Returns:
        Dict[str, Any]: API information
    """
    return {
        "name": "SBARDS API",
        "version": "1.0.0",
        "description": "API for the SBARDS project",
        "endpoints": {
            "prescanning": "/api/prescanning",
            "monitoring": "/api/monitoring",
            "dashboard": "/dashboard"
        }
    }
