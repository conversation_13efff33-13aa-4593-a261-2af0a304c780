"""
Configuration Loader for SBARDS

This module provides functionality for loading and managing the SBARDS configuration.
It supports loading configuration from files and environment variables, with
environment variables taking precedence over file-based configuration.
"""

import os
import json
import logging
from typing import Dict, Any, Optional

from utils.config_validator import validate_config, CONFIG_SCHEMA

logger = logging.getLogger("SBARDS.ConfigLoader")

# Environment variable prefix for SBARDS configuration
ENV_PREFIX = "SBARDS_"

def _parse_env_value(value: str) -> Any:
    """
    Parse an environment variable value into the appropriate Python type.
    
    Args:
        value (str): Environment variable value
        
    Returns:
        Any: Parsed value
    """
    # Try to parse as JSON
    try:
        return json.loads(value)
    except json.JSONDecodeError:
        # If not valid JSON, return as string
        return value

def _get_env_config() -> Dict[str, Any]:
    """
    Get configuration from environment variables.
    
    Environment variables should be prefixed with SBARDS_ and use double underscores
    to indicate nested configuration. For example:
    
    SBARDS_MONITORING__INTERVAL_SECONDS=5
    
    Returns:
        Dict[str, Any]: Configuration dictionary from environment variables
    """
    env_config = {}
    
    for key, value in os.environ.items():
        if key.startswith(ENV_PREFIX):
            # Remove prefix and split by double underscore
            config_key = key[len(ENV_PREFIX):]
            parts = config_key.split("__")
            
            # Build nested dictionary
            current = env_config
            for i, part in enumerate(parts[:-1]):
                part = part.lower()
                if part not in current:
                    current[part] = {}
                current = current[part]
                
            # Set value
            current[parts[-1].lower()] = _parse_env_value(value)
    
    return env_config

def _merge_configs(base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
    """
    Merge two configuration dictionaries, with override taking precedence.
    
    Args:
        base (Dict[str, Any]): Base configuration
        override (Dict[str, Any]): Override configuration
        
    Returns:
        Dict[str, Any]: Merged configuration
    """
    result = base.copy()
    
    for key, value in override.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            # Recursively merge dictionaries
            result[key] = _merge_configs(result[key], value)
        else:
            # Override value
            result[key] = value
            
    return result

def load_config(config_path: str = "config.json", validate: bool = True) -> Dict[str, Any]:
    """
    Load configuration from file and environment variables.
    
    Args:
        config_path (str): Path to the configuration file
        validate (bool): Whether to validate the configuration
        
    Returns:
        Dict[str, Any]: Configuration dictionary
        
    Raises:
        ValueError: If the configuration is invalid
        FileNotFoundError: If the configuration file does not exist
    """
    # Load configuration from file
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
    try:
        with open(config_path, "r") as f:
            file_config = json.load(f)
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON in configuration file: {e}")
        
    # Load configuration from environment variables
    env_config = _get_env_config()
    
    # Merge configurations
    config = _merge_configs(file_config, env_config)
    
    # Validate configuration
    if validate:
        is_valid, errors = validate_config(config)
        if not is_valid:
            error_message = "Configuration validation failed:\n" + "\n".join(f"- {error}" for error in errors)
            logger.error(error_message)
            raise ValueError(error_message)
    
    # Create required directories
    if "paths" in config:
        for dir_key in ["output_dir", "log_dir", "temp_dir"]:
            if dir_key in config["paths"]:
                os.makedirs(config["paths"][dir_key], exist_ok=True)
    
    return config

def get_config_value(config: Dict[str, Any], path: str, default: Any = None) -> Any:
    """
    Get a value from the configuration using a dot-separated path.
    
    Args:
        config (Dict[str, Any]): Configuration dictionary
        path (str): Dot-separated path to the value (e.g., "monitoring.interval_seconds")
        default (Any): Default value to return if the path does not exist
        
    Returns:
        Any: The value at the specified path, or the default value if not found
    """
    parts = path.split(".")
    current = config
    
    for part in parts:
        if isinstance(current, dict) and part in current:
            current = current[part]
        else:
            return default
            
    return current

def get_sensitive_config(config: Dict[str, Any], path: str, env_var: Optional[str] = None, default: Any = None) -> Any:
    """
    Get a sensitive configuration value, prioritizing environment variables.
    
    Args:
        config (Dict[str, Any]): Configuration dictionary
        path (str): Dot-separated path to the value in the configuration
        env_var (Optional[str]): Environment variable name (without prefix)
        default (Any): Default value to return if not found
        
    Returns:
        Any: The sensitive configuration value
    """
    # Try to get from environment variable
    if env_var:
        env_value = os.environ.get(f"{ENV_PREFIX}{env_var}")
        if env_value is not None:
            return _parse_env_value(env_value)
    
    # Fall back to configuration file
    return get_config_value(config, path, default)
