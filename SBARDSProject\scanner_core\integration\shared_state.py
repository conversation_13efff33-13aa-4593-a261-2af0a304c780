"""
Shared State for SBARDS

This module provides shared state management between the pre-inspection and monitoring layers.
"""

import os
import time
import json
import logging
import threading
from typing import Dict, List, Any, Optional, Set
from datetime import datetime
from collections import deque

class SharedState:
    """
    Manages shared state between the pre-inspection and monitoring layers.
    
    This class provides thread-safe access to shared data including:
    1. Detection results from pre-inspection layer
    2. Alerts from monitoring layer
    3. Fast-track requests
    4. Priority information
    5. Shared configuration
    """
    
    def __init__(self):
        """Initialize the shared state."""
        self.logger = logging.getLogger("SBARDS.SharedState")
        
        # Thread safety
        self._lock = threading.RLock()
        
        # Pre-inspection detections
        self._pre_inspection_detections = deque(maxlen=1000)
        self._processed_pre_inspection_ids = set()
        
        # Monitoring alerts
        self._monitoring_alerts = deque(maxlen=1000)
        self._processed_alert_ids = set()
        
        # Fast-track requests
        self._fast_track_requests = deque(maxlen=100)
        
        # Priority information
        self._priority_files = {}  # file_path -> priority_level
        self._priority_processes = {}  # process_id -> priority_level
        
        # Shared configuration
        self._shared_config = {}
        
        # File state cache
        self._file_state_cache = {}  # file_path -> state_info
        
        # Process state cache
        self._process_state_cache = {}  # process_id -> state_info
        
        self.logger.info("Shared State initialized")
        
    def add_pre_inspection_detection(self, detection: Dict[str, Any]):
        """
        Add a detection from the pre-inspection layer.
        
        Args:
            detection (Dict[str, Any]): Detection information
        """
        with self._lock:
            # Add timestamp if not present
            if "timestamp" not in detection:
                detection["timestamp"] = time.time()
                
            # Add unique ID if not present
            if "id" not in detection:
                detection["id"] = f"pre_{int(detection['timestamp'] * 1000)}_{len(self._pre_inspection_detections)}"
                
            self._pre_inspection_detections.append(detection)
            self.logger.debug(f"Added pre-inspection detection: {detection.get('id')}")
            
    def add_monitoring_alert(self, alert: Dict[str, Any]):
        """
        Add an alert from the monitoring layer.
        
        Args:
            alert (Dict[str, Any]): Alert information
        """
        with self._lock:
            # Add timestamp if not present
            if "timestamp" not in alert:
                alert["timestamp"] = time.time()
                
            # Add unique ID if not present
            if "id" not in alert:
                alert["id"] = f"mon_{int(alert['timestamp'] * 1000)}_{len(self._monitoring_alerts)}"
                
            self._monitoring_alerts.append(alert)
            self.logger.debug(f"Added monitoring alert: {alert.get('id')}")
            
    def add_fast_track_request(self, request: Dict[str, Any]):
        """
        Add a fast-track request.
        
        Args:
            request (Dict[str, Any]): Fast-track request information
        """
        with self._lock:
            # Add timestamp if not present
            if "timestamp" not in request:
                request["timestamp"] = time.time()
                
            # Add unique ID if not present
            if "id" not in request:
                request["id"] = f"ft_{int(request['timestamp'] * 1000)}_{len(self._fast_track_requests)}"
                
            self._fast_track_requests.append(request)
            self.logger.debug(f"Added fast-track request: {request.get('id')}")
            
    def get_new_pre_inspection_detections(self) -> List[Dict[str, Any]]:
        """
        Get new (unprocessed) pre-inspection detections.
        
        Returns:
            List[Dict[str, Any]]: List of new detections
        """
        with self._lock:
            new_detections = []
            for detection in self._pre_inspection_detections:
                if detection.get("id") not in self._processed_pre_inspection_ids:
                    new_detections.append(detection)
            return new_detections
            
    def get_new_monitoring_alerts(self) -> List[Dict[str, Any]]:
        """
        Get new (unprocessed) monitoring alerts.
        
        Returns:
            List[Dict[str, Any]]: List of new alerts
        """
        with self._lock:
            new_alerts = []
            for alert in self._monitoring_alerts:
                if alert.get("id") not in self._processed_alert_ids:
                    new_alerts.append(alert)
            return new_alerts
            
    def get_fast_track_requests(self) -> List[Dict[str, Any]]:
        """
        Get all fast-track requests.
        
        Returns:
            List[Dict[str, Any]]: List of fast-track requests
        """
        with self._lock:
            return list(self._fast_track_requests)
            
    def mark_pre_inspection_detections_as_processed(self):
        """Mark all pre-inspection detections as processed."""
        with self._lock:
            for detection in self._pre_inspection_detections:
                if "id" in detection:
                    self._processed_pre_inspection_ids.add(detection["id"])
                    
    def mark_monitoring_alerts_as_processed(self):
        """Mark all monitoring alerts as processed."""
        with self._lock:
            for alert in self._monitoring_alerts:
                if "id" in alert:
                    self._processed_alert_ids.add(alert["id"])
                    
    def clear_fast_track_requests(self):
        """Clear all fast-track requests."""
        with self._lock:
            self._fast_track_requests.clear()
            
    def set_file_priority(self, file_path: str, priority_level: int):
        """
        Set priority level for a file.
        
        Args:
            file_path (str): Path to the file
            priority_level (int): Priority level (higher is more important)
        """
        with self._lock:
            self._priority_files[file_path] = priority_level
            
    def get_file_priority(self, file_path: str) -> int:
        """
        Get priority level for a file.
        
        Args:
            file_path (str): Path to the file
            
        Returns:
            int: Priority level (0 if not set)
        """
        with self._lock:
            return self._priority_files.get(file_path, 0)
            
    def set_process_priority(self, process_id: str, priority_level: int):
        """
        Set priority level for a process.
        
        Args:
            process_id (str): Process ID
            priority_level (int): Priority level (higher is more important)
        """
        with self._lock:
            self._priority_processes[process_id] = priority_level
            
    def get_process_priority(self, process_id: str) -> int:
        """
        Get priority level for a process.
        
        Args:
            process_id (str): Process ID
            
        Returns:
            int: Priority level (0 if not set)
        """
        with self._lock:
            return self._priority_processes.get(process_id, 0)
            
    def update_shared_config(self, config: Dict[str, Any]):
        """
        Update shared configuration.
        
        Args:
            config (Dict[str, Any]): Configuration dictionary
        """
        with self._lock:
            self._shared_config.update(config)
            
    def get_shared_config(self) -> Dict[str, Any]:
        """
        Get shared configuration.
        
        Returns:
            Dict[str, Any]: Shared configuration dictionary
        """
        with self._lock:
            return self._shared_config.copy()
            
    def update_file_state(self, file_path: str, state_info: Dict[str, Any]):
        """
        Update file state information.
        
        Args:
            file_path (str): Path to the file
            state_info (Dict[str, Any]): State information
        """
        with self._lock:
            if file_path in self._file_state_cache:
                self._file_state_cache[file_path].update(state_info)
            else:
                self._file_state_cache[file_path] = state_info
                
    def get_file_state(self, file_path: str) -> Optional[Dict[str, Any]]:
        """
        Get file state information.
        
        Args:
            file_path (str): Path to the file
            
        Returns:
            Optional[Dict[str, Any]]: State information or None if not found
        """
        with self._lock:
            return self._file_state_cache.get(file_path)
