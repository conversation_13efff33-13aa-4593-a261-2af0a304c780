"""
Process Monitor for SBARDS

This module provides process monitoring for the monitoring phase of the SBARDS project.
"""

import os
import time
import logging
import threading
import platform
from typing import Dict, List, Any, Optional, Set

class ProcessMonitor:
    """
    Process monitor for the monitoring phase.

    This class provides mechanisms for:
    1. Monitoring processes
    2. Detecting suspicious processes
    3. Tracking process relationships
    4. Monitoring process behavior
    """

    def __init__(self, config: Dict[str, Any], alert_manager):
        """
        Initialize process monitor.

        Args:
            config (Dict[str, Any]): Process monitoring configuration
            alert_manager: Alert manager
        """
        self.config = config
        self.enabled = config.get("enabled", True)
        self.suspicious_process_patterns = config.get("suspicious_process_patterns", [])
        self.check_interval_seconds = config.get("check_interval_seconds", 5)
        self.max_history_entries = config.get("max_history_entries", 2000)
        self.memory_usage_threshold_percent = config.get("memory_usage_threshold_percent", 80)
        self.cpu_usage_threshold_percent = config.get("cpu_usage_threshold_percent", 90)
        self.track_parent_child = config.get("track_parent_child", True)

        # Set up logging
        self.logger = logging.getLogger("ProcessMonitor")

        # Set alert manager
        self.alert_manager = alert_manager

        # Initialize process storage
        self.processes = {}
        self.process_history = []
        self.monitored_processes = set()
        self.process_count = 0

        # Initialize monitoring state
        self.is_running_flag = False
        self.stop_event = threading.Event()
        self.monitoring_thread = None

    def start(self) -> bool:
        """
        Start process monitoring.

        Returns:
            bool: True if started successfully, False otherwise
        """
        if not self.enabled:
            self.logger.warning("Process monitoring is not enabled in configuration")
            return False

        if self.is_running_flag:
            self.logger.warning("Process monitoring is already running")
            return True

        self.logger.info("Starting process monitoring")

        try:
            # Reset stop event
            self.stop_event.clear()

            # Start monitoring thread
            self.monitoring_thread = threading.Thread(
                target=self._monitoring_loop,
                daemon=True
            )
            self.monitoring_thread.start()

            self.is_running_flag = True
            self.logger.info("Process monitoring started")

            return True

        except Exception as e:
            self.logger.error(f"Error starting process monitoring: {e}")
            return False

    def stop(self) -> bool:
        """
        Stop process monitoring.

        Returns:
            bool: True if stopped successfully, False otherwise
        """
        if not self.is_running_flag:
            self.logger.warning("Process monitoring is not running")
            return True

        self.logger.info("Stopping process monitoring")

        try:
            # Set stop event
            self.stop_event.set()

            # Wait for monitoring thread to stop
            if self.monitoring_thread:
                self.monitoring_thread.join(timeout=5.0)

            self.is_running_flag = False
            self.logger.info("Process monitoring stopped")

            return True

        except Exception as e:
            self.logger.error(f"Error stopping process monitoring: {e}")
            return False

    def is_running(self) -> bool:
        """
        Check if process monitoring is running.

        Returns:
            bool: True if running, False otherwise
        """
        return self.is_running_flag

    def _monitoring_loop(self) -> None:
        """Process monitoring loop."""
        while not self.stop_event.is_set():
            try:
                # Get current processes
                self._update_processes()

                # Check for suspicious processes
                self._check_suspicious_processes()

                # Check resource usage
                self._check_resource_usage()

                # Wait for next check
                time.sleep(self.check_interval_seconds)

            except Exception as e:
                self.logger.error(f"Error in process monitoring loop: {e}")
                # Wait a bit before retrying
                time.sleep(1.0)

    def _update_processes(self) -> None:
        """Update process list."""
        try:
            import psutil

            # Get current processes
            current_processes = {}

            for proc in psutil.process_iter(['pid', 'name', 'username', 'cmdline', 'create_time', 'ppid']):
                try:
                    # Get process info
                    proc_info = proc.info
                    pid = proc_info['pid']

                    # Add to current processes
                    current_processes[pid] = {
                        "pid": pid,
                        "name": proc_info['name'],
                        "username": proc_info['username'],
                        "cmdline": proc_info['cmdline'],
                        "create_time": proc_info['create_time'],
                        "parent_pid": proc_info['ppid'],
                        "timestamp": time.time()
                    }

                    # Check if process is new
                    if pid not in self.processes:
                        # Add to process history
                        self.process_history.append({
                            "event": "created",
                            "pid": pid,
                            "name": proc_info['name'],
                            "timestamp": time.time()
                        })

                        # Keep history size limited
                        if len(self.process_history) > self.max_history_entries:
                            self.process_history.pop(0)
                except Exception as e:
                    self.logger.debug(f"Error getting process info for PID {proc.pid}: {e}")

            # Check for terminated processes
            for pid in list(self.processes.keys()):
                if pid not in current_processes:
                    # Add to process history
                    self.process_history.append({
                        "event": "terminated",
                        "pid": pid,
                        "name": self.processes[pid]["name"],
                        "timestamp": time.time()
                    })

                    # Keep history size limited
                    if len(self.process_history) > self.max_history_entries:
                        self.process_history.pop(0)

                    # Remove from processes
                    del self.processes[pid]

                    # Remove from monitored processes
                    self.monitored_processes.discard(pid)

            # Update processes
            self.processes = current_processes
            self.process_count = len(self.processes)

        except ImportError:
            self.logger.error("psutil module not found. Process monitoring requires psutil.")
            self.stop()
        except Exception as e:
            self.logger.error(f"Error updating processes: {e}")

    def _check_suspicious_processes(self) -> None:
        """Check for suspicious processes."""
        for pid, proc_info in self.processes.items():
            # Check process name against suspicious patterns
            process_name = proc_info["name"].lower()
            cmdline = " ".join(proc_info.get("cmdline", [])).lower()

            for pattern in self.suspicious_process_patterns:
                if pattern.lower() in process_name or pattern.lower() in cmdline:
                    # Create alert
                    self.alert_manager.add_alert(
                        level="warning",
                        source="process_monitor",
                        message=f"Suspicious process detected: {process_name} (PID: {pid})",
                        details={
                            "pid": pid,
                            "name": proc_info["name"],
                            "cmdline": proc_info.get("cmdline", []),
                            "username": proc_info.get("username", ""),
                            "pattern": pattern
                        }
                    )

    def _check_resource_usage(self) -> None:
        """Check resource usage of monitored processes."""
        try:
            import psutil

            # Make a copy of the monitored_processes set to avoid "set changed size during iteration" error
            monitored_pids = list(self.monitored_processes)

            for pid in monitored_pids:
                if pid in self.processes:
                    try:
                        # Get process
                        proc = psutil.Process(pid)

                        # Get CPU and memory usage
                        cpu_percent = proc.cpu_percent(interval=0.1)
                        memory_percent = proc.memory_percent()

                        # Check thresholds
                        if cpu_percent > self.cpu_usage_threshold_percent:
                            # Create alert
                            self.alert_manager.add_alert(
                                level="warning",
                                source="process_monitor",
                                message=f"High CPU usage detected: {self.processes[pid]['name']} (PID: {pid}, CPU: {cpu_percent:.1f}%)",
                                details={
                                    "pid": pid,
                                    "name": self.processes[pid]["name"],
                                    "cpu_percent": cpu_percent,
                                    "memory_percent": memory_percent
                                }
                            )

                        if memory_percent > self.memory_usage_threshold_percent:
                            # Create alert
                            self.alert_manager.add_alert(
                                level="warning",
                                source="process_monitor",
                                message=f"High memory usage detected: {self.processes[pid]['name']} (PID: {pid}, Memory: {memory_percent:.1f}%)",
                                details={
                                    "pid": pid,
                                    "name": self.processes[pid]["name"],
                                    "cpu_percent": cpu_percent,
                                    "memory_percent": memory_percent
                                }
                            )
                    except psutil.NoSuchProcess:
                        # Process no longer exists, remove from monitored processes
                        self.monitored_processes.discard(pid)
                        self.logger.debug(f"Process {pid} no longer exists, removing from monitored processes")
                    except Exception as e:
                        self.logger.debug(f"Error checking resource usage for PID {pid}: {e}")
        except Exception as e:
            self.logger.error(f"Error checking resource usage: {e}")

    def get_current_processes(self) -> List[Dict[str, Any]]:
        """
        Get current processes.

        Returns:
            List[Dict[str, Any]]: Current processes
        """
        return list(self.processes.values())

    def get_process_count(self) -> int:
        """
        Get process count.

        Returns:
            int: Process count
        """
        return self.process_count

    def monitor_process(self, pid: int, priority: int = 5) -> None:
        """
        Monitor a specific process.

        Args:
            pid (int): Process ID
            priority (int, optional): Priority level
        """
        if not self.is_running_flag:
            self.logger.warning("Process monitoring is not running")
            return

        # Add to monitored processes
        self.monitored_processes.add(pid)

        self.logger.info(f"Monitoring process {pid} with priority {priority}")

        # Check if process exists
        if pid in self.processes:
            self.logger.info(f"Process {pid} ({self.processes[pid]['name']}) is being monitored")
