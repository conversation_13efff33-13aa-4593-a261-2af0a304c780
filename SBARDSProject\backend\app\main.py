"""
SBARDS Backend API

This module provides a FastAPI backend for the SBARDS project, offering:
- Collection of scan reports
- Integration with VirusTotal for file hash verification
- Centralized logging and reporting
- REST API for UIs, dashboards, and further integration
"""

import os
import json
import hashlib
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

import requests
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks, status, File, UploadFile, Form, Body
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, RedirectResponse
from pydantic import BaseModel, Field
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text, Boolean, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session, relationship

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("backend_api.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("SBARDS.Backend")

# Initialize FastAPI app
app = FastAPI(
    title="SBARDS Backend API",
    description="""
    Backend API for the Security-Based Automated Ransomware Detection System.

    This API provides:
    - Collection of scan reports from SBARDS scanners
    - Integration with VirusTotal for file hash verification
    - Centralized logging and reporting
    - REST API for UIs, dashboards, and further integration
    """,
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    openapi_url="/api/openapi.json",
    openapi_tags=[
        {
            "name": "reports",
            "description": "Operations with scan reports"
        },
        {
            "name": "files",
            "description": "Operations with files and VirusTotal integration"
        },
        {
            "name": "stats",
            "description": "Statistics and metrics"
        },
        {
            "name": "dashboard",
            "description": "Dashboard and UI"
        }
    ]
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add middleware for request logging
@app.middleware("http")
async def log_requests(request, call_next):
    start_time = datetime.utcnow()
    response = await call_next(request)
    process_time = (datetime.utcnow() - start_time).total_seconds() * 1000
    logger.debug(f"Request: {request.method} {request.url.path} - Status: {response.status_code} - Time: {process_time:.2f}ms")
    return response

# Database setup
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./sbards.db")
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# VirusTotal API configuration
VIRUSTOTAL_API_KEY = os.getenv("VIRUSTOTAL_API_KEY", "")
VIRUSTOTAL_API_URL = "https://www.virustotal.com/api/v3"

# Database models
class ScanReport(Base):
    __tablename__ = "scan_reports"

    id = Column(Integer, primary_key=True, index=True)
    scan_id = Column(String, unique=True, index=True)
    timestamp = Column(DateTime, default=datetime.utcnow)
    scan_path = Column(String)
    files_scanned = Column(Integer)
    threats_found = Column(Integer)
    report_path = Column(String)
    report_content = Column(Text)

    file_results = relationship("FileResult", back_populates="scan_report")

class FileResult(Base):
    __tablename__ = "file_results"

    id = Column(Integer, primary_key=True, index=True)
    scan_report_id = Column(Integer, ForeignKey("scan_reports.id"))
    file_path = Column(String)
    file_hash = Column(String)
    is_threat = Column(Boolean, default=False)
    threat_type = Column(String, nullable=True)
    virustotal_result = Column(Text, nullable=True)

    scan_report = relationship("ScanReport", back_populates="file_results")

# Create database tables
Base.metadata.create_all(bind=engine)

# Dependency to get database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Pydantic models for API
class FileResultCreate(BaseModel):
    file_path: str
    file_hash: str
    is_threat: bool
    threat_type: Optional[str] = None

class ScanReportCreate(BaseModel):
    scan_id: str
    scan_path: str
    files_scanned: int
    threats_found: int
    report_path: str
    report_content: str
    file_results: List[FileResultCreate]

class FileResultResponse(BaseModel):
    id: int
    file_path: str
    file_hash: str
    is_threat: bool
    threat_type: Optional[str] = None
    virustotal_result: Optional[Dict[str, Any]] = None

    class Config:
        orm_mode = True

class ScanReportResponse(BaseModel):
    id: int
    scan_id: str
    timestamp: datetime
    scan_path: str
    files_scanned: int
    threats_found: int
    report_path: str
    file_results: List[FileResultResponse] = []

    class Config:
        orm_mode = True

# Helper functions
def calculate_file_hash(file_path: str) -> str:
    """Calculate SHA-256 hash of a file."""
    try:
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for byte_block in iter(lambda: f.read(4096), b""):
                sha256_hash.update(byte_block)
        return sha256_hash.hexdigest()
    except Exception as e:
        logger.error(f"Error calculating hash for {file_path}: {e}")
        return ""

async def check_virustotal(file_hash: str) -> Dict[str, Any]:
    """Check a file hash against VirusTotal API."""
    if not VIRUSTOTAL_API_KEY:
        logger.warning("VirusTotal API key not configured")
        return {"error": "VirusTotal API key not configured"}

    try:
        headers = {
            "x-apikey": VIRUSTOTAL_API_KEY
        }
        response = requests.get(f"{VIRUSTOTAL_API_URL}/files/{file_hash}", headers=headers)

        if response.status_code == 200:
            return response.json()
        elif response.status_code == 404:
            return {"error": "File not found in VirusTotal database"}
        else:
            logger.error(f"VirusTotal API error: {response.status_code} - {response.text}")
            return {"error": f"VirusTotal API error: {response.status_code}"}
    except Exception as e:
        logger.error(f"Error checking VirusTotal: {e}")
        return {"error": str(e)}

# Create static directory if it doesn't exist
static_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "static")
os.makedirs(static_dir, exist_ok=True)

# Mount static files
app.mount("/static", StaticFiles(directory=static_dir), name="static")

# API endpoints
@app.get("/", response_class=HTMLResponse, tags=["dashboard"])
async def root():
    """
    Redirect to the dashboard.

    This endpoint redirects users to the main dashboard page.
    """
    return RedirectResponse(url="/dashboard")

@app.get("/dashboard", response_class=HTMLResponse, tags=["dashboard"])
async def dashboard():
    """
    Serve the dashboard HTML page.

    This endpoint serves the main dashboard HTML page that displays scan reports and statistics.
    """
    try:
        with open(os.path.join(static_dir, "dashboard.html"), "r") as f:
            return f.read()
    except FileNotFoundError:
        logger.error("Dashboard HTML file not found")
        return HTMLResponse(content="<h1>Dashboard not found</h1>", status_code=404)

@app.get("/api", tags=["reports"])
async def api_root():
    """
    API root endpoint.

    Returns basic information about the API.
    """
    return {
        "message": "SBARDS Backend API",
        "version": "1.0.0",
        "documentation": "/api/docs",
        "endpoints": {
            "reports": "/api/reports/",
            "stats": "/api/stats/",
            "check_file": "/api/check-file/"
        }
    }

@app.post("/api/reports/",
         response_model=ScanReportResponse,
         status_code=status.HTTP_201_CREATED,
         tags=["reports"],
         summary="Create a new scan report",
         description="Create a new scan report and store it in the database. For threats, it will automatically check VirusTotal in the background.")
async def create_scan_report(
    report: ScanReportCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Create a new scan report and store it in the database.

    - **scan_id**: Unique identifier for the scan
    - **scan_path**: Path that was scanned
    - **files_scanned**: Number of files scanned
    - **threats_found**: Number of threats found
    - **report_path**: Path to the HTML report file
    - **report_content**: Content of the HTML report
    - **file_results**: List of file results with threat information

    For any files marked as threats, the system will automatically check them against VirusTotal
    in the background and update the database with the results.
    """
    logger.info(f"Creating new scan report with ID: {report.scan_id}")

    try:
        # Create the scan report
        db_report = ScanReport(
            scan_id=report.scan_id,
            scan_path=report.scan_path,
            files_scanned=report.files_scanned,
            threats_found=report.threats_found,
            report_path=report.report_path,
            report_content=report.report_content
        )
        db.add(db_report)
        db.commit()
        db.refresh(db_report)

        # Add file results
        for file_result in report.file_results:
            db_file_result = FileResult(
                scan_report_id=db_report.id,
                file_path=file_result.file_path,
                file_hash=file_result.file_hash,
                is_threat=file_result.is_threat,
                threat_type=file_result.threat_type
            )
            db.add(db_file_result)

            # Check VirusTotal in background for threats
            if file_result.is_threat:
                logger.info(f"Scheduling VirusTotal check for file: {file_result.file_path}")
                background_tasks.add_task(check_and_update_virustotal, db_file_result.id)

        db.commit()

        # Return the created report
        logger.info(f"Successfully created scan report with ID: {report.scan_id}")
        return db_report

    except Exception as e:
        logger.error(f"Error creating scan report: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating scan report: {str(e)}"
        )

async def check_and_update_virustotal(file_result_id: int):
    """Check VirusTotal for a file result and update the database."""
    db = SessionLocal()
    try:
        file_result = db.query(FileResult).filter(FileResult.id == file_result_id).first()
        if file_result:
            vt_result = await check_virustotal(file_result.file_hash)
            file_result.virustotal_result = json.dumps(vt_result)
            db.commit()
    except Exception as e:
        logger.error(f"Error updating VirusTotal result: {e}")
    finally:
        db.close()

@app.get("/api/reports/",
         response_model=List[ScanReportResponse],
         tags=["reports"],
         summary="Get all scan reports",
         description="Retrieve a list of all scan reports with pagination support.")
async def get_scan_reports(
    skip: int = 0,
    limit: int = 100,
    include_content: bool = False,
    db: Session = Depends(get_db)
):
    """
    Get all scan reports with pagination.

    - **skip**: Number of records to skip (for pagination)
    - **limit**: Maximum number of records to return
    - **include_content**: Whether to include the full HTML report content (default: false)

    Returns a list of scan reports ordered by timestamp (newest first).
    """
    try:
        # Query reports ordered by timestamp (newest first)
        query = db.query(ScanReport).order_by(ScanReport.timestamp.desc())

        # Apply pagination
        reports = query.offset(skip).limit(limit).all()

        # If include_content is False, remove the report_content field to reduce response size
        if not include_content:
            for report in reports:
                report.report_content = None

        return reports
    except Exception as e:
        logger.error(f"Error retrieving scan reports: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving scan reports: {str(e)}"
        )

@app.get("/api/reports/{scan_id}",
         response_model=ScanReportResponse,
         tags=["reports"],
         summary="Get a specific scan report",
         description="Retrieve a specific scan report by its ID.")
async def get_scan_report(
    scan_id: str,
    include_content: bool = True,
    db: Session = Depends(get_db)
):
    """
    Get a specific scan report by ID.

    - **scan_id**: The unique identifier of the scan report
    - **include_content**: Whether to include the full HTML report content (default: true)

    Returns the scan report with the specified ID, including all file results.
    """
    try:
        report = db.query(ScanReport).filter(ScanReport.scan_id == scan_id).first()

        if report is None:
            logger.warning(f"Scan report not found: {scan_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Scan report with ID {scan_id} not found"
            )

        # If include_content is False, remove the report_content field to reduce response size
        if not include_content:
            report.report_content = None

        return report
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving scan report {scan_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving scan report: {str(e)}"
        )

@app.post("/api/check-file/",
         tags=["files"],
         summary="Check a file against VirusTotal",
         description="Upload a file to check it against VirusTotal's database of known threats.")
async def check_file(
    file: UploadFile = File(...),
    save_result: bool = False,
    db: Session = Depends(get_db)
):
    """
    Check a file against VirusTotal.

    - **file**: The file to check
    - **save_result**: Whether to save the result to the database (default: false)

    The file will be temporarily saved, hashed, and then the hash will be checked against VirusTotal.
    The file is deleted after processing.
    """
    logger.info(f"Checking file against VirusTotal: {file.filename}")

    # Save the uploaded file temporarily
    temp_file_path = f"temp_{file.filename}"
    try:
        with open(temp_file_path, "wb") as f:
            content = await file.read()
            f.write(content)

        # Calculate file hash
        file_hash = calculate_file_hash(temp_file_path)
        logger.info(f"File hash: {file_hash}")

        # Check VirusTotal
        vt_result = await check_virustotal(file_hash)

        # Save result to database if requested
        if save_result and "error" not in vt_result:
            try:
                # Create a file result entry
                file_result = FileResult(
                    file_path=file.filename,
                    file_hash=file_hash,
                    is_threat=False,  # Will be updated based on VT result
                    virustotal_result=json.dumps(vt_result)
                )

                # Check if it's a threat based on VirusTotal result
                if "data" in vt_result and "attributes" in vt_result["data"]:
                    attributes = vt_result["data"]["attributes"]
                    if "last_analysis_stats" in attributes:
                        stats = attributes["last_analysis_stats"]
                        if stats.get("malicious", 0) > 0:
                            file_result.is_threat = True
                            file_result.threat_type = f"VirusTotal: {stats.get('malicious', 0)} detections"

                db.add(file_result)
                db.commit()
                logger.info(f"Saved VirusTotal result for {file.filename}")
            except Exception as e:
                logger.error(f"Error saving VirusTotal result: {e}")
                db.rollback()

        # Prepare response
        response = {
            "filename": file.filename,
            "file_hash": file_hash,
            "virustotal_result": vt_result
        }

        # Add a summary if available
        if "data" in vt_result and "attributes" in vt_result["data"]:
            attributes = vt_result["data"]["attributes"]
            if "last_analysis_stats" in attributes:
                stats = attributes["last_analysis_stats"]
                response["summary"] = {
                    "malicious": stats.get("malicious", 0),
                    "suspicious": stats.get("suspicious", 0),
                    "undetected": stats.get("undetected", 0),
                    "harmless": stats.get("harmless", 0)
                }

        return response
    except Exception as e:
        logger.error(f"Error checking file against VirusTotal: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error checking file: {str(e)}"
        )
    finally:
        # Clean up temporary file
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)

@app.get("/api/stats/",
         tags=["stats"],
         summary="Get system statistics",
         description="Get statistics about scans and threats from the system.")
async def get_stats(db: Session = Depends(get_db)):
    """
    Get statistics about scans and threats.

    Returns:
    - **total_scans**: Total number of scans performed
    - **total_files_scanned**: Total number of files scanned
    - **total_threats**: Total number of threats found
    - **recent_scans**: Number of scans in the last 24 hours
    - **recent_threats**: Number of threats found in the last 24 hours
    """
    try:
        # Basic stats
        total_scans = db.query(ScanReport).count()
        total_files_scanned = db.query(ScanReport).with_entities(
            db.func.sum(ScanReport.files_scanned)
        ).scalar() or 0
        total_threats = db.query(ScanReport).with_entities(
            db.func.sum(ScanReport.threats_found)
        ).scalar() or 0

        # Recent stats (last 24 hours)
        from datetime import timedelta
        yesterday = datetime.now() - timedelta(days=1)

        recent_scans = db.query(ScanReport).filter(
            ScanReport.timestamp >= yesterday
        ).count()

        recent_threats = db.query(ScanReport).filter(
            ScanReport.timestamp >= yesterday
        ).with_entities(
            db.func.sum(ScanReport.threats_found)
        ).scalar() or 0

        # Top threats by type
        threat_types = db.query(
            FileResult.threat_type,
            db.func.count(FileResult.id).label('count')
        ).filter(
            FileResult.is_threat == True
        ).group_by(
            FileResult.threat_type
        ).order_by(
            db.func.count(FileResult.id).desc()
        ).limit(5).all()

        top_threats = [
            {"type": t[0] or "Unknown", "count": t[1]}
            for t in threat_types
        ]

        return {
            "total_scans": total_scans,
            "total_files_scanned": total_files_scanned,
            "total_threats": total_threats,
            "recent_scans": recent_scans,
            "recent_threats": recent_threats,
            "top_threat_types": top_threats
        }
    except Exception as e:
        logger.error(f"Error getting statistics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting statistics: {str(e)}"
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
