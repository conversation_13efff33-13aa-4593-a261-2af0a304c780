#!/usr/bin/env python3
"""
SBARDS Main Entry Point - Comprehensive Workflow System

This is the main entry point for the SBARDS (Smart Behavioral Analysis and Ransomware Detection System).
It provides a unified interface for all system components and phases with options to run individual phases
or the complete workflow according to best practices.

Available Phases:
1. Capture Layer (طبقة الالتقاط)
2. Pre-Scanning Quick Check (فحص أولي سريع)
3. Static Analysis Layer (طبقة التحليل الثابت)
4. Dynamic Analysis Layer (طبقة التحليل الديناميكي)
5. Response Layer (طبقة الاستجابة)
6. External Integration Layer (طبقة التكامل الخارجي)
7. Memory Protection Layer (طبقة حماية الذاكرة)
8. Continuous Monitoring Layer (طبقة المراقبة المستمرة)
9. Complete Workflow (جميع المراحل)
"""

import sys
import os
import json
import logging
import argparse
import time
import signal
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import core modules
from core.config import load_config
from core.logging import setup_logging
from core.utils import validate_environment

# Import all phase modules with graceful error handling
MISSING_DEPENDENCIES = []

try:
    from phases.capture.capture import CaptureLayer
except ImportError as e:
    print(f"Warning: Cannot import CaptureLayer: {e}")
    MISSING_DEPENDENCIES.append("CaptureLayer")
    CaptureLayer = None

try:
    from phases.prescanning.orchestrator import Orchestrator as PreScanningOrchestrator
except ImportError as e:
    print(f"Warning: Cannot import PreScanningOrchestrator: {e}")
    MISSING_DEPENDENCIES.append("PreScanningOrchestrator")
    PreScanningOrchestrator = None

try:
    from phases.static_analysis.static_analyzer import StaticAnalyzer
except ImportError as e:
    print(f"Warning: Cannot import StaticAnalyzer: {e}")
    MISSING_DEPENDENCIES.append("StaticAnalyzer")
    StaticAnalyzer = None

try:
    from phases.dynamic_analysis.dynamic_analyzer import DynamicAnalyzer
except ImportError as e:
    print(f"Warning: Cannot import DynamicAnalyzer: {e}")
    MISSING_DEPENDENCIES.append("DynamicAnalyzer")
    DynamicAnalyzer = None

try:
    from phases.response.response import ResponseLayer
except ImportError as e:
    print(f"Warning: Cannot import ResponseLayer: {e}")
    MISSING_DEPENDENCIES.append("ResponseLayer")
    ResponseLayer = None

try:
    from phases.integration.external_integration import ExternalIntegrationLayer
except ImportError as e:
    print(f"Warning: Cannot import ExternalIntegrationLayer: {e}")
    MISSING_DEPENDENCIES.append("ExternalIntegrationLayer")
    ExternalIntegrationLayer = None

try:
    from phases.memory_protection.memory_protection import MemoryProtectionLayer
except ImportError as e:
    print(f"Warning: Cannot import MemoryProtectionLayer: {e}")
    MISSING_DEPENDENCIES.append("MemoryProtectionLayer")
    MemoryProtectionLayer = None

try:
    from phases.monitoring.monitor_manager import MonitorManager
except ImportError as e:
    print(f"Warning: Cannot import MonitorManager: {e}")
    MISSING_DEPENDENCIES.append("MonitorManager")
    MonitorManager = None

try:
    from phases.integration.workflow_orchestrator import WorkflowOrchestrator
except ImportError as e:
    print(f"Warning: Cannot import WorkflowOrchestrator: {e}")
    MISSING_DEPENDENCIES.append("WorkflowOrchestrator")
    WorkflowOrchestrator = None

if MISSING_DEPENDENCIES:
    print(f"\nWarning: Some phase modules could not be imported: {', '.join(MISSING_DEPENDENCIES)}")
    print("Some functionality may be limited. Please install missing dependencies.")
    print("To install dependencies: pip install yara-python python-magic docker psutil requests")


class SBARDSMain:
    """Main SBARDS application controller."""

    def __init__(self, config_path: str = "config.json"):
        """Initialize SBARDS main controller."""
        self.config_path = config_path
        self.config = None
        self.logger = None
        self.running = False

        # Phase instances
        self.capture_layer = None
        self.prescanning_orchestrator = None
        self.static_analyzer = None
        self.dynamic_analyzer = None
        self.response_layer = None
        self.external_integration = None
        self.memory_protection = None
        self.monitor_manager = None
        self.workflow_orchestrator = None

    def initialize(self, log_level: str = "INFO") -> bool:
        """Initialize SBARDS system."""
        try:
            # Setup logging
            setup_logging(level=log_level)
            self.logger = logging.getLogger("SBARDS.Main")

            self.logger.info("Initializing SBARDS...")

            # Load configuration
            self.config = load_config(self.config_path)

            # Validate environment
            if not validate_environment(self.config):
                self.logger.error("Environment validation failed")
                return False

            # Initialize phase instances
            self._initialize_phases()

            self.logger.info("SBARDS initialization completed successfully")
            return True

        except Exception as e:
            if self.logger:
                self.logger.error(f"Initialization failed: {e}")
            else:
                print(f"Initialization failed: {e}")
            return False

    def _initialize_phases(self):
        """Initialize all phase instances."""
        try:
            # Initialize phases only if they were imported successfully
            self.capture_layer = CaptureLayer(self.config) if CaptureLayer else None
            self.prescanning_orchestrator = PreScanningOrchestrator(self.config) if PreScanningOrchestrator else None
            self.static_analyzer = StaticAnalyzer(self.config) if StaticAnalyzer else None
            self.dynamic_analyzer = DynamicAnalyzer(self.config) if DynamicAnalyzer else None
            self.response_layer = ResponseLayer(self.config) if ResponseLayer else None
            self.external_integration = ExternalIntegrationLayer(self.config) if ExternalIntegrationLayer else None
            self.memory_protection = MemoryProtectionLayer(self.config) if MemoryProtectionLayer else None
            self.monitor_manager = MonitorManager(self.config) if MonitorManager else None
            self.workflow_orchestrator = WorkflowOrchestrator(self.config) if WorkflowOrchestrator else None

            # Count successfully initialized phases
            initialized_phases = sum(1 for phase in [
                self.capture_layer, self.prescanning_orchestrator, self.static_analyzer,
                self.dynamic_analyzer, self.response_layer, self.external_integration,
                self.memory_protection, self.monitor_manager, self.workflow_orchestrator
            ] if phase is not None)

            self.logger.info(f"Initialized {initialized_phases}/9 phases successfully")

            if MISSING_DEPENDENCIES:
                self.logger.warning(f"Missing dependencies: {', '.join(MISSING_DEPENDENCIES)}")

        except Exception as e:
            self.logger.error(f"Error initializing phases: {e}")
            raise

    def _save_phase_results(self, phase_name: str, results: Dict[str, Any], output_dir: str):
        """Save phase results to file."""
        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{phase_name}_results_{timestamp}.json"

            with open(output_path / filename, 'w') as f:
                json.dump(results, f, indent=2, default=str)

            self.logger.debug(f"Results saved to: {output_path / filename}")

        except Exception as e:
            self.logger.error(f"Error saving results: {e}")

    def _get_files_to_scan(self, directory_path: str, recursive: bool = True,
                          max_files: int = 1000, file_types: list = None) -> list:
        """Get list of files to scan from directory."""
        import os

        files_to_scan = []

        try:
            if recursive:
                for root, dirs, files in os.walk(directory_path):
                    for file in files:
                        file_path = os.path.join(root, file)

                        # Filter by file types if specified
                        if file_types:
                            file_ext = os.path.splitext(file)[1].lower()
                            if file_ext not in file_types:
                                continue

                        files_to_scan.append(file_path)

                        if len(files_to_scan) >= max_files:
                            break
                    if len(files_to_scan) >= max_files:
                        break
            else:
                for item in os.listdir(directory_path):
                    item_path = os.path.join(directory_path, item)
                    if os.path.isfile(item_path):
                        # Filter by file types if specified
                        if file_types:
                            file_ext = os.path.splitext(item)[1].lower()
                            if file_ext not in file_types:
                                continue

                        files_to_scan.append(item_path)

                        if len(files_to_scan) >= max_files:
                            break

        except Exception as e:
            self.logger.error(f"Error getting files to scan: {e}")

        return files_to_scan

    def show_phase_menu(self):
        """Display the phase selection menu."""
        print("\n" + "="*80)
        print("SBARDS - Smart Behavioral Analysis and Ransomware Detection System")
        print("="*80)
        print("Available Phases:")
        print("1. Capture Layer (طبقة الالتقاط)")
        print("2. Pre-Scanning Quick Check (فحص أولي سريع)")
        print("3. Static Analysis Layer (طبقة التحليل الثابت)")
        print("4. Dynamic Analysis Layer (طبقة التحليل الديناميكي)")
        print("5. Response Layer (طبقة الاستجابة)")
        print("6. External Integration Layer (طبقة التكامل الخارجي)")
        print("7. Memory Protection Layer (طبقة حماية الذاكرة)")
        print("8. Continuous Monitoring Layer (طبقة المراقبة المستمرة)")
        print("9. Complete Workflow (جميع المراحل)")
        print("10. Continuous Phases (المراحل المتواصلة)")
        print("11. Show Help & Examples (المساعدة والأمثلة)")
        print("0. Exit")
        print("="*80)

    def run_capture_phase(self, file_path: str, output_dir: str = "output",
                         recursive: bool = True, max_files: int = 1000,
                         file_types: list = None) -> Dict[str, Any]:
        """Run Capture Layer phase."""
        self.logger.info("=== CAPTURE LAYER PHASE ===")

        if not self.capture_layer:
            error_msg = "Capture layer not available (missing dependencies)"
            self.logger.error(f"✗ {error_msg}")
            return {"success": False, "error": error_msg}

        try:
            import os

            # Check if path is directory or file
            if os.path.isdir(file_path):
                self.logger.info(f"Capturing directory: {file_path}")
                self.logger.info(f"  - Recursive: {recursive}")
                self.logger.info(f"  - Max files: {max_files}")
                if file_types:
                    self.logger.info(f"  - File types: {', '.join(file_types)}")

                # Capture directory
                result = self.capture_layer.capture_directory(
                    file_path,
                    recursive=recursive,
                    max_files=max_files,
                    metadata={"file_types_filter": file_types}
                )

                if result.get("success"):
                    self.logger.info(f"✓ Directory captured successfully: {result.get('scan_id')}")
                    self.logger.info(f"  - Files captured: {result.get('files_captured', 0)}")
                    self.logger.info(f"  - Files failed: {result.get('files_failed', 0)}")
                else:
                    self.logger.error(f"✗ Directory capture failed: {result.get('error')}")

            else:
                self.logger.info(f"Capturing file: {file_path}")

                # Capture single file
                result = self.capture_layer.capture_file(file_path)

                if result.get("success"):
                    self.logger.info(f"✓ File captured successfully: {result.get('capture_id')}")
                    self.logger.info(f"  - File Hash: {result.get('file_info', {}).get('file_hash', 'N/A')}")
                    self.logger.info(f"  - File Size: {result.get('file_info', {}).get('file_size', 'N/A')} bytes")
                else:
                    self.logger.error(f"✗ Capture failed: {result.get('error')}")

            # Save results
            self._save_phase_results("capture", result, output_dir)
            return result

        except Exception as e:
            self.logger.error(f"Capture phase error: {e}")
            return {"success": False, "error": str(e)}

    def run_prescanning_phase(self, file_path: str, output_dir: str = "output",
                             recursive: bool = True, max_files: int = 1000,
                             file_types: list = None) -> Dict[str, Any]:
        """Run Pre-Scanning Quick Check phase."""
        self.logger.info("=== PRE-SCANNING QUICK CHECK PHASE ===")

        if not self.prescanning_orchestrator:
            error_msg = "Pre-scanning orchestrator not available (missing dependencies)"
            self.logger.error(f"✗ {error_msg}")
            return {"error": error_msg}

        try:
            import os

            # Check if path is directory or file
            if os.path.isdir(file_path):
                self.logger.info(f"Pre-scanning directory: {file_path}")

                # Get list of files to scan
                files_to_scan = self._get_files_to_scan(file_path, recursive, max_files, file_types)

                self.logger.info(f"  - Files to scan: {len(files_to_scan)}")

                # Scan all files
                all_results = []
                total_matches = 0

                for i, file_to_scan in enumerate(files_to_scan, 1):
                    try:
                        self.logger.debug(f"Scanning file {i}/{len(files_to_scan)}: {file_to_scan}")
                        file_result = self.prescanning_orchestrator.scan_file(file_to_scan)
                        file_result["file_path"] = file_to_scan
                        all_results.append(file_result)

                        matches = file_result.get("matches", [])
                        total_matches += len(matches)

                        if matches:
                            self.logger.warning(f"⚠ Suspicious patterns in {os.path.basename(file_to_scan)}: {len(matches)} matches")

                    except Exception as e:
                        self.logger.error(f"Error scanning {file_to_scan}: {e}")
                        all_results.append({
                            "file_path": file_to_scan,
                            "error": str(e),
                            "matches": []
                        })

                # Aggregate results
                result = {
                    "directory_path": file_path,
                    "files_scanned": len(files_to_scan),
                    "total_matches": total_matches,
                    "files_with_matches": len([r for r in all_results if r.get("matches", [])]),
                    "scan_results": all_results,
                    "scan_time": sum(r.get("scan_time", 0) for r in all_results if "scan_time" in r)
                }

                self.logger.info(f"✓ Directory pre-scanning completed")
                self.logger.info(f"  - Files scanned: {len(files_to_scan)}")
                self.logger.info(f"  - Total matches: {total_matches}")
                self.logger.info(f"  - Files with matches: {result['files_with_matches']}")

            else:
                self.logger.info(f"Pre-scanning file: {file_path}")

                # Run pre-scanning on single file
                result = self.prescanning_orchestrator.scan_file(file_path)

                matches = result.get("matches", [])
                self.logger.info(f"✓ Pre-scanning completed")
                self.logger.info(f"  - Matches found: {len(matches)}")
                self.logger.info(f"  - Scan time: {result.get('scan_time', 'N/A')} seconds")

                if matches:
                    self.logger.warning("⚠ Suspicious patterns detected:")
                    for match in matches[:5]:  # Show first 5 matches
                        self.logger.warning(f"    - {match.get('rule', 'Unknown rule')}")

            # Save results
            self._save_phase_results("prescanning", result, output_dir)
            return result

        except Exception as e:
            self.logger.error(f"Pre-scanning phase error: {e}")
            return {"error": str(e)}

    def run_static_analysis_phase(self, file_path: str, output_dir: str = "output",
                                 recursive: bool = True, max_files: int = 1000,
                                 file_types: list = None) -> Dict[str, Any]:
        """Run Static Analysis Layer phase."""
        self.logger.info("=== STATIC ANALYSIS LAYER PHASE ===")

        try:
            import os

            # Check if path is directory or file
            if os.path.isdir(file_path):
                self.logger.info(f"Static analysis on directory: {file_path}")

                # Get list of files to analyze
                files_to_analyze = self._get_files_to_scan(file_path, recursive, max_files, file_types)

                self.logger.info(f"  - Files to analyze: {len(files_to_analyze)}")

                # Analyze all files
                all_results = []
                total_risk_score = 0
                total_matches = 0

                for i, file_to_analyze in enumerate(files_to_analyze, 1):
                    try:
                        self.logger.debug(f"Analyzing file {i}/{len(files_to_analyze)}: {file_to_analyze}")

                        # Run static analysis
                        file_result = self.static_analyzer.analyze_file(file_to_analyze)
                        file_result["file_path"] = file_to_analyze
                        all_results.append(file_result)

                        # Aggregate metrics
                        risk_assessment = file_result.get("risk_assessment", {})
                        risk_score = risk_assessment.get("risk_score", 0)
                        total_risk_score += risk_score

                        yara_analysis = file_result.get("yara_analysis", {})
                        yara_matches = yara_analysis.get("matches_found", 0)
                        total_matches += yara_matches

                        if risk_score > 50 or yara_matches > 0:
                            self.logger.warning(f"⚠ High risk file {os.path.basename(file_to_analyze)}: Risk={risk_score}, YARA={yara_matches}")

                    except Exception as e:
                        self.logger.error(f"Error analyzing {file_to_analyze}: {e}")
                        all_results.append({
                            "file_path": file_to_analyze,
                            "error": str(e),
                            "risk_assessment": {"risk_score": 0}
                        })

                # Aggregate results
                avg_risk_score = total_risk_score / len(files_to_analyze) if files_to_analyze else 0
                high_risk_files = [r for r in all_results if r.get("risk_assessment", {}).get("risk_score", 0) > 50]

                result = {
                    "directory_path": file_path,
                    "files_analyzed": len(files_to_analyze),
                    "total_risk_score": total_risk_score,
                    "average_risk_score": avg_risk_score,
                    "total_yara_matches": total_matches,
                    "high_risk_files": len(high_risk_files),
                    "analysis_results": all_results
                }

                self.logger.info(f"✓ Directory static analysis completed")
                self.logger.info(f"  - Files analyzed: {len(files_to_analyze)}")
                self.logger.info(f"  - Average risk score: {avg_risk_score:.1f}")
                self.logger.info(f"  - Total YARA matches: {total_matches}")
                self.logger.info(f"  - High risk files: {len(high_risk_files)}")

            else:
                self.logger.info(f"Static analysis on file: {file_path}")

                # Run static analysis
                result = self.static_analyzer.analyze_file(file_path)

                if "error" not in result:
                    risk_assessment = result.get("risk_assessment", {})
                    self.logger.info(f"✓ Static analysis completed")
                    self.logger.info(f"  - Risk Level: {risk_assessment.get('risk_level', 'N/A')}")
                    self.logger.info(f"  - Risk Score: {risk_assessment.get('risk_score', 'N/A')}/100")
                    self.logger.info(f"  - Recommendation: {risk_assessment.get('recommendation', 'N/A')}")

                    # Show analysis components
                    components = [k for k in result.keys() if k.endswith("_analysis")]
                    self.logger.info(f"  - Analysis components: {len(components)}")

                    # Show YARA matches if any
                    yara_analysis = result.get("yara_analysis", {})
                    if yara_analysis.get("matches_found", 0) > 0:
                        self.logger.warning(f"⚠ YARA matches: {yara_analysis['matches_found']}")

                    # Show entropy analysis
                    entropy_analysis = result.get("entropy_analysis", {})
                    if entropy_analysis.get("is_suspicious", False):
                        self.logger.warning(f"⚠ High entropy detected: {entropy_analysis.get('entropy', 'N/A')}")
                else:
                    self.logger.error(f"✗ Static analysis failed: {result.get('error')}")

            # Save results
            self._save_phase_results("static_analysis", result, output_dir)
            return result

        except Exception as e:
            self.logger.error(f"Static analysis phase error: {e}")
            return {"error": str(e)}

    def run_dynamic_analysis_phase(self, file_path: str, output_dir: str = "output") -> Dict[str, Any]:
        """Run Dynamic Analysis Layer phase."""
        self.logger.info("=== DYNAMIC ANALYSIS LAYER PHASE ===")
        self.logger.info("Performing behavioral analysis in sandbox...")

        try:
            # Run dynamic analysis
            result = self.dynamic_analyzer.analyze_file(file_path)

            # Save results
            self._save_phase_results("dynamic_analysis", result, output_dir)

            if "error" not in result:
                risk_assessment = result.get("risk_assessment", {})
                behavioral_analysis = result.get("behavioral_analysis", {})

                self.logger.info(f"✓ Dynamic analysis completed")
                self.logger.info(f"  - Analysis Duration: {result.get('analysis_duration', 'N/A')} seconds")
                self.logger.info(f"  - Risk Level: {risk_assessment.get('risk_level', 'N/A')}")
                self.logger.info(f"  - Risk Score: {risk_assessment.get('risk_score', 'N/A')}/100")
                self.logger.info(f"  - Sandbox Type: {result.get('sandbox_type', 'N/A')}")

                # Show behavioral indicators
                ransomware_score = behavioral_analysis.get("ransomware_score", 0)
                if ransomware_score > 0:
                    self.logger.warning(f"⚠ Ransomware indicators: {ransomware_score}/100")

                # Show network behavior
                network_behavior = behavioral_analysis.get("network_behavior", {})
                if network_behavior.get("suspicious_ports"):
                    self.logger.warning(f"⚠ Suspicious network activity detected")

                # Show process behavior
                process_behavior = behavioral_analysis.get("process_behavior", {})
                if process_behavior.get("suspicious_processes"):
                    self.logger.warning(f"⚠ Suspicious processes: {len(process_behavior['suspicious_processes'])}")
            else:
                self.logger.error(f"✗ Dynamic analysis failed: {result.get('error')}")

            return result

        except Exception as e:
            self.logger.error(f"Dynamic analysis phase error: {e}")
            return {"error": str(e)}

    def run_response_phase(self, analysis_results: Dict[str, Any], output_dir: str = "output") -> Dict[str, Any]:
        """Run Response Layer phase."""
        self.logger.info("=== RESPONSE LAYER PHASE ===")
        self.logger.info("Processing response actions...")

        try:
            # Run response actions
            result = self.response_layer.send_alerts(analysis_results)

            # Save results
            self._save_phase_results("response", result, output_dir)

            self.logger.info(f"✓ Response phase completed")
            self.logger.info(f"  - Alerts sent: {len(result.get('alerts_sent', []))}")
            self.logger.info(f"  - Errors: {len(result.get('errors', []))}")

            return result

        except Exception as e:
            self.logger.error(f"Response phase error: {e}")
            return {"error": str(e)}

    def run_monitoring_phase(self, duration_minutes: int = 60, output_dir: str = "output") -> Dict[str, Any]:
        """Run Continuous Monitoring Layer phase."""
        self.logger.info("=== CONTINUOUS MONITORING LAYER PHASE ===")
        self.logger.info(f"Starting monitoring for {duration_minutes} minutes...")

        try:
            # Start monitoring
            self.monitor_manager.start_monitoring()

            # Monitor for specified duration
            import time
            start_time = time.time()
            duration_seconds = duration_minutes * 60

            while time.time() - start_time < duration_seconds:
                time.sleep(10)  # Check every 10 seconds

                # Get current monitoring status
                status = self.monitor_manager.get_status()
                if not status.get("running", False):
                    break

            # Stop monitoring and get results
            stop_result = self.monitor_manager.stop_monitoring()

            # Get monitoring status for results
            results = self.monitor_manager.get_status(detailed=True)
            results["stop_result"] = stop_result
            results["duration_minutes"] = (time.time() - start_time) / 60

            # Save results
            self._save_phase_results("monitoring", results, output_dir)

            self.logger.info(f"✓ Monitoring completed")
            self.logger.info(f"  - Duration: {(time.time() - start_time)/60:.1f} minutes")
            self.logger.info(f"  - Events detected: {len(results.get('events', []))}")

            return results

        except Exception as e:
            self.logger.error(f"Monitoring phase error: {e}")
            return {"error": str(e)}

    def run_complete_workflow(self, file_path: str, output_dir: str = "output") -> Dict[str, Any]:
        """Run Complete Workflow with all phases."""
        self.logger.info("=== COMPLETE WORKFLOW EXECUTION ===")
        self.logger.info(f"Processing file: {file_path}")

        try:
            # Use the workflow orchestrator
            result = self.workflow_orchestrator.process_file(file_path)

            # Save complete workflow results
            self._save_phase_results("complete_workflow", result, output_dir)

            # Display summary
            final_decision = result.get("final_decision", {})
            self.logger.info(f"✓ Complete workflow finished")
            self.logger.info(f"  - Workflow ID: {result.get('workflow_id', 'N/A')}")
            self.logger.info(f"  - Final Decision: {final_decision.get('decision', 'N/A')}")
            self.logger.info(f"  - Reason: {final_decision.get('reason', 'N/A')}")

            # Show phases executed
            phases = result.get("phases", {})
            self.logger.info(f"  - Phases executed: {len(phases)}")

            for phase_name, phase_result in phases.items():
                if isinstance(phase_result, dict) and "error" not in phase_result:
                    self.logger.info(f"    ✓ {phase_name}")
                else:
                    self.logger.warning(f"    ⚠ {phase_name} (with issues)")

            return result

        except Exception as e:
            self.logger.error(f"Complete workflow error: {e}")
            return {"error": str(e)}

    def run_continuous_phases(self, file_path: str, output_dir: str = "output",
                             recursive: bool = True, max_files: int = 1000,
                             file_types: list = None) -> Dict[str, Any]:
        """Run all phases continuously as a single integrated project."""
        self.logger.info("=== CONTINUOUS PHASES EXECUTION ===")
        self.logger.info("Running all phases sequentially as integrated workflow...")

        import time
        start_time = time.time()

        # Initialize results container
        continuous_results = {
            "execution_id": f"continuous_{int(time.time())}",
            "start_time": time.time(),
            "target_path": file_path,
            "configuration": {
                "recursive": recursive,
                "max_files": max_files,
                "file_types": file_types
            },
            "phases": {},
            "summary": {},
            "errors": []
        }

        try:
            # Phase 1: Capture Layer
            self.logger.info("\n" + "="*60)
            self.logger.info("PHASE 1/6: CAPTURE LAYER")
            self.logger.info("="*60)

            capture_result = self.run_capture_phase(
                file_path, output_dir, recursive, max_files, file_types
            )
            continuous_results["phases"]["capture"] = capture_result

            if not capture_result.get("success", False):
                continuous_results["errors"].append("Capture phase failed")
                return self._finalize_continuous_results(continuous_results, start_time)

            # Phase 2: Pre-Scanning Quick Check
            self.logger.info("\n" + "="*60)
            self.logger.info("PHASE 2/6: PRE-SCANNING QUICK CHECK")
            self.logger.info("="*60)

            prescan_result = self.run_prescanning_phase(
                file_path, output_dir, recursive, max_files, file_types
            )
            continuous_results["phases"]["prescanning"] = prescan_result

            # Phase 3: Static Analysis Layer
            self.logger.info("\n" + "="*60)
            self.logger.info("PHASE 3/6: STATIC ANALYSIS LAYER")
            self.logger.info("="*60)

            static_result = self.run_static_analysis_phase(
                file_path, output_dir, recursive, max_files, file_types
            )
            continuous_results["phases"]["static_analysis"] = static_result

            # Phase 4: Dynamic Analysis Layer (if available)
            self.logger.info("\n" + "="*60)
            self.logger.info("PHASE 4/6: DYNAMIC ANALYSIS LAYER")
            self.logger.info("="*60)

            if self.dynamic_analyzer:
                # For directories, analyze first captured file as representative
                analysis_target = file_path
                if capture_result.get("scan_id") and capture_result.get("captured_files"):
                    analysis_target = capture_result["captured_files"][0]["file_path"]

                dynamic_result = self.run_dynamic_analysis_phase(analysis_target, output_dir)
                continuous_results["phases"]["dynamic_analysis"] = dynamic_result
            else:
                self.logger.warning("Dynamic analysis not available - skipping")
                continuous_results["phases"]["dynamic_analysis"] = {"skipped": True, "reason": "Not available"}

            # Phase 5: Response Layer
            self.logger.info("\n" + "="*60)
            self.logger.info("PHASE 5/6: RESPONSE LAYER")
            self.logger.info("="*60)

            # Aggregate analysis results for response
            analysis_results = {
                "capture": capture_result,
                "prescanning": prescan_result,
                "static_analysis": static_result,
                "dynamic_analysis": continuous_results["phases"].get("dynamic_analysis", {})
            }

            response_result = self.run_response_phase(analysis_results, output_dir)
            continuous_results["phases"]["response"] = response_result

            # Phase 6: Monitoring (short duration for continuous mode)
            self.logger.info("\n" + "="*60)
            self.logger.info("PHASE 6/6: CONTINUOUS MONITORING")
            self.logger.info("="*60)

            monitoring_result = self.run_monitoring_phase(5, output_dir)  # 5 minutes monitoring
            continuous_results["phases"]["monitoring"] = monitoring_result

            # Generate comprehensive summary
            continuous_results = self._finalize_continuous_results(continuous_results, start_time)

            # Save complete continuous results
            self._save_phase_results("continuous_phases", continuous_results, output_dir)

            # Display final summary
            self._display_continuous_summary(continuous_results)

            return continuous_results

        except Exception as e:
            self.logger.error(f"Continuous phases execution error: {e}")
            continuous_results["errors"].append(str(e))
            return self._finalize_continuous_results(continuous_results, start_time)

    def _finalize_continuous_results(self, results: Dict[str, Any], start_time: float) -> Dict[str, Any]:
        """Finalize continuous phases results with summary."""
        import time

        end_time = time.time()
        execution_time = end_time - start_time

        # Calculate summary statistics
        phases_completed = len([p for p in results["phases"].values() if not p.get("skipped", False)])
        phases_with_errors = len([p for p in results["phases"].values() if "error" in p])

        # Aggregate threat indicators
        total_yara_matches = 0
        total_risk_score = 0
        high_risk_files = 0

        # From prescanning
        prescan = results["phases"].get("prescanning", {})
        if "total_matches" in prescan:
            total_yara_matches += prescan["total_matches"]
        elif "matches" in prescan:
            total_yara_matches += len(prescan["matches"])

        # From static analysis
        static = results["phases"].get("static_analysis", {})
        if "total_yara_matches" in static:
            total_yara_matches += static["total_yara_matches"]
        if "average_risk_score" in static:
            total_risk_score = static["average_risk_score"]
        if "high_risk_files" in static:
            high_risk_files = static["high_risk_files"]

        # Determine overall threat level
        threat_level = "LOW"
        if total_yara_matches > 0 or total_risk_score > 50 or high_risk_files > 0:
            threat_level = "HIGH"
        elif total_risk_score > 25:
            threat_level = "MEDIUM"

        results.update({
            "end_time": end_time,
            "execution_time_seconds": execution_time,
            "execution_time_minutes": execution_time / 60,
            "summary": {
                "phases_completed": phases_completed,
                "phases_with_errors": phases_with_errors,
                "total_yara_matches": total_yara_matches,
                "average_risk_score": total_risk_score,
                "high_risk_files": high_risk_files,
                "threat_level": threat_level,
                "recommendation": self._get_threat_recommendation(threat_level, total_yara_matches, high_risk_files)
            }
        })

        return results

    def _get_threat_recommendation(self, threat_level: str, yara_matches: int, high_risk_files: int) -> str:
        """Get threat recommendation based on analysis results."""
        if threat_level == "HIGH":
            if yara_matches > 0:
                return "QUARANTINE - Malware signatures detected"
            elif high_risk_files > 0:
                return "INVESTIGATE - High-risk files found"
            else:
                return "MONITOR - Elevated threat indicators"
        elif threat_level == "MEDIUM":
            return "REVIEW - Moderate risk indicators detected"
        else:
            return "ALLOW - No significant threats detected"

    def _display_continuous_summary(self, results: Dict[str, Any]) -> None:
        """Display comprehensive summary of continuous phases execution."""
        summary = results["summary"]

        self.logger.info("\n" + "="*80)
        self.logger.info("CONTINUOUS PHASES EXECUTION SUMMARY")
        self.logger.info("="*80)

        self.logger.info(f"Execution ID: {results['execution_id']}")
        self.logger.info(f"Target: {results['target_path']}")
        self.logger.info(f"Execution Time: {results['execution_time_minutes']:.1f} minutes")
        self.logger.info(f"Phases Completed: {summary['phases_completed']}/6")

        if summary['phases_with_errors'] > 0:
            self.logger.warning(f"Phases with Errors: {summary['phases_with_errors']}")

        self.logger.info(f"\nTHREAT ANALYSIS SUMMARY:")
        self.logger.info(f"  - Threat Level: {summary['threat_level']}")
        self.logger.info(f"  - YARA Matches: {summary['total_yara_matches']}")
        self.logger.info(f"  - Average Risk Score: {summary['average_risk_score']:.1f}/100")
        self.logger.info(f"  - High Risk Files: {summary['high_risk_files']}")

        self.logger.info(f"\nRECOMMENDATION: {summary['recommendation']}")

        # Phase-by-phase summary
        self.logger.info(f"\nPHASE EXECUTION DETAILS:")
        for phase_name, phase_result in results["phases"].items():
            if phase_result.get("skipped"):
                self.logger.info(f"  ⏭ {phase_name.upper()}: SKIPPED ({phase_result.get('reason', 'Unknown')})")
            elif "error" in phase_result:
                self.logger.error(f"  ✗ {phase_name.upper()}: FAILED ({phase_result['error']})")
            else:
                self.logger.info(f"  ✓ {phase_name.upper()}: COMPLETED")

        self.logger.info("="*80)

    def _show_help_and_examples(self):
        """Show help information and usage examples."""
        print("\n" + "="*80)
        print("SBARDS HELP & EXAMPLES")
        print("="*80)

        print("\n📁 DIRECTORY SCANNING FEATURES:")
        print("  • All phases (1-3, 9-10) support directory scanning")
        print("  • Recursive scanning of subdirectories")
        print("  • File type filtering (.exe, .dll, .pdf, etc.)")
        print("  • Configurable file limits for large directories")

        print("\n🔄 PHASE DESCRIPTIONS:")
        print("  1. Capture Layer - File/directory ingestion and validation")
        print("  2. Pre-Scanning - Quick YARA-based threat detection")
        print("  3. Static Analysis - Comprehensive file analysis")
        print("  4. Dynamic Analysis - Behavioral analysis in sandbox")
        print("  5-7. Advanced Phases - Run with Complete Workflow (9)")
        print("  8. Monitoring - Real-time system monitoring")
        print("  9. Complete Workflow - All phases with intelligent decisions")
        print("  10. Continuous Phases - Sequential execution of all phases")

        print("\n💡 USAGE EXAMPLES:")
        print("  Single File Analysis:")
        print("    • Option 3 → Enter: suspicious.exe")
        print("    • Option 9 → Enter: malware.dll")

        print("\n  Directory Analysis:")
        print("    • Option 2 → Enter: E:\\Downloads → y → 100")
        print("    • Option 3 → Enter: C:\\Temp → y → 50")
        print("    • Option 10 → Enter: E:\\WA → y → 20 → .exe .dll")

        print("\n  Quick Scans:")
        print("    • Option 2 for fast YARA scanning")
        print("    • Option 10 for comprehensive analysis")

        print("\n⚙️ CONFIGURATION OPTIONS:")
        print("  • Recursive: y/n (scan subdirectories)")
        print("  • Max files: number (e.g., 100, 500, 1000)")
        print("  • File types: extensions (e.g., .exe .dll .pdf)")

        print("\n🛡️ SECURITY RECOMMENDATIONS:")
        print("  • Start with Pre-Scanning (2) for quick assessment")
        print("  • Use Continuous Phases (10) for thorough analysis")
        print("  • Filter file types to focus on executables")
        print("  • Limit file count for large directories")

        print("\n📊 RESULT INTERPRETATION:")
        print("  • HIGH threat level → Immediate action required")
        print("  • MEDIUM threat level → Review and monitor")
        print("  • LOW threat level → Standard monitoring")
        print("  • YARA matches → Potential malware signatures")
        print("  • High risk score → Suspicious characteristics")

        print("\n🚀 BEST PRACTICES:")
        print("  • Test with small directories first")
        print("  • Use file type filtering for targeted scans")
        print("  • Monitor system resources during large scans")
        print("  • Review detailed logs in output directory")

        print("="*80)

    def run_interactive_mode(self):
        """Run interactive mode with menu selection."""
        self.logger.info("=== INTERACTIVE MODE ===")

        while True:
            try:
                self.show_phase_menu()
                choice = input("\nSelect phase (0-11): ").strip()

                if choice == "0":
                    self.logger.info("Exiting SBARDS...")
                    break

                elif choice == "1":
                    file_path = input("Enter file/directory path: ").strip()
                    if file_path:
                        recursive = input("Recursive scan? (y/n, default y): ").strip().lower() != 'n'
                        max_files = input("Max files (default 1000): ").strip()
                        max_files = int(max_files) if max_files.isdigit() else 1000
                        self.run_capture_phase(file_path, recursive=recursive, max_files=max_files)

                elif choice == "2":
                    file_path = input("Enter file/directory path: ").strip()
                    if file_path:
                        recursive = input("Recursive scan? (y/n, default y): ").strip().lower() != 'n'
                        max_files = input("Max files (default 1000): ").strip()
                        max_files = int(max_files) if max_files.isdigit() else 1000
                        self.run_prescanning_phase(file_path, recursive=recursive, max_files=max_files)

                elif choice == "3":
                    file_path = input("Enter file/directory path: ").strip()
                    if file_path:
                        recursive = input("Recursive scan? (y/n, default y): ").strip().lower() != 'n'
                        max_files = input("Max files (default 1000): ").strip()
                        max_files = int(max_files) if max_files.isdigit() else 1000
                        self.run_static_analysis_phase(file_path, recursive=recursive, max_files=max_files)

                elif choice == "4":
                    file_path = input("Enter file path: ").strip()
                    if file_path:
                        self.run_dynamic_analysis_phase(file_path)

                elif choice == "5":
                    print("Response phase requires analysis results from previous phases.")
                    print("Please run complete workflow (option 9) or continuous phases (option 10) instead.")

                elif choice == "6":
                    print("External integration runs automatically with complete workflow.")
                    print("Please run complete workflow (option 9) or continuous phases (option 10) instead.")

                elif choice == "7":
                    print("Memory protection runs automatically with complete workflow.")
                    print("Please run complete workflow (option 9) or continuous phases (option 10) instead.")

                elif choice == "8":
                    duration = input("Enter monitoring duration in minutes (default 60): ").strip()
                    duration = int(duration) if duration.isdigit() else 60
                    self.run_monitoring_phase(duration)

                elif choice == "9":
                    file_path = input("Enter file/directory path: ").strip()
                    if file_path:
                        self.run_complete_workflow(file_path)

                elif choice == "10":
                    file_path = input("Enter file/directory path: ").strip()
                    if file_path:
                        recursive = input("Recursive scan? (y/n, default y): ").strip().lower() != 'n'
                        max_files = input("Max files (default 1000): ").strip()
                        max_files = int(max_files) if max_files.isdigit() else 1000
                        file_types = input("File types (e.g., .exe .dll .pdf, leave empty for all): ").strip()
                        file_types = file_types.split() if file_types else None
                        self.run_continuous_phases(file_path, recursive=recursive, max_files=max_files, file_types=file_types)

                elif choice == "11":
                    self._show_help_and_examples()

                else:
                    print("Invalid choice. Please select 0-11.")

                input("\nPress Enter to continue...")

            except KeyboardInterrupt:
                self.logger.info("Interrupted by user")
                break
            except Exception as e:
                self.logger.error(f"Interactive mode error: {e}")
                input("Press Enter to continue...")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="SBARDS - Smart Behavioral Analysis and Ransomware Detection System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Available Phases:
  1. capture       - Capture Layer (supports directories)
  2. prescanning   - Pre-Scanning Quick Check (supports directories)
  3. static        - Static Analysis Layer (supports directories)
  4. dynamic       - Dynamic Analysis Layer
  5. response      - Response Layer
  6. monitoring    - Continuous Monitoring Layer
  7. workflow      - Complete Workflow
  8. continuous    - Continuous Phases (all phases sequentially)
  9. interactive   - Interactive Mode

Examples:
  python main.py --phase workflow --file /path/to/file
  python main.py --phase static --file /path/to/directory --recursive
  python main.py --phase continuous --file /path/to/directory --max-files 500
  python main.py --phase prescanning --file /path/to/dir --file-types .exe .dll
  python main.py --phase interactive
  python main.py --phase monitoring --duration 30
        """
    )

    parser.add_argument("--phase", "-p",
                       choices=["capture", "prescanning", "static", "dynamic",
                               "response", "monitoring", "workflow", "continuous", "interactive"],
                       default="interactive",
                       help="Phase to run")

    parser.add_argument("--file", "-f",
                       help="File path to analyze")

    parser.add_argument("--config", "-c",
                       default="config.json",
                       help="Configuration file path")

    parser.add_argument("--output", "-o",
                       default="output",
                       help="Output directory")

    parser.add_argument("--log-level", "-l",
                       choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       default="INFO",
                       help="Logging level")

    parser.add_argument("--duration", "-d",
                       type=int, default=60,
                       help="Monitoring duration in minutes")

    parser.add_argument("--recursive", "-r",
                       action="store_true",
                       help="Recursively scan subdirectories")

    parser.add_argument("--max-files",
                       type=int, default=1000,
                       help="Maximum number of files to process in directory scan")

    parser.add_argument("--file-types",
                       nargs="*",
                       help="Specific file types to scan (e.g., .exe .dll .pdf)")

    args = parser.parse_args()

    try:
        # Initialize SBARDS
        sbards = SBARDSMain(args.config)

        if not sbards.initialize(args.log_level):
            print("Failed to initialize SBARDS")
            return 1

        # Run selected phase
        if args.phase == "interactive":
            sbards.run_interactive_mode()

        elif args.phase == "monitoring":
            sbards.run_monitoring_phase(args.duration, args.output)

        elif args.phase in ["capture", "prescanning", "static", "dynamic", "workflow", "continuous"]:
            if not args.file:
                print(f"Error: --file is required for {args.phase} phase")
                return 1

            if args.phase == "capture":
                sbards.run_capture_phase(args.file, args.output, args.recursive, args.max_files, args.file_types)
            elif args.phase == "prescanning":
                sbards.run_prescanning_phase(args.file, args.output, args.recursive, args.max_files, args.file_types)
            elif args.phase == "static":
                sbards.run_static_analysis_phase(args.file, args.output, args.recursive, args.max_files, args.file_types)
            elif args.phase == "dynamic":
                sbards.run_dynamic_analysis_phase(args.file, args.output)
            elif args.phase == "workflow":
                sbards.run_complete_workflow(args.file, args.output)
            elif args.phase == "continuous":
                sbards.run_continuous_phases(args.file, args.output, args.recursive, args.max_files, args.file_types)

        elif args.phase == "response":
            print("Response phase requires analysis results from previous phases.")
            print("Please run complete workflow instead: python main.py --phase workflow --file <path>")
            return 1

        return 0

    except KeyboardInterrupt:
        print("\nInterrupted by user")
        return 1
    except Exception as e:
        print(f"Error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())