"""
Dependency Checker for SBARDS

This module provides functionality to check for outdated or vulnerable dependencies.
"""

import os
import sys
import json
import logging
import subprocess
from typing import Dict, List, Any, Optional, Tuple

logger = logging.getLogger("SBARDS.DependencyChecker")

def get_installed_packages() -> List[Dict[str, str]]:
    """
    Get a list of installed Python packages.
    
    Returns:
        List[Dict[str, str]]: List of installed packages with name and version
    """
    try:
        # Run pip list in JSON format
        result = subprocess.run(
            [sys.executable, "-m", "pip", "list", "--format=json"],
            capture_output=True,
            text=True,
            check=True
        )
        
        # Parse JSON output
        packages = json.loads(result.stdout)
        return packages
    except Exception as e:
        logger.error(f"Error getting installed packages: {e}")
        return []

def check_outdated_packages() -> List[Dict[str, str]]:
    """
    Check for outdated Python packages.
    
    Returns:
        List[Dict[str, str]]: List of outdated packages with name, current version, and latest version
    """
    try:
        # Run pip list --outdated in JSON format
        result = subprocess.run(
            [sys.executable, "-m", "pip", "list", "--outdated", "--format=json"],
            capture_output=True,
            text=True,
            check=True
        )
        
        # Parse JSON output
        outdated_packages = json.loads(result.stdout)
        return outdated_packages
    except Exception as e:
        logger.error(f"Error checking outdated packages: {e}")
        return []

def check_vulnerable_packages() -> List[Dict[str, Any]]:
    """
    Check for vulnerable Python packages using safety.
    
    Returns:
        List[Dict[str, Any]]: List of vulnerable packages with details
    """
    try:
        # Check if safety is installed
        try:
            import safety
        except ImportError:
            logger.warning("Safety is not installed. Installing...")
            subprocess.run(
                [sys.executable, "-m", "pip", "install", "safety"],
                capture_output=True,
                check=True
            )
        
        # Run safety check in JSON format
        result = subprocess.run(
            [sys.executable, "-m", "safety", "check", "--json"],
            capture_output=True,
            text=True
        )
        
        # Parse JSON output
        if result.returncode == 0:
            # No vulnerabilities found
            return []
        else:
            # Vulnerabilities found
            try:
                vulnerabilities = json.loads(result.stdout)
                return vulnerabilities
            except json.JSONDecodeError:
                logger.error("Error parsing safety output")
                return []
    except Exception as e:
        logger.error(f"Error checking vulnerable packages: {e}")
        return []

def update_package(package_name: str) -> bool:
    """
    Update a Python package.
    
    Args:
        package_name (str): Name of the package to update
        
    Returns:
        bool: True if the package was updated, False otherwise
    """
    try:
        # Run pip install --upgrade
        result = subprocess.run(
            [sys.executable, "-m", "pip", "install", "--upgrade", package_name],
            capture_output=True,
            text=True,
            check=True
        )
        
        logger.info(f"Updated package: {package_name}")
        return True
    except Exception as e:
        logger.error(f"Error updating package {package_name}: {e}")
        return False

def generate_requirements_file(output_path: str = "requirements.txt") -> bool:
    """
    Generate a requirements.txt file from installed packages.
    
    Args:
        output_path (str): Path to the output file
        
    Returns:
        bool: True if the file was generated, False otherwise
    """
    try:
        # Run pip freeze
        result = subprocess.run(
            [sys.executable, "-m", "pip", "freeze"],
            capture_output=True,
            text=True,
            check=True
        )
        
        # Write to file
        with open(output_path, "w") as f:
            f.write(result.stdout)
            
        logger.info(f"Generated requirements file: {output_path}")
        return True
    except Exception as e:
        logger.error(f"Error generating requirements file: {e}")
        return False

def check_dependencies() -> Dict[str, Any]:
    """
    Check dependencies and return a summary.
    
    Returns:
        Dict[str, Any]: Summary of dependency check
    """
    installed = get_installed_packages()
    outdated = check_outdated_packages()
    vulnerable = check_vulnerable_packages()
    
    # Create summary
    summary = {
        "total_packages": len(installed),
        "outdated_packages": len(outdated),
        "vulnerable_packages": len(vulnerable),
        "outdated_list": outdated,
        "vulnerable_list": vulnerable
    }
    
    # Log summary
    logger.info(f"Dependency check summary: {len(installed)} packages installed, "
                f"{len(outdated)} outdated, {len(vulnerable)} vulnerable")
    
    return summary

def main():
    """Main function for command-line usage."""
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Check dependencies
    summary = check_dependencies()
    
    # Print summary
    print(f"\nDependency Check Summary:")
    print(f"------------------------")
    print(f"Total packages: {summary['total_packages']}")
    print(f"Outdated packages: {summary['outdated_packages']}")
    print(f"Vulnerable packages: {summary['vulnerable_packages']}")
    
    # Print outdated packages
    if summary['outdated_packages'] > 0:
        print(f"\nOutdated Packages:")
        for package in summary['outdated_list']:
            print(f"  {package['name']}: {package['version']} -> {package['latest_version']}")
    
    # Print vulnerable packages
    if summary['vulnerable_packages'] > 0:
        print(f"\nVulnerable Packages:")
        for package in summary['vulnerable_list']:
            print(f"  {package['name']}: {package['version']}")
            print(f"    Vulnerability: {package['vulnerability']}")
            print(f"    Advisory: {package['advisory']}")
    
    # Generate requirements file
    generate_requirements_file()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
