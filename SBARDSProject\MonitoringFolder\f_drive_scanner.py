"""
F:\ Drive Scanner for SBARDS

This script performs a detailed scan of the F:\ drive for suspicious files and activities.
"""

import os
import sys
import json
import time
import shutil
import psutil
import datetime
import hashlib
import threading
import logging
from typing import Dict, List, Any, Set

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/f_drive_scan.log', mode='a')
    ]
)

logger = logging.getLogger("SBARDS.FDriveScanner")

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

# Load configuration
try:
    config_path = 'SBARDSProject/config.json'
    if not os.path.exists(config_path):
        config_path = 'config.json'
        if not os.path.exists(config_path):
            logger.error("Configuration file not found")
            sys.exit(1)
            
    with open(config_path, 'r') as f:
        config = json.load(f)
    logger.info("Configuration loaded successfully")
except Exception as e:
    logger.error(f"Error loading configuration: {e}")
    sys.exit(1)

class FDriveScanner:
    """Scanner for F:\ drive."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the scanner."""
        self.config = config
        self.logger = logging.getLogger("SBARDS.FDriveScanner")
        
        # Drive path
        self.drive_path = "F:\\"
        
        # File tracking
        self.file_hashes = {}
        self.suspicious_files = []
        self.suspicious_extensions = config.get("monitoring", {}).get("filesystem_monitoring", {}).get(
            "suspicious_extensions", []
        )
        
        # Scan thread
        self.scan_thread = None
        self.stop_event = threading.Event()
        self.is_running = False
        
        # Initial scan
        self.initial_scan()
        
    def initial_scan(self) -> None:
        """Perform initial scan of the drive."""
        self.logger.info(f"Performing initial scan of {self.drive_path}")
        
        try:
            # Check if drive exists
            if not os.path.exists(self.drive_path):
                self.logger.error(f"Drive {self.drive_path} not found")
                return
                
            # Get disk usage
            disk_usage = shutil.disk_usage(self.drive_path)
            self.logger.info(f"Total space: {disk_usage.total / (1024**3):.2f} GB")
            self.logger.info(f"Used space: {disk_usage.used / (1024**3):.2f} GB")
            self.logger.info(f"Free space: {disk_usage.free / (1024**3):.2f} GB")
            self.logger.info(f"Usage percentage: {disk_usage.used / disk_usage.total * 100:.2f}%")
            
            # List top-level directories
            self.logger.info(f"Top-level directories in {self.drive_path}:")
            for item in os.listdir(self.drive_path):
                item_path = os.path.join(self.drive_path, item)
                if os.path.isdir(item_path):
                    self.logger.info(f"  Directory: {item}")
                else:
                    self.logger.info(f"  File: {item}")
                    
            # Scan for suspicious files
            self.scan_for_suspicious_files()
            
        except Exception as e:
            self.logger.error(f"Error during initial scan: {e}")
            
    def scan_for_suspicious_files(self) -> None:
        """Scan for suspicious files."""
        self.logger.info("Scanning for suspicious files...")
        
        try:
            # Check if drive exists
            if not os.path.exists(self.drive_path):
                self.logger.error(f"Drive {self.drive_path} not found")
                return
                
            # Reset suspicious files
            self.suspicious_files = []
            
            # Walk through the drive
            for root, dirs, files in os.walk(self.drive_path):
                # Skip System Volume Information
                if "System Volume Information" in root:
                    continue
                    
                for file in files:
                    file_path = os.path.join(root, file)
                    
                    try:
                        # Check file extension
                        _, ext = os.path.splitext(file_path)
                        if ext.lower() in self.suspicious_extensions:
                            self.suspicious_files.append({
                                "path": file_path,
                                "extension": ext,
                                "size": os.path.getsize(file_path),
                                "modified": datetime.datetime.fromtimestamp(os.path.getmtime(file_path)).isoformat()
                            })
                            self.logger.warning(f"Suspicious file found: {file_path}")
                            
                        # Calculate file hash for tracking changes
                        if os.path.getsize(file_path) < 10 * 1024 * 1024:  # Only hash files smaller than 10MB
                            file_hash = self.calculate_file_hash(file_path)
                            if file_path in self.file_hashes:
                                if self.file_hashes[file_path] != file_hash:
                                    self.logger.warning(f"File changed: {file_path}")
                            self.file_hashes[file_path] = file_hash
                            
                    except Exception as e:
                        self.logger.error(f"Error processing file {file_path}: {e}")
                        
            self.logger.info(f"Found {len(self.suspicious_files)} suspicious files")
            
        except Exception as e:
            self.logger.error(f"Error scanning for suspicious files: {e}")
            
    def calculate_file_hash(self, file_path: str) -> str:
        """Calculate file hash."""
        try:
            with open(file_path, "rb") as f:
                file_hash = hashlib.md5()
                chunk = f.read(8192)
                while chunk:
                    file_hash.update(chunk)
                    chunk = f.read(8192)
                return file_hash.hexdigest()
        except Exception as e:
            self.logger.error(f"Error calculating hash for {file_path}: {e}")
            return ""
            
    def start_scanning(self) -> bool:
        """Start scanning."""
        if self.is_running:
            self.logger.warning("Scanning is already running")
            return True
            
        self.logger.info("Starting scanning")
        self.stop_event.clear()
        
        # Start scanning thread
        self.scan_thread = threading.Thread(
            target=self._scanning_loop,
            daemon=True
        )
        self.scan_thread.start()
        
        self.is_running = True
        return True
        
    def stop_scanning(self) -> bool:
        """Stop scanning."""
        if not self.is_running:
            return True
            
        self.logger.info("Stopping scanning")
        self.stop_event.set()
        
        if self.scan_thread:
            self.scan_thread.join(timeout=10.0)
            
        self.is_running = False
        return True
        
    def _scanning_loop(self) -> None:
        """Scanning loop."""
        scan_interval = 30  # Scan every 30 seconds
        
        while not self.stop_event.is_set():
            try:
                # Check disk usage
                if os.path.exists(self.drive_path):
                    disk_usage = shutil.disk_usage(self.drive_path)
                    self.logger.info(f"Free space: {disk_usage.free / (1024**3):.2f} GB")
                    
                    # Scan for suspicious files
                    self.scan_for_suspicious_files()
                    
                else:
                    self.logger.warning(f"Drive {self.drive_path} not found")
                    
                # Wait for next scan
                for _ in range(scan_interval):
                    if self.stop_event.is_set():
                        break
                    time.sleep(1)
                    
            except Exception as e:
                self.logger.error(f"Error during scanning: {e}")
                # Wait a bit before retrying
                self.stop_event.wait(1.0)
                
        self.logger.info("Scanning stopped")
        
    def get_suspicious_files(self) -> List[Dict[str, Any]]:
        """Get suspicious files."""
        return self.suspicious_files

def main():
    """Main entry point."""
    logger.info("Starting F:\ Drive Scanner")
    
    # Create scanner
    scanner = FDriveScanner(config)
    
    # Start scanning
    scanner.start_scanning()
    
    try:
        # Keep running until interrupted
        logger.info("Press Ctrl+C to stop scanning")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Stopping scanning")
    finally:
        # Stop scanning
        scanner.stop_scanning()
        logger.info("Scanning stopped")
        
        # Print summary
        suspicious_files = scanner.get_suspicious_files()
        logger.info(f"Found {len(suspicious_files)} suspicious files:")
        for file in suspicious_files:
            logger.info(f"  {file['path']} ({file['extension']}, {file['size']} bytes, modified: {file['modified']})")

if __name__ == "__main__":
    main()
