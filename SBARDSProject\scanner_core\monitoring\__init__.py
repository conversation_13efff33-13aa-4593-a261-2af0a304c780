"""
SBARDS Monitoring Layer

This module provides real-time behavioral monitoring capabilities for the SBARDS project.
It sits between the pre-inspection layer and subsequent analysis/response layers.
"""

from .monitor_manager import MonitorManager
from .process_monitor import ProcessMonitor
from .filesystem_monitor import FilesystemMonitor
from .network_monitor import NetworkMonitor
from .event_correlator import EventCorrelator
from .alert_manager import AlertManager
from .response_manager import ResponseManager

__all__ = [
    'MonitorManager',
    'ProcessMonitor',
    'FilesystemMonitor',
    'NetworkMonitor',
    'EventCorrelator',
    'AlertManager',
    'ResponseManager'
]
