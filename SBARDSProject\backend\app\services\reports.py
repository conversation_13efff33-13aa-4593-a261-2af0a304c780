"""
Report service.

This module provides services for managing scan reports.
"""

import json
import os
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple

from sqlalchemy.orm import Session
from sqlalchemy import func, desc

from ..core.logging import logger
from ..core.security import generate_scan_id
from ..db.models import ScanReport, FileResult
from ..schemas.reports import ScanReportCreate, FileResultCreate


def create_scan_report(db: Session, report: ScanReportCreate) -> ScanReport:
    """
    Create a new scan report.
    
    Args:
        db (Session): Database session.
        report (ScanReportCreate): Scan report data.
        
    Returns:
        ScanReport: Created scan report.
    """
    try:
        # Create scan report
        db_report = ScanReport(
            scan_id=report.scan_id,
            scan_path=report.scan_path,
            files_scanned=report.files_scanned,
            threats_found=report.threats_found,
            report_path=report.report_path,
            report_content=report.report_content
        )
        db.add(db_report)
        db.commit()
        db.refresh(db_report)
        
        # Add file results
        for file_result in report.file_results:
            db_file_result = FileResult(
                scan_report_id=db_report.id,
                file_path=file_result.file_path,
                file_hash=file_result.file_hash,
                is_threat=file_result.is_threat,
                threat_type=file_result.threat_type
            )
            db.add(db_file_result)
        
        db.commit()
        db.refresh(db_report)
        
        logger.info(f"Created scan report with ID: {report.scan_id}")
        return db_report
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating scan report: {e}")
        raise


def get_scan_reports(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    include_content: bool = False
) -> List[ScanReport]:
    """
    Get all scan reports.
    
    Args:
        db (Session): Database session.
        skip (int): Number of records to skip.
        limit (int): Maximum number of records to return.
        include_content (bool): Whether to include report content.
        
    Returns:
        List[ScanReport]: List of scan reports.
    """
    try:
        # Query reports ordered by timestamp (newest first)
        query = db.query(ScanReport).order_by(desc(ScanReport.timestamp))
        
        # Apply pagination
        reports = query.offset(skip).limit(limit).all()
        
        # If include_content is False, remove the report_content field to reduce response size
        if not include_content:
            for report in reports:
                report.report_content = None
        
        return reports
    except Exception as e:
        logger.error(f"Error getting scan reports: {e}")
        raise


def get_scan_report(
    db: Session,
    scan_id: str,
    include_content: bool = True
) -> Optional[ScanReport]:
    """
    Get a specific scan report by ID.
    
    Args:
        db (Session): Database session.
        scan_id (str): Scan ID.
        include_content (bool): Whether to include report content.
        
    Returns:
        Optional[ScanReport]: Scan report if found, None otherwise.
    """
    try:
        report = db.query(ScanReport).filter(ScanReport.scan_id == scan_id).first()
        
        if report and not include_content:
            report.report_content = None
        
        return report
    except Exception as e:
        logger.error(f"Error getting scan report {scan_id}: {e}")
        raise


def update_file_result_virustotal(
    db: Session,
    file_result_id: int,
    virustotal_result: Dict[str, Any]
) -> Optional[FileResult]:
    """
    Update a file result with VirusTotal data.
    
    Args:
        db (Session): Database session.
        file_result_id (int): File result ID.
        virustotal_result (Dict[str, Any]): VirusTotal result.
        
    Returns:
        Optional[FileResult]: Updated file result if found, None otherwise.
    """
    try:
        file_result = db.query(FileResult).filter(FileResult.id == file_result_id).first()
        
        if file_result:
            file_result.virustotal_result = json.dumps(virustotal_result)
            
            # Update threat status based on VirusTotal result
            if "data" in virustotal_result and "attributes" in virustotal_result["data"]:
                attributes = virustotal_result["data"]["attributes"]
                if "last_analysis_stats" in attributes:
                    stats = attributes["last_analysis_stats"]
                    if stats.get("malicious", 0) > 0:
                        file_result.is_threat = True
                        file_result.threat_type = f"VirusTotal: {stats.get('malicious', 0)} detections"
            
            db.commit()
            db.refresh(file_result)
            
            logger.info(f"Updated file result {file_result_id} with VirusTotal data")
            return file_result
        
        return None
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating file result {file_result_id} with VirusTotal data: {e}")
        raise


def parse_html_report(report_path: str) -> Dict[str, Any]:
    """
    Parse an HTML scan report to extract structured data.
    
    Args:
        report_path (str): Path to the HTML report.
        
    Returns:
        Dict[str, Any]: Extracted data from the report.
    """
    try:
        with open(report_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # Extract basic information from the HTML report
        # Extract scan path
        scan_path_match = re.search(r'<strong>Path:</strong>\s*([^<]+)', content)
        scan_path = scan_path_match.group(1).strip() if scan_path_match else ""
        
        # Extract files scanned
        files_scanned_match = re.search(r'<strong>Files Scanned:</strong>\s*(\d+)', content)
        files_scanned = int(files_scanned_match.group(1)) if files_scanned_match else 0
        
        # Extract threats found
        threats_found_match = re.search(r'<strong>Threats Found:</strong>\s*<span[^>]*>(\d+)</span>', content)
        threats_found = int(threats_found_match.group(1)) if threats_found_match else 0
        
        # Extract scan date
        scan_date_match = re.search(r'<strong>Scan Date:</strong>\s*([^<]+)', content)
        scan_date = scan_date_match.group(1).strip() if scan_date_match else ""
        
        return {
            "scan_path": scan_path,
            "files_scanned": files_scanned,
            "threats_found": threats_found,
            "scan_date": scan_date,
            "report_content": content
        }
    except Exception as e:
        logger.error(f"Error parsing HTML report {report_path}: {e}")
        return {
            "scan_path": "",
            "files_scanned": 0,
            "threats_found": 0,
            "scan_date": "",
            "report_content": ""
        }
