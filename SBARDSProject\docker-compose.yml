version: '3.8'

services:
  api:
    build: .
    command: python run.py --api
    ports:
      - "8000:8000"
    volumes:
      - ./logs:/app/logs
      - ./output:/app/output
      - ./rules:/app/rules
      - ./samples:/app/samples
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    networks:
      - sbards-network

  prescanning:
    build: .
    command: python run.py --prescanning
    volumes:
      - ./logs:/app/logs
      - ./output:/app/output
      - ./rules:/app/rules
      - ./samples:/app/samples
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    networks:
      - sbards-network

  monitoring:
    build: .
    command: python run.py --monitoring
    volumes:
      - ./logs:/app/logs
      - ./output:/app/output
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    networks:
      - sbards-network

  dashboard:
    build: .
    command: python dashboard/dashboard.py
    ports:
      - "5000:5000"
    volumes:
      - ./logs:/app/logs
      - ./output:/app/output
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    networks:
      - sbards-network

networks:
  sbards-network:
    driver: bridge
