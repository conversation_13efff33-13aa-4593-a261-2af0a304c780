import os
import time
import hashlib
import json
import logging
import watchdog.observers
import watchdog.events
from pathlib import Path

class DownloadMonitor:
    def __init__(self, config, scanner):
        self.config = config
        self.scanner = scanner
        self.logger = logging.getLogger("SBARDS.DownloadMonitor")

        # Setup paths to monitor
        self.paths_to_monitor = self._get_download_paths()

        # Setup hash database
        self.hash_db_path = os.path.join(
            self.config["output"]["output_directory"],
            "hash_database.json"
        )
        self.hash_db = self._load_hash_database()

        # Setup observers
        self.observers = []

    def _get_download_paths(self):
        """Get paths to monitor for downloads"""
        paths = []

        # Default download directories
        user_home = str(Path.home())

        # Common browser download paths
        common_download_paths = [
            os.path.join(user_home, "Downloads"),
            os.path.join(user_home, "Desktop")
        ]

        # Windows-specific browser download paths
        if os.name == 'nt':
            # Chrome downloads
            chrome_path = os.path.join(user_home, "AppData", "Local", "Google", "Chrome", "User Data", "Default", "Downloads")
            if os.path.exists(chrome_path):
                common_download_paths.append(chrome_path)

            # Edge downloads
            edge_path = os.path.join(user_home, "AppData", "Local", "Microsoft", "Edge", "User Data", "Default", "Downloads")
            if os.path.exists(edge_path):
                common_download_paths.append(edge_path)

            # Firefox downloads
            firefox_path = os.path.join(user_home, "AppData", "Local", "Mozilla", "Firefox", "Profiles")
            if os.path.exists(firefox_path):
                for profile in os.listdir(firefox_path):
                    profile_downloads = os.path.join(firefox_path, profile, "Downloads")
                    if os.path.exists(profile_downloads):
                        common_download_paths.append(profile_downloads)

            # WhatsApp downloads
            whatsapp_paths = [
                os.path.join(user_home, "AppData", "Roaming", "WhatsApp", "WhatsApp"),
                os.path.join(user_home, "Documents", "WhatsApp"),
                os.path.join(user_home, "Downloads", "WhatsApp")
            ]

            for path in whatsapp_paths:
                if os.path.exists(path):
                    common_download_paths.append(path)

        # Linux-specific browser download paths
        else:
            # Chrome/Chromium downloads
            chrome_paths = [
                os.path.join(user_home, ".config", "google-chrome", "Default", "Downloads"),
                os.path.join(user_home, ".config", "chromium", "Default", "Downloads")
            ]

            for path in chrome_paths:
                if os.path.exists(path):
                    common_download_paths.append(path)

            # Firefox downloads
            firefox_path = os.path.join(user_home, ".mozilla", "firefox")
            if os.path.exists(firefox_path):
                for item in os.listdir(firefox_path):
                    if item.endswith('.default'):
                        profile_downloads = os.path.join(firefox_path, item, "downloads")
                        if os.path.exists(profile_downloads):
                            common_download_paths.append(profile_downloads)

            # WhatsApp downloads
            whatsapp_paths = [
                os.path.join(user_home, ".config", "WhatsApp"),
                os.path.join(user_home, "Downloads", "WhatsApp")
            ]

            for path in whatsapp_paths:
                if os.path.exists(path):
                    common_download_paths.append(path)

        # Add all valid paths to the monitoring list
        for path in common_download_paths:
            if os.path.exists(path):
                paths.append(path)

        # Log the paths we're monitoring
        self.logger.info(f"Monitoring download paths: {paths}")
        return paths

    def _load_hash_database(self):
        """Load hash database from disk"""
        if os.path.exists(self.hash_db_path):
            try:
                with open(self.hash_db_path, 'r') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"Error loading hash database: {e}")
                return {}
        else:
            return {}

    def _save_hash_database(self):
        """Save hash database to disk"""
        try:
            os.makedirs(os.path.dirname(self.hash_db_path), exist_ok=True)
            with open(self.hash_db_path, 'w') as f:
                json.dump(self.hash_db, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving hash database: {e}")

    def calculate_file_hash(self, file_path):
        """Calculate SHA-256 hash of a file"""
        try:
            sha256_hash = hashlib.sha256()
            with open(file_path, "rb") as f:
                # Read in 1MB chunks to handle large files
                for byte_block in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(byte_block)
            return sha256_hash.hexdigest()
        except Exception as e:
            self.logger.error(f"Error calculating hash for {file_path}: {e}")
            return None

    def start_monitoring(self):
        """Start monitoring download directories"""
        self.logger.info("Starting download monitoring")

        class DownloadHandler(watchdog.events.FileSystemEventHandler):
            def __init__(self, parent):
                self.parent = parent
                self.last_cleanup_time = time.time()
                self.cleanup_interval = 24 * 60 * 60  # 24 hours in seconds

            def on_created(self, event):
                if not event.is_directory:
                    self.parent.process_new_file(event.src_path)
                    self._check_cleanup()

            def on_modified(self, event):
                if not event.is_directory:
                    self.parent.process_new_file(event.src_path)
                    self._check_cleanup()

            def _check_cleanup(self):
                # Periodically clean up the hash database
                current_time = time.time()
                if current_time - self.last_cleanup_time > self.cleanup_interval:
                    self.parent.cleanup_hash_database()
                    self.last_cleanup_time = current_time

        # Start observers for each path
        for path in self.paths_to_monitor:
            if os.path.exists(path):
                observer = watchdog.observers.Observer()
                observer.schedule(DownloadHandler(self), path, recursive=True)
                observer.start()
                self.observers.append(observer)
                self.logger.info(f"Started monitoring: {path}")
            else:
                self.logger.warning(f"Path does not exist, skipping: {path}")

        # Initial cleanup of the hash database
        self.cleanup_hash_database()

    def stop_monitoring(self):
        """Stop all observers"""
        for observer in self.observers:
            observer.stop()

        for observer in self.observers:
            observer.join()

        self._save_hash_database()
        self.logger.info("Download monitoring stopped")

    def process_new_file(self, file_path):
        """Process a newly created or modified file"""
        try:
            # Skip temporary files and partial downloads
            temp_extensions = ['.tmp', '.crdownload', '.part', '.partial', '.download']
            if any(file_path.lower().endswith(ext) for ext in temp_extensions):
                self.logger.debug(f"Skipping temporary file: {file_path}")
                return

            # Wait for file to be completely written
            # Some downloads might still be in progress
            try:
                file_size_1 = os.path.getsize(file_path)
                time.sleep(1)  # Short delay
                file_size_2 = os.path.getsize(file_path)

                # If file size is still changing, it's probably still being downloaded
                if file_size_1 != file_size_2:
                    self.logger.debug(f"File still being downloaded, will process later: {file_path}")
                    return
            except Exception as e:
                self.logger.debug(f"Error checking file size stability: {e}")

            # Check file size limit
            try:
                max_size_mb = self.config.get("scanner", {}).get("max_file_size_mb", 100)
                file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
                if file_size_mb > max_size_mb:
                    self.logger.warning(f"Skipping large file: {file_path} ({file_size_mb:.2f} MB > {max_size_mb} MB)")
                    return
            except Exception as e:
                self.logger.debug(f"Error checking file size: {e}")

            # Calculate file hash
            file_hash = self.calculate_file_hash(file_path)
            if not file_hash:
                return

            # Check if we've already scanned this hash
            if file_hash in self.hash_db:
                # Get the previous scan results
                prev_scan = self.hash_db[file_hash]
                self.logger.info(f"File already scanned (hash match): {file_path}")

                # Update the file path in case it's different from the previously scanned file
                prev_scan["file_path"] = file_path
                prev_scan["last_seen"] = time.time()

                # If the previous scan found threats, alert again
                if prev_scan.get("threats_found", False):
                    self.logger.warning(f"Previously detected threats found in file: {file_path}")
                    self._alert_user(file_path, prev_scan.get("results", []))

                return

            # Scan the file
            self.logger.info(f"Scanning new download: {file_path}")
            scan_results = self.scanner.scan_file(file_path)

            # Store the results in the hash database
            self.hash_db[file_hash] = {
                "file_path": file_path,
                "scan_time": time.time(),
                "last_seen": time.time(),
                "results": scan_results,
                "threats_found": len(scan_results) > 0,
                "file_size_bytes": os.path.getsize(file_path),
                "file_extension": os.path.splitext(file_path)[1].lower()
            }

            # Save the database periodically
            self._save_hash_database()

            # Alert if threats found
            if len(scan_results) > 0:
                self.logger.warning(f"Threats found in downloaded file: {file_path}")
                self._alert_user(file_path, scan_results)
            else:
                self.logger.info(f"No threats found in: {file_path}")

        except Exception as e:
            self.logger.error(f"Error processing file {file_path}: {e}")

    def _alert_user(self, file_path, scan_results):
        """Alert the user about threats found in a downloaded file"""
        # This could be extended to show desktop notifications, etc.
        threat_names = [result["rule"] for result in scan_results]
        self.logger.critical(f"SECURITY ALERT: Threats detected in {file_path}: {', '.join(threat_names)}")

    def cleanup_hash_database(self, max_age_days=30, max_entries=10000):
        """Clean up old entries from the hash database to prevent it from growing too large

        Args:
            max_age_days (int): Maximum age of entries in days
            max_entries (int): Maximum number of entries to keep
        """
        if not self.hash_db:
            return

        self.logger.info(f"Cleaning up hash database (current size: {len(self.hash_db)} entries)")

        # Remove entries older than max_age_days
        current_time = time.time()
        max_age_seconds = max_age_days * 24 * 60 * 60

        # First, remove by age
        entries_to_remove = []
        for file_hash, entry in self.hash_db.items():
            last_seen = entry.get("last_seen", entry.get("scan_time", 0))
            if current_time - last_seen > max_age_seconds:
                entries_to_remove.append(file_hash)

        for file_hash in entries_to_remove:
            del self.hash_db[file_hash]

        # If still too many entries, remove oldest ones
        if len(self.hash_db) > max_entries:
            # Sort entries by last_seen time
            sorted_entries = sorted(
                self.hash_db.items(),
                key=lambda x: x[1].get("last_seen", x[1].get("scan_time", 0))
            )

            # Keep only the newest max_entries
            entries_to_keep = sorted_entries[-max_entries:]

            # Create new hash_db with only the entries to keep
            new_hash_db = {}
            for file_hash, entry in entries_to_keep:
                new_hash_db[file_hash] = entry

            self.hash_db = new_hash_db

        self.logger.info(f"Hash database cleanup complete (new size: {len(self.hash_db)} entries)")
        self._save_hash_database()