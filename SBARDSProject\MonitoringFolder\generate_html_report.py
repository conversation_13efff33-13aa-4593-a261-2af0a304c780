"""
HTML Report Generator for SBARDS

This script generates an HTML report from the device monitoring results.
"""

import os
import sys
import json
import time
import datetime
import webbrowser
import logging
from typing import Dict, List, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("SBARDS.ReportGenerator")

def generate_html_report(devices, suspicious_files, output_path=None):
    """
    Generate HTML report.

    Args:
        devices (Dict[str, Dict[str, Any]]): Detected devices
        suspicious_files (List[Dict[str, Any]]): Suspicious files
        output_path (str): Output path for the HTML report

    Returns:
        str: Path to the generated HTML report
    """
    # Create output directory if it doesn't exist
    if output_path is None:
        output_dir = "reports"
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, f"scan_report_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.html")
    else:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

    logger.info(f"Generating HTML report: {output_path}")

    # Get current time
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Create HTML content
    html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SBARDS Device Monitoring Report</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }}
        h1, h2, h3 {{
            color: #2c3e50;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
        }}
        .header {{
            background-color: #3498db;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }}
        .section {{
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }}
        th, td {{
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        th {{
            background-color: #3498db;
            color: white;
        }}
        tr:hover {{
            background-color: #f5f5f5;
        }}
        .alert {{
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }}
        .success {{
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }}
        .progress-bar {{
            height: 20px;
            background-color: #e0e0e0;
            border-radius: 10px;
            margin-bottom: 10px;
        }}
        .progress {{
            height: 100%;
            background-color: #3498db;
            border-radius: 10px;
            text-align: center;
            color: white;
            line-height: 20px;
        }}
        .danger {{
            background-color: #e74c3c;
        }}
        .warning {{
            background-color: #f39c12;
        }}
        .safe {{
            background-color: #2ecc71;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>SBARDS Device Monitoring Report</h1>
            <p>Generated on: {current_time}</p>
        </div>

        <div class="section">
            <h2>Device Summary</h2>
            <p>Total devices detected: {len(devices)}</p>
            <table>
                <tr>
                    <th>Drive</th>
                    <th>Type</th>
                    <th>Label</th>
                    <th>Total Space</th>
                    <th>Free Space</th>
                    <th>Usage</th>
                </tr>
"""

    # Add device rows
    for path, device in devices.items():
        if "total" in device:
            total_gb = device["total"] / (1024**3)
            free_gb = device["free"] / (1024**3)
            percent_used = device["percent_used"]

            # Determine progress bar class
            progress_class = "safe"
            if percent_used > 90:
                progress_class = "danger"
            elif percent_used > 70:
                progress_class = "warning"

            html += f"""
                <tr>
                    <td>{path}</td>
                    <td>{device["type"]}</td>
                    <td>{device.get("label", "No Label")}</td>
                    <td>{total_gb:.2f} GB</td>
                    <td>{free_gb:.2f} GB</td>
                    <td>
                        <div class="progress-bar">
                            <div class="progress {progress_class}" style="width: {percent_used}%">
                                {percent_used:.2f}%
                            </div>
                        </div>
                    </td>
                </tr>
"""
        else:
            html += f"""
                <tr>
                    <td>{path}</td>
                    <td>{device["type"]}</td>
                    <td>{device.get("label", "No Label")}</td>
                    <td>N/A</td>
                    <td>N/A</td>
                    <td>N/A</td>
                </tr>
"""

    html += """
            </table>
        </div>
"""

    # Add suspicious files section
    html += f"""
        <div class="section">
            <h2>Suspicious Files</h2>
"""

    if suspicious_files:
        html += f"""
            <div class="alert">
                <strong>Warning!</strong> Found {len(suspicious_files)} suspicious files that may indicate ransomware or malicious activity.
            </div>
            <table>
                <tr>
                    <th>File Path</th>
                    <th>Extension</th>
                    <th>Size</th>
                    <th>Last Modified</th>
                </tr>
"""

        # Add suspicious file rows
        for file in suspicious_files:
            # Format file size
            if file["size"] < 1024:
                size_str = f"{file['size']} bytes"
            elif file["size"] < 1024**2:
                size_str = f"{file['size'] / 1024:.2f} KB"
            else:
                size_str = f"{file['size'] / (1024**2):.2f} MB"

            html += f"""
                <tr>
                    <td>{file["path"]}</td>
                    <td>{file["extension"]}</td>
                    <td>{size_str}</td>
                    <td>{file["modified"]}</td>
                </tr>
"""

        html += """
            </table>
"""
    else:
        html += """
            <div class="success">
                <strong>Good news!</strong> No suspicious files were found.
            </div>
"""

    html += """
        </div>

        <div class="section">
            <h2>Monitoring Information</h2>
            <p>The SBARDS monitoring system is actively scanning your devices for suspicious files and activities.</p>
            <p>If suspicious files are detected, they will be displayed in this report.</p>
            <p>This report is automatically updated periodically.</p>
        </div>
    </div>
</body>
</html>
"""

    # Write HTML to file
    try:
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(html)

        logger.info(f"HTML report generated: {output_path}")

        # Open report in browser
        report_url = f"file://{os.path.abspath(output_path)}"
        logger.info(f"Opening report in browser: {report_url}")

        try:
            webbrowser.open(report_url)
        except Exception as e:
            logger.error(f"Error opening report in browser: {e}")
            logger.info(f"Please open the report manually at: {output_path}")

        return output_path
    except Exception as e:
        logger.error(f"Error generating HTML report: {e}")
        return None

def main():
    """Main entry point."""
    logger.info("Starting HTML Report Generator")

    # Check if improved_device_monitor.py exists
    if not os.path.exists("improved_device_monitor.py"):
        logger.error("improved_device_monitor.py not found")
        return

    # Import improved_device_monitor
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from improved_device_monitor import ImprovedDeviceMonitor

        # Load configuration
        config_path = 'SBARDSProject/config.json'
        if not os.path.exists(config_path):
            config_path = 'config.json'
            if not os.path.exists(config_path):
                logger.error("Configuration file not found")
                return

        with open(config_path, 'r') as f:
            config = json.load(f)

        # Create monitor
        monitor = ImprovedDeviceMonitor(config)

        # Perform a full scan
        logger.info("Performing full scan of all devices...")

        # Detect devices
        monitor.detect_devices()

        # Scan all devices
        for device_path in monitor.devices:
            logger.info(f"Scanning device: {device_path}")
            monitor.scan_device(device_path)

        # Get devices and suspicious files
        devices = monitor.get_devices()
        suspicious_files = monitor.get_suspicious_files()

        logger.info(f"Scan complete. Found {len(suspicious_files)} suspicious files.")

        # Generate HTML report
        generate_html_report(devices, suspicious_files)

    except Exception as e:
        logger.error(f"Error generating report: {e}")

if __name__ == "__main__":
    main()
