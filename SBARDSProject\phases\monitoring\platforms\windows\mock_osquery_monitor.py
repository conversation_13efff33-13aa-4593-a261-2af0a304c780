"""
Mock OSQuery Monitor for SBARDS

This module provides a mock OSQuery monitor for the SBARDS project.
"""

import os
import time
import random
import logging
import threading
from typing import Dict, List, Any, Optional, Set
from ..mock_monitor import MockMonitor

class MockOSQueryMonitor(MockMonitor):
    """
    Mock OSQuery Monitor.
    
    This class provides a mock implementation of the OSQuery monitor.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the mock OSQuery monitor.
        
        Args:
            config (Dict[str, Any]): Configuration dictionary
        """
        super().__init__(config, "MockOSQueryMonitor")
        
        # Query types
        self.query_types = [
            "process",
            "file",
            "registry",
            "network"
        ]
        
        # Query counters
        self.query_counters = {query_type: 0 for query_type in self.query_types}
        
        # Last queries
        self.last_queries = []
        self.max_queries = 100
        
        # Monitored items
        self.processes = set()
        self.files = set()
        self.registry_keys = set()
        self.network_connections = set()
        
        # File change tracking
        self.file_change_history = {}
        
    def _generate_mock_events(self) -> None:
        """Generate mock OSQuery events."""
        # Run different query types
        for query_type in self.query_types:
            self._run_query(query_type)
            
    def _run_query(self, query_type: str) -> None:
        """
        Run mock query.
        
        Args:
            query_type (str): Query type
        """
        if query_type == "process":
            self._run_process_query()
        elif query_type == "file":
            self._run_file_query()
        elif query_type == "registry":
            self._run_registry_query()
        elif query_type == "network":
            self._run_network_query()
            
        # Update counter
        self.query_counters[query_type] += 1
        
    def _run_process_query(self) -> None:
        """Run mock process query."""
        # Generate random number of processes (3-10)
        num_processes = random.randint(3, 10)
        
        # Generate processes
        current_processes = set()
        for _ in range(num_processes):
            process = random.choice(self.mock_data["processes"])
            process_key = f"{process['name']}_{process['pid']}"
            current_processes.add(process_key)
            
        # Check for new processes
        new_processes = current_processes - self.processes
        for process_key in new_processes:
            if self.alert_manager and random.random() < 0.1:  # 10% chance of alert
                process_name = process_key.split("_")[0]
                self.alert_manager.add_alert(
                    source="MockOSQueryMonitor",
                    alert_type="new_process",
                    message=f"New process detected: {process_name}",
                    severity="info",
                    details={"process": process_name}
                )
                
        # Check for terminated processes
        terminated_processes = self.processes - current_processes
        for process_key in terminated_processes:
            if self.alert_manager and random.random() < 0.1:  # 10% chance of alert
                process_name = process_key.split("_")[0]
                self.alert_manager.add_alert(
                    source="MockOSQueryMonitor",
                    alert_type="terminated_process",
                    message=f"Process terminated: {process_name}",
                    severity="info",
                    details={"process": process_name}
                )
                
        # Update processes
        self.processes = current_processes
        
    def _run_file_query(self) -> None:
        """Run mock file query."""
        # Generate random number of files (3-10)
        num_files = random.randint(3, 10)
        
        # Generate files
        current_files = set()
        result = []
        for _ in range(num_files):
            file_info = random.choice(self.mock_data["files"])
            file_key = f"{file_info['path']}_{file_info['filename']}"
            current_files.add(file_key)
            
            # Add to result
            result.append({
                "path": file_info["path"],
                "filename": file_info["filename"],
                "size": file_info["size"],
                "mtime": file_info["mtime"]
            })
            
        # Check for new files
        new_files = current_files - self.files
        for file_key in new_files:
            if self.alert_manager and random.random() < 0.1:  # 10% chance of alert
                file_path = file_key.replace("_", "/")
                self.alert_manager.add_alert(
                    source="MockOSQueryMonitor",
                    alert_type="new_file",
                    message=f"New file detected: {file_path}",
                    severity="info",
                    details={"file_path": file_path}
                )
                
        # Check for deleted files
        deleted_files = self.files - current_files
        for file_key in deleted_files:
            if self.alert_manager and random.random() < 0.1:  # 10% chance of alert
                file_path = file_key.replace("_", "/")
                self.alert_manager.add_alert(
                    source="MockOSQueryMonitor",
                    alert_type="deleted_file",
                    message=f"File deleted: {file_path}",
                    severity="info",
                    details={"file_path": file_path}
                )
                
        # Check for modified files
        for file_info in result:
            file_path = os.path.join(file_info.get("path", ""), file_info.get("filename", ""))
            file_size = file_info.get("size", 0)
            file_mtime = file_info.get("mtime", 0)
            
            # Create a unique identifier for the file
            file_key = file_path.lower()
            
            # Check if we've seen this file before
            if file_key in self.file_change_history:
                old_size = self.file_change_history[file_key].get("size", 0)
                old_mtime = self.file_change_history[file_key].get("mtime", 0)
                
                # Check if the file has changed
                if old_size != file_size or old_mtime != file_mtime:
                    # File has changed
                    self.logger.debug(f"File changed: {file_path}")
                    
                    # Generate alert for file changes
                    if self.alert_manager and random.random() < 0.1:  # 10% chance of alert
                        self.alert_manager.add_alert(
                            source="MockOSQueryMonitor",
                            alert_type="file_change",
                            message=f"File changed: {file_path}",
                            severity="info",
                            details={
                                "file_path": file_path,
                                "old_size": old_size,
                                "new_size": file_size,
                                "old_mtime": old_mtime,
                                "new_mtime": file_mtime
                            }
                        )
                    
                    # Update file change history
                    self.file_change_history[file_key] = {
                        "size": file_size,
                        "mtime": file_mtime,
                        "last_change": time.time()
                    }
            else:
                # New file, add to history
                self.file_change_history[file_key] = {
                    "size": file_size,
                    "mtime": file_mtime,
                    "first_seen": time.time(),
                    "last_change": time.time()
                }
                
        # Update files
        self.files = current_files
        
    def _run_registry_query(self) -> None:
        """Run mock registry query."""
        # Generate random number of registry keys (3-10)
        num_keys = random.randint(3, 10)
        
        # Generate registry keys
        current_keys = set()
        for _ in range(num_keys):
            registry = random.choice(self.mock_data["registry"])
            key = registry["key"]
            current_keys.add(key)
            
        # Check for new keys
        new_keys = current_keys - self.registry_keys
        for key in new_keys:
            if self.alert_manager and random.random() < 0.1:  # 10% chance of alert
                self.alert_manager.add_alert(
                    source="MockOSQueryMonitor",
                    alert_type="new_registry_key",
                    message=f"New registry key: {key}",
                    severity="info",
                    details={"key": key}
                )
                
        # Check for deleted keys
        deleted_keys = self.registry_keys - current_keys
        for key in deleted_keys:
            if self.alert_manager and random.random() < 0.1:  # 10% chance of alert
                self.alert_manager.add_alert(
                    source="MockOSQueryMonitor",
                    alert_type="deleted_registry_key",
                    message=f"Registry key deleted: {key}",
                    severity="info",
                    details={"key": key}
                )
                
        # Update registry keys
        self.registry_keys = current_keys
        
    def _run_network_query(self) -> None:
        """Run mock network query."""
        # Generate random number of network connections (3-10)
        num_connections = random.randint(3, 10)
        
        # Generate network connections
        current_connections = set()
        for _ in range(num_connections):
            network = random.choice(self.mock_data["network"])
            connection = f"{network['source_ip']}:{network['source_port']}-{network['dest_ip']}:{network['dest_port']}"
            current_connections.add(connection)
            
        # Check for new connections
        new_connections = current_connections - self.network_connections
        for connection in new_connections:
            if self.alert_manager and random.random() < 0.1:  # 10% chance of alert
                self.alert_manager.add_alert(
                    source="MockOSQueryMonitor",
                    alert_type="new_network_connection",
                    message=f"New network connection: {connection}",
                    severity="info",
                    details={"connection": connection}
                )
                
        # Check for closed connections
        closed_connections = self.network_connections - current_connections
        for connection in closed_connections:
            if self.alert_manager and random.random() < 0.1:  # 10% chance of alert
                self.alert_manager.add_alert(
                    source="MockOSQueryMonitor",
                    alert_type="closed_network_connection",
                    message=f"Network connection closed: {connection}",
                    severity="info",
                    details={"connection": connection}
                )
                
        # Update network connections
        self.network_connections = current_connections
        
    def get_detailed_status(self) -> Dict[str, Any]:
        """
        Get detailed status.
        
        Returns:
            Dict[str, Any]: Detailed status
        """
        status = super().get_detailed_status()
        status.update({
            "query_counters": self.query_counters,
            "last_queries": len(self.last_queries),
            "processes": len(self.processes),
            "files": len(self.files),
            "registry_keys": len(self.registry_keys),
            "network_connections": len(self.network_connections),
            "file_changes": len(self.file_change_history)
        })
        
        return status
