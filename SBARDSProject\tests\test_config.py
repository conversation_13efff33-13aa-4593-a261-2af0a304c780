"""
Tests for the configuration module.
"""

import os
import json
import tempfile
import unittest
from core.config import ConfigLoader

class TestConfigLoader(unittest.TestCase):
    """Tests for the ConfigLoader class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary directory
        self.temp_dir = tempfile.TemporaryDirectory()
        
        # Create a temporary config file
        self.config_path = os.path.join(self.temp_dir.name, "config.json")
        self.test_config = {
            "scanner": {
                "target_directory": "test_samples",
                "recursive": True
            },
            "rules": {
                "rule_files": ["test_rules.yar"]
            },
            "output": {
                "log_directory": "test_logs",
                "output_directory": "test_output"
            }
        }
        
        with open(self.config_path, "w") as f:
            json.dump(self.test_config, f)
    
    def tearDown(self):
        """Tear down test fixtures."""
        # Remove the temporary directory
        self.temp_dir.cleanup()
    
    def test_load_config(self):
        """Test loading configuration from a file."""
        # Create config loader
        config_loader = ConfigLoader(self.config_path)
        
        # Get config
        config = config_loader.get_config()
        
        # Check config
        self.assertEqual(config["scanner"]["target_directory"], "test_samples")
        self.assertEqual(config["rules"]["rule_files"], ["test_rules.yar"])
        self.assertEqual(config["output"]["log_directory"], "test_logs")
    
    def test_get_section(self):
        """Test getting a section of the configuration."""
        # Create config loader
        config_loader = ConfigLoader(self.config_path)
        
        # Get section
        scanner_config = config_loader.get_section("scanner")
        
        # Check section
        self.assertEqual(scanner_config["target_directory"], "test_samples")
        self.assertEqual(scanner_config["recursive"], True)
    
    def test_default_config(self):
        """Test creating a default configuration."""
        # Create a non-existent config path
        non_existent_path = os.path.join(self.temp_dir.name, "non_existent.json")
        
        # Create config loader
        config_loader = ConfigLoader(non_existent_path)
        
        # Get config
        config = config_loader.get_config()
        
        # Check config
        self.assertIn("scanner", config)
        self.assertIn("rules", config)
        self.assertIn("output", config)
        self.assertIn("performance", config)
        self.assertIn("api", config)
        self.assertIn("monitoring", config)
        self.assertIn("integration", config)
    
    def test_save_config(self):
        """Test saving configuration to a file."""
        # Create config loader
        config_loader = ConfigLoader(self.config_path)
        
        # Modify config
        config = config_loader.get_config()
        config["scanner"]["target_directory"] = "new_samples"
        
        # Save config
        config_loader.save_config()
        
        # Create a new config loader to load the saved config
        new_config_loader = ConfigLoader(self.config_path)
        new_config = new_config_loader.get_config()
        
        # Check config
        self.assertEqual(new_config["scanner"]["target_directory"], "new_samples")

if __name__ == "__main__":
    unittest.main()
