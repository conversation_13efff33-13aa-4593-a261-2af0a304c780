"""
Alert Manager for SBARDS

This module provides alert management for the SBARDS project.
"""

import os
import time
import json
import logging
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional, Set

class AlertManager:
    """
    Alert Manager for SBARDS.

    This class manages alerts generated by monitoring components.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the alert manager.

        Args:
            config (Dict[str, Any]): Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger("SBARDS.AlertManager")

        # Alerts
        self.alerts = []
        self.alerts_lock = threading.Lock()

        # Alert thread
        self.alert_thread = None
        self.stop_event = threading.Event()
        self.is_running = False

        # Alert processing interval
        self.alert_interval = config.get("alerts", {}).get("processing_interval_seconds", 5)

        # Alert file
        self.alert_file = os.path.join(
            config.get("output", {}).get("output_directory", "output"),
            "alerts.json"
        )

        # Response manager
        from phases.monitoring.alerts.response_manager import ResponseManager
        self.response_manager = ResponseManager(config)

        # Load existing alerts
        self._load_alerts()

        self.logger.info("Alert Manager initialized")

    def _load_alerts(self) -> None:
        """Load existing alerts from file."""
        if os.path.exists(self.alert_file):
            try:
                with open(self.alert_file, "r", encoding="utf-8") as f:
                    self.alerts = json.load(f)

                self.logger.info(f"Loaded {len(self.alerts)} alerts from {self.alert_file}")
            except Exception as e:
                self.logger.error(f"Error loading alerts: {e}")

    def _save_alerts(self) -> None:
        """Save alerts to file."""
        try:
            # Create output directory if it doesn't exist
            os.makedirs(os.path.dirname(self.alert_file), exist_ok=True)

            with open(self.alert_file, "w", encoding="utf-8") as f:
                json.dump(self.alerts, f, indent=4)

            self.logger.debug(f"Saved {len(self.alerts)} alerts to {self.alert_file}")
        except Exception as e:
            self.logger.error(f"Error saving alerts: {e}")

    def start(self) -> bool:
        """
        Start alert processing.

        Returns:
            bool: True if successful, False otherwise
        """
        if self.is_running:
            self.logger.warning("Alert processing is already running")
            return True

        self.logger.info("Starting alert processing")
        self.stop_event.clear()

        # Start response manager
        self.response_manager.start()

        # Start alert thread
        self.alert_thread = threading.Thread(
            target=self._alert_loop,
            daemon=True
        )
        self.alert_thread.start()

        self.is_running = True
        return True

    def stop(self) -> bool:
        """
        Stop alert processing.

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.is_running:
            return True

        self.logger.info("Stopping alert processing")
        self.stop_event.set()

        # Stop response manager
        self.response_manager.stop()

        if self.alert_thread:
            self.alert_thread.join(timeout=10.0)

        self.is_running = False
        return True

    def _alert_loop(self) -> None:
        """Alert processing loop."""
        while not self.stop_event.is_set():
            try:
                # Process alerts
                self._process_alerts()

                # Save alerts
                self._save_alerts()

                # Wait for next alert processing cycle
                self.stop_event.wait(self.alert_interval)

            except Exception as e:
                self.logger.error(f"Error during alert processing: {e}")
                # Wait a bit before retrying
                self.stop_event.wait(1.0)

        self.logger.info("Alert processing stopped")

    def _process_alerts(self) -> None:
        """Process alerts."""
        with self.alerts_lock:
            # Get unprocessed alerts
            unprocessed_alerts = [
                alert for alert in self.alerts
                if not alert.get("processed", False)
            ]

            # Process alerts
            for alert in unprocessed_alerts:
                # Mark as processed
                alert["processed"] = True
                alert["processed_time"] = datetime.now().isoformat()

                # Send to response manager
                self.response_manager.handle_alert(alert)

    def add_alert(self, source: str, alert_type: str, message: str, severity: str = "info", details: Optional[Dict[str, Any]] = None) -> None:
        """
        Add an alert.

        Args:
            source (str): Alert source
            alert_type (str): Alert type
            message (str): Alert message
            severity (str): Alert severity (info, warning, error, critical)
            details (Optional[Dict[str, Any]]): Alert details
        """
        # Create alert
        alert = {
            "id": len(self.alerts) + 1,
            "source": source,
            "type": alert_type,
            "message": message,
            "severity": severity,
            "time": datetime.now().isoformat(),
            "details": details or {},
            "processed": False
        }

        # Add alert
        with self.alerts_lock:
            self.alerts.append(alert)

        # Log alert
        log_level = {
            "info": logging.INFO,
            "warning": logging.WARNING,
            "error": logging.ERROR,
            "critical": logging.CRITICAL
        }.get(severity.lower(), logging.INFO)

        self.logger.log(log_level, f"Alert: {message}")

    def get_alerts(self, limit: int = 100, severity: Optional[str] = None, alert_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get alerts.

        Args:
            limit (int): Maximum number of alerts to return
            severity (Optional[str]): Filter alerts by severity
            alert_type (Optional[str]): Filter alerts by type

        Returns:
            List[Dict[str, Any]]: List of alerts
        """
        with self.alerts_lock:
            # Start with all alerts
            filtered_alerts = self.alerts

            # Filter alerts by severity
            if severity:
                filtered_alerts = [
                    alert for alert in filtered_alerts
                    if alert.get("severity", "").lower() == severity.lower()
                ]

            # Filter alerts by type
            if alert_type:
                filtered_alerts = [
                    alert for alert in filtered_alerts
                    if alert.get("type", "").lower() == alert_type.lower()
                ]

            # Sort alerts by time (newest first)
            sorted_alerts = sorted(
                filtered_alerts,
                key=lambda a: a.get("time", ""),
                reverse=True
            )

            # Limit number of alerts
            return sorted_alerts[:limit]

    def clear_alerts(self) -> None:
        """Clear all alerts."""
        with self.alerts_lock:
            self.alerts = []

        self.logger.info("Alerts cleared")
