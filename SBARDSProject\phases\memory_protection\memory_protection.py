"""
Memory Protection Layer for SBARDS

This module provides memory protection capabilities including:
- Memory scanning for malicious patterns
- Process memory analysis
- Memory dump analysis
- Buffer overflow detection
- Heap spray detection
"""

import os
import sys
import time
import logging
import psutil
import hashlib
from typing import Dict, List, Any, Optional
from datetime import datetime


class MemoryProtectionLayer:
    """
    Memory Protection Layer for SBARDS.

    Provides memory protection and analysis capabilities for detecting
    memory-based threats and attacks.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the Memory Protection Layer.

        Args:
            config (Dict[str, Any]): Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger("SBARDS.MemoryProtection")

        # Memory protection settings
        self.memory_config = config.get("memory_protection", {})
        self.scan_interval = self.memory_config.get("scan_interval_seconds", 60)
        self.memory_threshold = self.memory_config.get("memory_threshold_mb", 100)
        self.suspicious_patterns = self.memory_config.get("suspicious_patterns", [])

        # Process monitoring
        self.monitored_processes = set()
        self.suspicious_processes = []

        # Memory analysis results
        self.analysis_results = []

        self.logger.info("Memory Protection Layer initialized")

    def scan_process_memory(self, pid: int) -> Dict[str, Any]:
        """
        Scan memory of a specific process.

        Args:
            pid (int): Process ID to scan

        Returns:
            Dict[str, Any]: Memory scan results
        """
        results = {
            "pid": pid,
            "timestamp": datetime.now().isoformat(),
            "memory_usage": {},
            "suspicious_patterns": [],
            "risk_score": 0,
            "status": "unknown"
        }

        try:
            # Get process information
            process = psutil.Process(pid)
            process_info = {
                "name": process.name(),
                "exe": process.exe() if process.exe() else "unknown",
                "cmdline": " ".join(process.cmdline()) if process.cmdline() else "unknown",
                "create_time": process.create_time(),
                "status": process.status()
            }

            # Get memory information
            memory_info = process.memory_info()
            memory_percent = process.memory_percent()

            results["process_info"] = process_info
            results["memory_usage"] = {
                "rss": memory_info.rss,
                "vms": memory_info.vms,
                "percent": memory_percent,
                "rss_mb": memory_info.rss / (1024 * 1024),
                "vms_mb": memory_info.vms / (1024 * 1024)
            }

            # Check for suspicious memory usage
            if memory_percent > 50:  # High memory usage
                results["suspicious_patterns"].append({
                    "type": "high_memory_usage",
                    "description": f"Process using {memory_percent:.1f}% of system memory",
                    "severity": "medium"
                })
                results["risk_score"] += 30

            # Check for rapid memory growth (placeholder)
            if self._check_rapid_memory_growth(pid):
                results["suspicious_patterns"].append({
                    "type": "rapid_memory_growth",
                    "description": "Process showing rapid memory allocation",
                    "severity": "high"
                })
                results["risk_score"] += 50

            # Check for suspicious process characteristics
            if self._is_suspicious_process(process_info):
                results["suspicious_patterns"].append({
                    "type": "suspicious_process",
                    "description": "Process has suspicious characteristics",
                    "severity": "high"
                })
                results["risk_score"] += 40

            # Determine overall status
            if results["risk_score"] >= 70:
                results["status"] = "high_risk"
            elif results["risk_score"] >= 40:
                results["status"] = "medium_risk"
            elif results["risk_score"] > 0:
                results["status"] = "low_risk"
            else:
                results["status"] = "clean"

            self.logger.debug(f"Memory scan completed for PID {pid}: {results['status']}")

        except psutil.NoSuchProcess:
            results["error"] = "Process not found"
            self.logger.warning(f"Process {pid} not found during memory scan")
        except psutil.AccessDenied:
            results["error"] = "Access denied"
            self.logger.warning(f"Access denied for process {pid}")
        except Exception as e:
            results["error"] = str(e)
            self.logger.error(f"Error scanning memory for PID {pid}: {e}")

        return results

    def scan_all_processes(self) -> Dict[str, Any]:
        """
        Scan memory of all running processes.

        Returns:
            Dict[str, Any]: Comprehensive memory scan results
        """
        results = {
            "timestamp": datetime.now().isoformat(),
            "total_processes": 0,
            "scanned_processes": 0,
            "suspicious_processes": [],
            "high_risk_processes": [],
            "system_memory": {},
            "summary": {}
        }

        try:
            # Get system memory information
            memory = psutil.virtual_memory()
            results["system_memory"] = {
                "total": memory.total,
                "available": memory.available,
                "percent": memory.percent,
                "used": memory.used,
                "free": memory.free,
                "total_gb": memory.total / (1024**3),
                "available_gb": memory.available / (1024**3)
            }

            # Scan all processes
            processes = list(psutil.process_iter(['pid', 'name', 'memory_percent']))
            results["total_processes"] = len(processes)

            for proc in processes:
                try:
                    pid = proc.info['pid']

                    # Skip system processes
                    if pid in [0, 4]:  # System and System Idle Process on Windows
                        continue

                    # Scan process memory
                    scan_result = self.scan_process_memory(pid)
                    results["scanned_processes"] += 1

                    # Collect suspicious processes
                    if scan_result.get("risk_score", 0) > 0:
                        results["suspicious_processes"].append(scan_result)

                    if scan_result.get("status") == "high_risk":
                        results["high_risk_processes"].append(scan_result)

                except Exception as e:
                    self.logger.debug(f"Error scanning process {proc.info.get('pid', 'unknown')}: {e}")
                    continue

            # Generate summary
            results["summary"] = {
                "suspicious_count": len(results["suspicious_processes"]),
                "high_risk_count": len(results["high_risk_processes"]),
                "scan_completion_rate": (results["scanned_processes"] / results["total_processes"]) * 100
            }

            self.logger.info(f"Memory scan completed: {results['scanned_processes']}/{results['total_processes']} processes scanned")
            self.logger.info(f"Found {len(results['suspicious_processes'])} suspicious processes")

        except Exception as e:
            self.logger.error(f"Error during comprehensive memory scan: {e}")
            results["error"] = str(e)

        return results

    def _check_rapid_memory_growth(self, pid: int) -> bool:
        """
        Check if a process is showing rapid memory growth.

        Args:
            pid (int): Process ID to check

        Returns:
            bool: True if rapid growth detected
        """
        try:
            # This is a placeholder implementation
            # In a real implementation, you would track memory usage over time
            process = psutil.Process(pid)
            memory_percent = process.memory_percent()

            # Simple heuristic: if process is using more than 20% memory, consider it rapid growth
            return memory_percent > 20

        except Exception:
            return False

    def _is_suspicious_process(self, process_info: Dict[str, Any]) -> bool:
        """
        Check if a process has suspicious characteristics.

        Args:
            process_info (Dict[str, Any]): Process information

        Returns:
            bool: True if process is suspicious
        """
        try:
            name = process_info.get("name", "").lower()
            exe = process_info.get("exe", "").lower()
            cmdline = process_info.get("cmdline", "").lower()

            # Check for suspicious process names
            suspicious_names = [
                "cryptolocker", "wannacry", "petya", "notpetya", "ryuk",
                "maze", "sodinokibi", "revil", "darkside", "conti"
            ]

            for suspicious_name in suspicious_names:
                if suspicious_name in name or suspicious_name in exe:
                    return True

            # Check for suspicious command line patterns
            suspicious_patterns = [
                "vssadmin delete shadows",
                "wbadmin delete catalog",
                "bcdedit /set {default} bootstatuspolicy ignoreallfailures",
                "bcdedit /set {default} recoveryenabled no",
                "wmic shadowcopy delete",
                "cipher /w:"
            ]

            for pattern in suspicious_patterns:
                if pattern in cmdline:
                    return True

            return False

        except Exception:
            return False

    def create_memory_dump(self, pid: int, output_dir: str = "memory_dumps") -> Dict[str, Any]:
        """
        Create a memory dump of a specific process.

        Args:
            pid (int): Process ID to dump
            output_dir (str): Output directory for dump files

        Returns:
            Dict[str, Any]: Memory dump results
        """
        results = {
            "pid": pid,
            "timestamp": datetime.now().isoformat(),
            "dump_file": None,
            "success": False
        }

        try:
            # Create output directory
            os.makedirs(output_dir, exist_ok=True)

            # Get process information
            process = psutil.Process(pid)
            process_name = process.name()

            # Create dump filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            dump_filename = f"memory_dump_{process_name}_{pid}_{timestamp}.dmp"
            dump_path = os.path.join(output_dir, dump_filename)

            # Placeholder for actual memory dump creation
            # In a real implementation, you would use platform-specific APIs
            # For now, we'll create a placeholder file with process information
            dump_info = {
                "pid": pid,
                "process_name": process_name,
                "memory_info": process.memory_info()._asdict(),
                "create_time": process.create_time(),
                "dump_timestamp": timestamp,
                "note": "This is a placeholder dump file. Real implementation would contain actual memory content."
            }

            with open(dump_path, 'w') as f:
                import json
                json.dump(dump_info, f, indent=2)

            results["dump_file"] = dump_path
            results["success"] = True

            self.logger.info(f"Memory dump created for PID {pid}: {dump_path}")

        except Exception as e:
            self.logger.error(f"Error creating memory dump for PID {pid}: {e}")
            results["error"] = str(e)

        return results

    def get_protection_status(self) -> Dict[str, Any]:
        """
        Get current memory protection status.

        Returns:
            Dict[str, Any]: Protection status
        """
        try:
            memory = psutil.virtual_memory()

            return {
                "enabled": True,
                "scan_interval": self.scan_interval,
                "memory_threshold": self.memory_threshold,
                "system_memory": {
                    "total_gb": memory.total / (1024**3),
                    "available_gb": memory.available / (1024**3),
                    "usage_percent": memory.percent
                },
                "monitored_processes": len(self.monitored_processes),
                "suspicious_processes": len(self.suspicious_processes),
                "last_scan": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error getting protection status: {e}")
            return {"enabled": False, "error": str(e)}