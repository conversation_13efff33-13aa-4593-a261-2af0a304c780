"""
Authentication endpoints for the SBARDS Backend API.

This module provides authentication endpoints for the SBARDS Backend API.
"""

from datetime import timedelta
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRe<PERSON>Form
from pydantic import BaseModel

from ....core.config import settings
from ....core.logging import logger
from ....core.security import create_access_token, verify_token

# Create router
router = APIRouter()


class Token(BaseModel):
    """Token schema."""
    
    access_token: str
    token_type: str


class TokenData(BaseModel):
    """Token data schema."""
    
    username: str


@router.post(
    "/token",
    response_model=Token,
    summary="Get access token",
    description="Get an access token for API authentication."
)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    """
    Get an access token for API authentication.
    
    - **username**: Username
    - **password**: Password
    
    Returns an access token if credentials are valid.
    """
    # In a real application, you would validate the username and password against a database
    # For this example, we'll use a simple hardcoded check
    if form_data.username != settings.API_USERNAME or form_data.password != settings.API_PASSWORD:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Create access token
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": form_data.username},
        expires_delta=access_token_expires
    )
    
    return {"access_token": access_token, "token_type": "bearer"}


@router.get(
    "/me",
    response_model=Dict[str, Any],
    summary="Get current user",
    description="Get information about the current authenticated user."
)
async def read_users_me(token_data: Dict[str, Any] = Depends(verify_token)):
    """
    Get information about the current authenticated user.
    
    Returns information about the current authenticated user.
    """
    return {"username": token_data.get("sub"), "token_data": token_data}
