"""
Rate limiting middleware for the SBARDS Backend API.

This module provides rate limiting middleware for the SBARDS Backend API.
"""

import time
from typing import Dict, Callable, Optional

from fastapi import Request, Response, status
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGI<PERSON>pp

from ..core.logging import logger


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Rate limiting middleware."""
    
    def __init__(
        self,
        app: ASGIApp,
        rate_limit_duration: int = 60,  # 1 minute
        rate_limit_requests: int = 100,  # 100 requests per minute
    ):
        """
        Initialize rate limiting middleware.
        
        Args:
            app (ASGIApp): ASGI application.
            rate_limit_duration (int): Rate limit duration in seconds.
            rate_limit_requests (int): Maximum number of requests per duration.
        """
        super().__init__(app)
        self.rate_limit_duration = rate_limit_duration
        self.rate_limit_requests = rate_limit_requests
        self.rate_limit_store: Dict[str, Dict[str, int]] = {}
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Dispatch request with rate limiting.
        
        Args:
            request (Request): FastAPI request.
            call_next (Callable): Next middleware or route handler.
            
        Returns:
            Response: FastAPI response.
        """
        # Skip rate limiting for certain paths
        if self._should_skip_rate_limiting(request.url.path):
            return await call_next(request)
        
        # Get client identifier (IP address or API key)
        client_id = self._get_client_id(request)
        
        # Check rate limit
        if not self._check_rate_limit(client_id):
            logger.warning(f"Rate limit exceeded for client: {client_id}")
            return Response(
                content="Rate limit exceeded. Please try again later.",
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                headers={"Retry-After": str(self._get_retry_after(client_id))}
            )
        
        # Process request
        return await call_next(request)
    
    def _should_skip_rate_limiting(self, path: str) -> bool:
        """
        Check if rate limiting should be skipped for a path.
        
        Args:
            path (str): Request path.
            
        Returns:
            bool: True if rate limiting should be skipped, False otherwise.
        """
        # Skip rate limiting for static files and health check
        skip_paths = ["/static/", "/api/health"]
        return any(path.startswith(skip_path) for skip_path in skip_paths)
    
    def _get_client_id(self, request: Request) -> str:
        """
        Get client identifier.
        
        Args:
            request (Request): FastAPI request.
            
        Returns:
            str: Client identifier.
        """
        # Use API key if available, otherwise use client IP
        api_key = request.headers.get("X-API-Key")
        if api_key:
            return f"api_key:{api_key}"
        
        # Get client IP
        client_ip = request.client.host if request.client else "unknown"
        return f"ip:{client_ip}"
    
    def _check_rate_limit(self, client_id: str) -> bool:
        """
        Check if client has exceeded rate limit.
        
        Args:
            client_id (str): Client identifier.
            
        Returns:
            bool: True if client has not exceeded rate limit, False otherwise.
        """
        # Get current timestamp
        now = int(time.time())
        
        # Initialize rate limit entry if not exists
        if client_id not in self.rate_limit_store:
            self.rate_limit_store[client_id] = {"count": 0, "reset_at": now + self.rate_limit_duration}
        
        # Reset count if duration has passed
        if self.rate_limit_store[client_id]["reset_at"] <= now:
            self.rate_limit_store[client_id] = {"count": 0, "reset_at": now + self.rate_limit_duration}
        
        # Increment count
        self.rate_limit_store[client_id]["count"] += 1
        
        # Check if rate limit is exceeded
        return self.rate_limit_store[client_id]["count"] <= self.rate_limit_requests
    
    def _get_retry_after(self, client_id: str) -> int:
        """
        Get retry after time in seconds.
        
        Args:
            client_id (str): Client identifier.
            
        Returns:
            int: Retry after time in seconds.
        """
        now = int(time.time())
        reset_at = self.rate_limit_store.get(client_id, {}).get("reset_at", now + self.rate_limit_duration)
        return max(1, reset_at - now)
