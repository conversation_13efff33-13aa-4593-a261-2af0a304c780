import sys
import os
import re
import logging
import json
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("MockYaraScanner")

def contains_pattern(content, pattern):
    """Check if a pattern exists in content (case-insensitive)"""
    return pattern.lower() in content.lower()

def read_rules_file(rules_file):
    """Read and parse a simplified YARA rules file"""
    try:
        with open(rules_file, 'r', encoding='utf-8', errors='ignore') as file:
            content = file.read()

        # Extract rule names and patterns using regex
        rules = []
        rule_blocks = re.findall(r'rule\s+(\w+)\s*{([^}]+)}', content, re.DOTALL)

        for rule_name, rule_body in rule_blocks:
            # Extract meta information
            meta = {}
            meta_block = re.search(r'meta:\s*{([^}]+)}', rule_body, re.DOTALL)
            if meta_block:
                meta_content = meta_block.group(1)
                meta_items = re.findall(r'(\w+)\s*=\s*"([^"]+)"', meta_content)
                for key, value in meta_items:
                    meta[key] = value

            # Extract strings
            strings = []
            strings_block = re.search(r'strings:\s*(.+?)\s*condition:', rule_body, re.DOTALL)
            if strings_block:
                strings_content = strings_block.group(1)
                string_items = re.findall(r'\$(\w+)\s*=\s*"([^"]+)"', strings_content)
                for var_name, pattern in string_items:
                    strings.append({"name": var_name, "pattern": pattern})

            rules.append({
                "name": rule_name,
                "meta": meta,
                "strings": strings
            })

        return rules
    except Exception as e:
        logger.error(f"Error reading rules file: {e}")
        return []

def scan_file(file_path, rules):
    """Scan a file with the provided rules"""
    try:
        # Read the file with proper Unicode handling
        with open(file_path, 'rb') as file:
            raw_content = file.read()

        # Try different encodings
        encodings = ['utf-8', 'latin-1', 'cp1252']
        content = None

        for encoding in encodings:
            try:
                content = raw_content.decode(encoding)
                break
            except UnicodeDecodeError:
                continue

        if content is None:
            # If all encodings fail, use latin-1 as a fallback
            content = raw_content.decode('latin-1', errors='replace')

        # Match rules
        matches = []

        for rule in rules:
            rule_matched = False
            matched_strings = []

            # Check each string pattern in the rule
            for string_def in rule.get("strings", []):
                if contains_pattern(content, string_def["pattern"]):
                    matched_strings.append(string_def["name"])
                    rule_matched = True

            # Special case for known rules
            if rule["name"] == "ExampleRule" and contains_pattern(content, "malicious"):
                rule_matched = True
                matched_strings.append("text_string")

            elif rule["name"] == "PotentialRansomware":
                if contains_pattern(content, "your files have been encrypted"):
                    rule_matched = True
                    matched_strings.append("ransom_note")

                if contains_pattern(content, "bitcoin") and contains_pattern(content, "payment"):
                    rule_matched = True
                    matched_strings.append("bitcoin")
                    matched_strings.append("payment")

                if contains_pattern(content, "encrypt") and contains_pattern(content, "decrypt"):
                    rule_matched = True
                    matched_strings.append("encrypt")
                    matched_strings.append("decrypt")

            elif rule["name"] == "Ransomware_Generic":
                if (contains_pattern(content, "ransom") or
                    (contains_pattern(content, "bitcoin") and contains_pattern(content, "payment"))):
                    rule_matched = True
                    matched_strings.append("ransom_indicators")

            # If the rule matched, add it to the results
            if rule_matched:
                matches.append({
                    "rule": rule["name"],
                    "meta": rule.get("meta", {}),
                    "strings": matched_strings,
                    "tags": rule.get("meta", {}).get("category", "").split(",")
                })

        return matches

    except Exception as e:
        logger.error(f"Error scanning file {file_path}: {e}")
        return []

def save_json_result(file_path, matches, output_dir="output"):
    """Save scan results to a JSON file"""
    try:
        os.makedirs(output_dir, exist_ok=True)

        base_name = os.path.basename(file_path)
        output_file = os.path.join(output_dir, f"{base_name}_scan.json")

        result = {
            "file": file_path,
            "scan_time": datetime.now().isoformat(),
            "matches": matches
        }

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)

        return output_file

    except Exception as e:
        logger.error(f"Error saving JSON result: {e}")
        return None

def main():
    import argparse

    # Parse command line arguments with better Unicode support
    parser = argparse.ArgumentParser(description="Enhanced Mock YARA Scanner with Unicode support")
    parser.add_argument("rules_file", help="Path to the YARA rules file")

    # Create a mutually exclusive group for target file specification
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--target-file", help="Path to the file to scan")
    group.add_argument("--path-file", help="Path to a file containing the target file path (for Unicode support)")

    # Add output file option
    parser.add_argument("--output-file", help="Path to write JSON results (avoids console encoding issues)")

    # Handle the case where the script is called with the old syntax
    if len(sys.argv) >= 3 and not (sys.argv[2].startswith("--")):
        # Convert old syntax to new syntax
        sys.argv.insert(2, "--target-file")

    args = parser.parse_args()

    rules_file = args.rules_file
    output_file = args.output_file

    # Configure Unicode support
    if sys.platform == 'win32':
        # Force UTF-8 mode on Windows
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

    # Get target file path, either directly or from a file
    if args.path_file:
        try:
            with open(args.path_file, 'r', encoding='utf-8') as f:
                target_file = f.read().strip()
        except Exception as e:
            error_msg = f"Error reading path file: {e}"
            if output_file:
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump({"error": error_msg, "matches": []}, f, ensure_ascii=False)
            else:
                print(error_msg)
            return 1
    else:
        target_file = args.target_file

    # Log to file instead of console if output file is specified
    if not output_file:
        print(f"Enhanced Mock YARA Scanner (Unicode-enabled)")
        print(f"Rules file: {rules_file}")
        print(f"Target file: {target_file}")

    # Ensure the target file exists
    if not os.path.exists(target_file):
        error_msg = f"Error: Target file does not exist: {target_file}"
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump({"error": error_msg, "matches": []}, f, ensure_ascii=False)
        else:
            print(error_msg)
        return 1

    # Read rules
    rules = read_rules_file(rules_file)
    if not rules:
        error_msg = f"No rules found in {rules_file}"
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump({"error": error_msg, "matches": []}, f, ensure_ascii=False)
        else:
            print(error_msg)
        return 1

    if not output_file:
        print(f"Loaded {len(rules)} rules")

    # Scan the file
    matches = scan_file(target_file, rules)

    # Handle results
    if output_file:
        # Write results directly to the specified output file
        with open(output_file, 'w', encoding='utf-8') as f:
            result = {
                "file": target_file,
                "scan_time": datetime.now().isoformat(),
                "matches": matches,
                "rule_count": len(rules)
            }
            json.dump(result, f, indent=2, ensure_ascii=False)
    else:
        # Print results to console
        if matches:
            print(f"Found {len(matches)} matches:")
            for match in matches:
                print(f"Matched rule: {match['rule']} (found {', '.join(match['strings'])})")

            # Save results to JSON
            output_path = save_json_result(target_file, matches)
            if output_path:
                print(f"Results saved to {output_path}")
        else:
            print("No matches found")

    return 0

if __name__ == "__main__":
    sys.exit(main())
