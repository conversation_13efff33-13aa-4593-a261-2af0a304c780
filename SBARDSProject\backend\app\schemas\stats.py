"""
Pydantic schemas for statistics.

This module provides Pydantic schemas for statistics.
"""

from typing import List

from pydantic import BaseModel, Field


class ThreatType(BaseModel):
    """Schema for threat type statistics."""
    
    type: str = Field(..., description="Type of threat")
    count: int = Field(..., description="Number of occurrences")


class StatsResponse(BaseModel):
    """Schema for statistics responses."""
    
    total_scans: int = Field(..., description="Total number of scans performed")
    total_files_scanned: int = Field(..., description="Total number of files scanned")
    total_threats: int = Field(..., description="Total number of threats found")
    recent_scans: int = Field(..., description="Number of scans in the last 24 hours")
    recent_threats: int = Field(..., description="Number of threats found in the last 24 hours")
    top_threat_types: List[ThreatType] = Field([], description="Top threat types")
