{"project": {"name": "SBARDS", "version": "1.0.0", "description": "Security Behavior Analysis and Response Decision System"}, "paths": {"output_dir": "output", "log_dir": "logs", "temp_dir": "temp"}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file": {"enabled": true, "filename": "sbards.log"}, "json": {"enabled": false, "filename": "sbards.json.log"}}, "prescanning": {"enabled": true, "max_file_size_mb": 100, "excluded_extensions": [".exe", ".dll", ".sys", ".bin", ".dat", ".db", ".sqlite", ".sqlite3", ".mdf", ".ldf", ".bak", ".iso", ".img", ".vhd", ".vhdx", ".vmdk", ".mp3", ".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm", ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".ico", ".svg", ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".zip", ".rar", ".7z", ".tar", ".gz", ".bz2", ".xz"], "excluded_directories": ["node_modules", "venv", ".venv", ".git", ".svn", ".hg", "__pycache__", ".pytest_cache", ".vscode", ".idea", "dist", "build", "bin", "obj", "target", "out", "output", "logs", "log", "temp", "tmp"], "yara": {"rules_dir": "rules", "timeout_seconds": 60, "max_string_matches": 100}, "hash_algorithms": ["md5", "sha1", "sha256"], "scan_threads": 4, "report_format": "html", "report_dir": "reports"}, "monitoring": {"enabled": true, "monitor_whole_device": true, "interval_seconds": 2, "detailed_api_data": true, "continuous_monitoring": true, "use_mock_monitors": true, "enable_osquery": true, "enable_sysmon": true, "enable_etw": true, "file_change_detection": {"enabled": true, "excluded_directories": ["node_modules", "venv", ".venv", ".git", ".svn", ".hg", "__pycache__", ".pytest_cache", ".vscode", ".idea", "dist", "build", "bin", "obj", "target", "out", "output", "logs", "log", "temp", "tmp"], "excluded_extensions": [".tmp", ".temp", ".log", ".bak", ".swp", ".swo"]}, "process_monitoring": {"enabled": true, "suspicious_process_patterns": ["encrypt", "ransom", "crypt", "wncry", "wcry", "lockfiles", "cryptolocker", "bitlocker", "filecoder", "hidden_tear", "locky", "cerber", "petya"]}, "network_monitoring": {"enabled": true, "suspicious_domains": ["pastebin.com", "github.io", "example.com", "test-malicious-domain.com", "ransomware-command-server.net", "iplogger.org", "ngrok.io", "bitly.com", "tinyurl.com"]}, "alert": {"log_alerts": true, "alert_level": "info", "max_alerts_per_minute": 20, "deduplicate_alerts": true}}, "backend": {"host": "0.0.0.0", "port": 8000, "log_level": "info", "log_file": "logs/backend.log", "database": {"type": "sqlite", "path": "data/sbards.db"}, "api_keys": {"virustotal": "", "api": ["GkHli-qRc2IN6lyia0f8NTEVp-54r8I5Hngh03zmupo"]}, "cors": {"allowed_origins": ["http://localhost:3000", "http://127.0.0.1:3000"], "allowed_methods": ["GET", "POST", "PUT", "DELETE"], "allowed_headers": ["Content-Type", "Authorization"]}}}