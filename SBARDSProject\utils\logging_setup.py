"""
Logging Setup for SBARDS

This module provides centralized logging configuration for the SBARDS project.
It sets up logging with consistent formatting and supports multiple output
destinations (console, file, JSON).
"""

import os
import sys
import json
import logging
import traceback
from datetime import datetime
from typing import Dict, Any, Optional, List, Union

# Custom JSON formatter
class JsonFormatter(logging.Formatter):
    """
    JSON formatter for logging.
    
    This formatter outputs log records as JSON objects, which can be easily
    parsed by log analysis tools.
    """
    
    def __init__(self, include_stack_info: bool = True):
        """
        Initialize the JSON formatter.
        
        Args:
            include_stack_info (bool): Whether to include stack information in the log
        """
        super().__init__()
        self.include_stack_info = include_stack_info
        
    def format(self, record: logging.LogRecord) -> str:
        """
        Format the log record as a JSON string.
        
        Args:
            record (logging.LogRecord): Log record to format
            
        Returns:
            str: JSON-formatted log record
        """
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # Include exception info if available
        if record.exc_info:
            log_data["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": traceback.format_exception(*record.exc_info)
            }
            
        # Include stack info if requested and available
        if self.include_stack_info and hasattr(record, "stack_info") and record.stack_info:
            log_data["stack_info"] = record.stack_info
            
        return json.dumps(log_data)

# Custom context-aware logger
class ContextLogger(logging.Logger):
    """
    Context-aware logger.
    
    This logger extends the standard Logger class to include context information
    in log records, such as request IDs, user IDs, or other contextual data.
    """
    
    def __init__(self, name: str, level: int = logging.NOTSET):
        """
        Initialize the context logger.
        
        Args:
            name (str): Logger name
            level (int): Logger level
        """
        super().__init__(name, level)
        self.context = {}
        
    def set_context(self, **kwargs) -> None:
        """
        Set context information for the logger.
        
        Args:
            **kwargs: Context key-value pairs
        """
        self.context.update(kwargs)
        
    def clear_context(self) -> None:
        """Clear context information."""
        self.context.clear()
        
    def _log(self, level, msg, args, exc_info=None, extra=None, stack_info=False, stacklevel=1):
        """
        Log a message with the specified level and context.
        
        This method overrides the standard _log method to include context information.
        """
        if extra is None:
            extra = {}
            
        # Add context to extra
        for key, value in self.context.items():
            if key not in extra:
                extra[key] = value
                
        super()._log(level, msg, args, exc_info, extra, stack_info, stacklevel)

# Register the custom logger class
logging.setLoggerClass(ContextLogger)

def setup_logging(config: Dict[str, Any]) -> None:
    """
    Set up logging based on configuration.
    
    Args:
        config (Dict[str, Any]): Configuration dictionary
    """
    # Get logging configuration
    logging_config = config.get("logging", {})
    log_level_name = logging_config.get("level", "INFO")
    log_level = getattr(logging, log_level_name, logging.INFO)
    
    # Create log directory if it doesn't exist
    log_dir = config.get("paths", {}).get("log_dir", "logs")
    os.makedirs(log_dir, exist_ok=True)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # Remove existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    
    # Create formatter
    log_format = logging_config.get("format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    formatter = logging.Formatter(log_format)
    console_handler.setFormatter(formatter)
    
    # Add console handler to root logger
    root_logger.addHandler(console_handler)
    
    # Create file handler if enabled
    file_config = logging_config.get("file", {})
    if file_config.get("enabled", True):
        log_file = os.path.join(log_dir, file_config.get("filename", "sbards.log"))
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    # Create JSON file handler if enabled
    json_config = logging_config.get("json", {})
    if json_config.get("enabled", False):
        json_log_file = os.path.join(log_dir, json_config.get("filename", "sbards.json.log"))
        json_handler = logging.FileHandler(json_log_file)
        json_handler.setLevel(log_level)
        json_handler.setFormatter(JsonFormatter())
        root_logger.addHandler(json_handler)
    
    # Log configuration complete
    logging.info(f"Logging configured with level {log_level_name}")

def get_logger(name: str) -> ContextLogger:
    """
    Get a context-aware logger.
    
    Args:
        name (str): Logger name
        
    Returns:
        ContextLogger: Context-aware logger
    """
    return logging.getLogger(name)

# Exception handler with logging
def handle_exception(exc_type, exc_value, exc_traceback):
    """
    Global exception handler that logs unhandled exceptions.
    
    Args:
        exc_type: Exception type
        exc_value: Exception value
        exc_traceback: Exception traceback
    """
    if issubclass(exc_type, KeyboardInterrupt):
        # Don't log keyboard interrupt
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
        
    logger = logging.getLogger("SBARDS.ExceptionHandler")
    logger.error(
        "Unhandled exception",
        exc_info=(exc_type, exc_value, exc_traceback)
    )

# Set the global exception handler
sys.excepthook = handle_exception
