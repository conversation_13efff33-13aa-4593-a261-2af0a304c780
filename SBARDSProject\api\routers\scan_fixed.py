"""
Scan Router for SBARDS API

This module provides the scan router for the SBARDS API.
"""

import os
import logging
import json
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, WebSocket, WebSocketDisconnect
from pydantic import BaseModel

# Create router
router = APIRouter()

# Models
class ScanOptions(BaseModel):
    """Scan options model."""
    target_directory: str
    recursive: bool = True
    max_depth: int = 5
    exclude_dirs: List[str] = []
    exclude_extensions: List[str] = []
    max_file_size_mb: int = 100
    enable_yara: bool = True
    enable_hash_check: bool = True
    enable_av_integration: bool = True
    enable_fast_track: bool = True
    threads: int = 4
    batch_size: int = 20

class ScanProgress(BaseModel):
    """Scan progress model."""
    scan_id: str
    status: str
    total_files: int
    processed_files: int
    matched_files: int
    current_file: Optional[str] = None
    percent_complete: float
    elapsed_time: float
    estimated_time_remaining: Optional[float] = None
    current_phase: str

class ScanResult(BaseModel):
    """Scan result model."""
    scan_id: str
    target_directory: str
    start_time: float
    end_time: float
    elapsed_time: float
    total_files: int
    scanned_files: int
    matched_files: int
    skipped_files: int
    error_files: int
    matches: List[Dict[str, Any]] = []
    summary: Dict[str, Any] = {}

# WebSocket connections for scan progress updates
scan_connections: Dict[str, List[WebSocket]] = {}

# Endpoints
@router.post("/start", response_model=ScanProgress)
async def start_scan(
    scan_options: ScanOptions,
    background_tasks: BackgroundTasks
):
    """Start a new scan."""
    try:
        # Validate target directory
        if not os.path.exists(scan_options.target_directory):
            raise HTTPException(status_code=400, detail=f"Target directory not found: {scan_options.target_directory}")
        
        # Import orchestrator here to avoid circular imports
        from phases.prescanning.orchestrator import Orchestrator
        
        # Create orchestrator with config path
        orchestrator = Orchestrator("config.json")
        
        # Prepare scan
        scan_id = orchestrator.prepare_scan(
            target_directory=scan_options.target_directory,
            recursive=scan_options.recursive,
            max_depth=scan_options.max_depth,
            exclude_dirs=scan_options.exclude_dirs,
            exclude_extensions=scan_options.exclude_extensions,
            max_file_size_mb=scan_options.max_file_size_mb,
            enable_yara=scan_options.enable_yara,
            enable_hash_check=scan_options.enable_hash_check,
            enable_av_integration=scan_options.enable_av_integration,
            enable_fast_track=scan_options.enable_fast_track,
            threads=scan_options.threads,
            batch_size=scan_options.batch_size
        )
        
        # Start scan in background
        background_tasks.add_task(
            orchestrator.run_scan_async,
            scan_id=scan_id
        )
        
        # Return initial progress
        return ScanProgress(
            scan_id=scan_id,
            status="running",
            total_files=0,
            processed_files=0,
            matched_files=0,
            current_file=None,
            percent_complete=0.0,
            elapsed_time=0.0,
            estimated_time_remaining=None,
            current_phase="initializing"
        )
    except Exception as e:
        logging.error(f"Error starting scan: {e}")
        raise HTTPException(status_code=500, detail=f"Error starting scan: {str(e)}")

@router.get("/progress/{scan_id}", response_model=ScanProgress)
async def get_scan_progress(scan_id: str):
    """Get scan progress."""
    try:
        # Import orchestrator here to avoid circular imports
        from phases.prescanning.orchestrator import Orchestrator
        
        # Create orchestrator with config path
        orchestrator = Orchestrator("config.json")
        
        # Get scan progress
        progress = orchestrator.get_scan_progress(scan_id)
        if progress is None:
            raise HTTPException(status_code=404, detail=f"Scan not found: {scan_id}")
        
        return progress
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error getting scan progress: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting scan progress: {str(e)}")

@router.get("/result/{scan_id}", response_model=ScanResult)
async def get_scan_result(scan_id: str):
    """Get scan result."""
    try:
        # Import orchestrator here to avoid circular imports
        from phases.prescanning.orchestrator import Orchestrator
        
        # Create orchestrator with config path
        orchestrator = Orchestrator("config.json")
        
        # Get scan result
        result = orchestrator.get_scan_result(scan_id)
        if result is None:
            raise HTTPException(status_code=404, detail=f"Scan result not found: {scan_id}")
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error getting scan result: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting scan result: {str(e)}")

@router.post("/cancel/{scan_id}")
async def cancel_scan(scan_id: str):
    """Cancel a scan."""
    try:
        # Import orchestrator here to avoid circular imports
        from phases.prescanning.orchestrator import Orchestrator
        
        # Create orchestrator with config path
        orchestrator = Orchestrator("config.json")
        
        # Cancel scan
        success = orchestrator.cancel_scan(scan_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"Scan not found or already completed: {scan_id}")
        
        return {"status": "cancelled", "scan_id": scan_id}
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error cancelling scan: {e}")
        raise HTTPException(status_code=500, detail=f"Error cancelling scan: {str(e)}")

@router.websocket("/ws/{scan_id}")
async def websocket_scan_progress(websocket: WebSocket, scan_id: str):
    """WebSocket endpoint for scan progress updates."""
    await websocket.accept()
    
    # Add connection to scan connections
    if scan_id not in scan_connections:
        scan_connections[scan_id] = []
    scan_connections[scan_id].append(websocket)
    
    try:
        # Keep connection open
        while True:
            # Wait for client message (ping)
            await websocket.receive_text()
            
            # Get scan progress
            try:
                from phases.prescanning.orchestrator import Orchestrator
                orchestrator = Orchestrator("config.json")
                progress = orchestrator.get_scan_progress(scan_id)
                
                if progress:
                    # Send progress update
                    await websocket.send_text(json.dumps(progress.dict()))
            except Exception as e:
                logging.error(f"Error getting scan progress for WebSocket: {e}")
                await websocket.send_text(json.dumps({"error": str(e)}))
    except WebSocketDisconnect:
        # Remove connection from scan connections
        if scan_id in scan_connections and websocket in scan_connections[scan_id]:
            scan_connections[scan_id].remove(websocket)
            if not scan_connections[scan_id]:
                del scan_connections[scan_id]
