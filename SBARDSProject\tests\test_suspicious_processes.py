"""
Test script to simulate suspicious process behavior.

This script performs actions that mimic suspicious process behavior:
1. Creates processes with suspicious names
2. Simulates process injection techniques
3. Creates unusual process hierarchies

Run this to test the monitoring layer's process detection capabilities.
"""

import os
import sys
import time
import random
import string
import subprocess
import platform

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# List of suspicious process names
SUSPICIOUS_PROCESS_NAMES = [
    "encrypt_files",
    "ransomware_test",
    "cryptolocker_sim",
    "wcry_test",
    "lockfiles"
]

def create_suspicious_process(name):
    """Create a process with a suspicious name."""
    print(f"Creating suspicious process: {name}")
    
    # Create a Python script with the suspicious name
    script_content = """
import time
import sys
print(f"Suspicious process {sys.argv[0]} started")
print(f"This process would normally perform malicious actions")
print(f"Sleeping for demonstration purposes...")
time.sleep(30)  # Sleep to keep the process alive for monitoring
print(f"Suspicious process {sys.argv[0]} finished")
"""
    
    # Determine file extension based on platform
    if platform.system() == "Windows":
        script_ext = ".py"
        python_cmd = "python"
    else:
        script_ext = ".py"
        python_cmd = "python3"
    
    # Create the script file
    script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), f"{name}{script_ext}")
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    # Run the script as a separate process
    try:
        process = subprocess.Popen([python_cmd, script_path], 
                                  stdout=subprocess.PIPE,
                                  stderr=subprocess.PIPE,
                                  text=True)
        print(f"Process started with PID: {process.pid}")
        return process
    except Exception as e:
        print(f"Failed to create process: {e}")
        return None

def simulate_process_tree(depth=3):
    """Simulate a suspicious process tree with multiple levels."""
    print(f"Simulating suspicious process tree with depth {depth}")
    
    # Create a Python script that spawns child processes
    script_content = """
import time
import sys
import subprocess
import platform

depth = int(sys.argv[1])
current_level = int(sys.argv[2])
parent_id = sys.argv[3]

print(f"Process at level {current_level} (parent: {parent_id}) started")

# Spawn child if not at max depth
if current_level < depth:
    # Determine Python command based on platform
    python_cmd = "python" if platform.system() == "Windows" else "python3"
    
    # Spawn a child process
    child = subprocess.Popen(
        [python_cmd, sys.argv[0], sys.argv[1], str(current_level + 1), str(os.getpid())],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    print(f"Spawned child process with PID: {child.pid}")

# Sleep to keep the process alive
time.sleep(20)
print(f"Process at level {current_level} finished")
"""
    
    # Determine Python command based on platform
    if platform.system() == "Windows":
        python_cmd = "python"
    else:
        python_cmd = "python3"
    
    # Create the script file
    script_name = "process_tree_simulator.py"
    script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), script_name)
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    # Run the script as a separate process
    try:
        process = subprocess.Popen([python_cmd, script_path, str(depth), "1", "0"], 
                                  stdout=subprocess.PIPE,
                                  stderr=subprocess.PIPE,
                                  text=True)
        print(f"Root process started with PID: {process.pid}")
        return process
    except Exception as e:
        print(f"Failed to create process tree: {e}")
        return None

def main():
    """Main function to run the suspicious process simulation."""
    print("Starting suspicious process behavior simulation")
    print("This will trigger alerts in the monitoring system")
    print("=" * 60)
    
    processes = []
    
    # Create processes with suspicious names
    for name in SUSPICIOUS_PROCESS_NAMES:
        process = create_suspicious_process(name)
        if process:
            processes.append(process)
        time.sleep(2)  # Delay between process creation
    
    # Simulate process tree
    tree_process = simulate_process_tree()
    if tree_process:
        processes.append(tree_process)
    
    print("=" * 60)
    print("Suspicious processes created. Check the monitoring alerts.")
    print("Waiting for processes to complete...")
    
    # Wait for processes to complete
    for process in processes:
        try:
            process.wait(timeout=60)  # Wait up to 60 seconds
        except subprocess.TimeoutExpired:
            print(f"Process {process.pid} is still running, terminating...")
            process.terminate()
    
    print("Suspicious process simulation complete.")

if __name__ == "__main__":
    main()
