"""
External Integration Layer for SBARDS

This module provides external integration capabilities including:
- VirusTotal API integration
- Threat intelligence feeds
- External sandbox integration
- Blockchain hash storage
- Air-gapped backups
"""

import os
import json
import time
import logging
import hashlib
import requests
from typing import Dict, List, Any, Optional
from datetime import datetime


class ExternalIntegrationLayer:
    """
    External Integration Layer for SBARDS.

    Provides integration with external services and systems for enhanced
    threat detection and analysis capabilities.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the External Integration Layer.

        Args:
            config (Dict[str, Any]): Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger("SBARDS.ExternalIntegration")

        # Integration settings
        self.integration_config = config.get("external_integration", {})
        self.virustotal_config = self.integration_config.get("virustotal", {})
        self.threat_intel_config = self.integration_config.get("threat_intelligence", {})
        self.blockchain_config = self.integration_config.get("blockchain", {})

        # API keys and endpoints
        self.virustotal_api_key = self.virustotal_config.get("api_key", "")
        self.virustotal_enabled = self.virustotal_config.get("enabled", False) and bool(self.virustotal_api_key)

        # Cache directory
        self.cache_dir = self.integration_config.get("cache_directory", "threat_intel_cache")
        os.makedirs(self.cache_dir, exist_ok=True)

        # Session for HTTP requests
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": "SBARDS/1.0"
        })

        self.logger.info("External Integration Layer initialized")

    def analyze_file_hash(self, file_hash: str, file_path: str = None) -> Dict[str, Any]:
        """
        Analyze file hash using external services.

        Args:
            file_hash (str): File hash to analyze
            file_path (str, optional): Original file path

        Returns:
            Dict[str, Any]: Analysis results
        """
        results = {
            "file_hash": file_hash,
            "file_path": file_path,
            "timestamp": datetime.now().isoformat(),
            "virustotal": {},
            "threat_intelligence": {},
            "reputation": "unknown",
            "risk_score": 0
        }

        try:
            # VirusTotal analysis
            if self.virustotal_enabled:
                vt_result = self._query_virustotal(file_hash)
                results["virustotal"] = vt_result

                # Update reputation based on VirusTotal results
                if vt_result.get("positives", 0) > 0:
                    results["reputation"] = "malicious"
                    results["risk_score"] = min(100, vt_result.get("positives", 0) * 10)
                elif vt_result.get("total", 0) > 0:
                    results["reputation"] = "clean"

            # Threat intelligence lookup
            threat_intel = self._query_threat_intelligence(file_hash)
            results["threat_intelligence"] = threat_intel

            # Update risk score based on threat intelligence
            if threat_intel.get("is_malicious", False):
                results["reputation"] = "malicious"
                results["risk_score"] = max(results["risk_score"], 80)

            self.logger.info(f"External analysis completed for hash: {file_hash[:16]}...")

        except Exception as e:
            self.logger.error(f"Error during external analysis: {e}")
            results["error"] = str(e)

        return results

    def _query_virustotal(self, file_hash: str) -> Dict[str, Any]:
        """
        Query VirusTotal API for file hash.

        Args:
            file_hash (str): File hash to query

        Returns:
            Dict[str, Any]: VirusTotal results
        """
        if not self.virustotal_enabled:
            return {"error": "VirusTotal not enabled"}

        try:
            # Check cache first
            cache_file = os.path.join(self.cache_dir, "virustotal", f"{file_hash}.json")
            if os.path.exists(cache_file):
                cache_age = time.time() - os.path.getmtime(cache_file)
                if cache_age < 86400:  # 24 hours
                    with open(cache_file, 'r') as f:
                        cached_result = json.load(f)
                        self.logger.debug(f"Using cached VirusTotal result for {file_hash[:16]}...")
                        return cached_result

            # Query VirusTotal API
            url = f"https://www.virustotal.com/vtapi/v2/file/report"
            params = {
                "apikey": self.virustotal_api_key,
                "resource": file_hash
            }

            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()

            result = response.json()

            # Cache the result
            os.makedirs(os.path.dirname(cache_file), exist_ok=True)
            with open(cache_file, 'w') as f:
                json.dump(result, f, indent=2)

            self.logger.debug(f"VirusTotal query completed for {file_hash[:16]}...")
            return result

        except Exception as e:
            self.logger.error(f"VirusTotal query failed: {e}")
            return {"error": str(e)}

    def _query_threat_intelligence(self, file_hash: str) -> Dict[str, Any]:
        """
        Query threat intelligence feeds for file hash.

        Args:
            file_hash (str): File hash to query

        Returns:
            Dict[str, Any]: Threat intelligence results
        """
        try:
            # Check local threat intelligence database
            threat_db_file = os.path.join(self.cache_dir, "threat_intelligence.json")

            if os.path.exists(threat_db_file):
                with open(threat_db_file, 'r') as f:
                    threat_db = json.load(f)

                if file_hash in threat_db:
                    self.logger.debug(f"Found threat intelligence for {file_hash[:16]}...")
                    return threat_db[file_hash]

            # If not found, return empty result
            return {
                "is_malicious": False,
                "threat_type": None,
                "confidence": 0,
                "sources": []
            }

        except Exception as e:
            self.logger.error(f"Threat intelligence query failed: {e}")
            return {"error": str(e)}

    def store_blockchain_hash(self, file_hash: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        Store file hash on blockchain for integrity verification.

        Args:
            file_hash (str): File hash to store
            metadata (Dict[str, Any]): Additional metadata

        Returns:
            Dict[str, Any]: Storage results
        """
        try:
            # Simulate blockchain storage (placeholder implementation)
            blockchain_record = {
                "file_hash": file_hash,
                "timestamp": datetime.now().isoformat(),
                "metadata": metadata,
                "block_id": f"block_{int(time.time())}",
                "transaction_id": f"tx_{hashlib.sha256(file_hash.encode()).hexdigest()[:16]}"
            }

            # Store in local blockchain cache
            blockchain_file = os.path.join(self.cache_dir, "blockchain_records.json")

            if os.path.exists(blockchain_file):
                with open(blockchain_file, 'r') as f:
                    records = json.load(f)
            else:
                records = []

            records.append(blockchain_record)

            with open(blockchain_file, 'w') as f:
                json.dump(records, f, indent=2)

            self.logger.info(f"Blockchain record stored for hash: {file_hash[:16]}...")
            return {"success": True, "record": blockchain_record}

        except Exception as e:
            self.logger.error(f"Blockchain storage failed: {e}")
            return {"success": False, "error": str(e)}

    def create_air_gapped_backup(self, file_path: str, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create air-gapped backup of analysis results.

        Args:
            file_path (str): Original file path
            analysis_results (Dict[str, Any]): Analysis results to backup

        Returns:
            Dict[str, Any]: Backup results
        """
        try:
            # Create backup directory
            backup_dir = self.integration_config.get("backup_directory", "air_gapped_backups")
            os.makedirs(backup_dir, exist_ok=True)

            # Create backup record
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_record = {
                "original_file": file_path,
                "backup_timestamp": timestamp,
                "analysis_results": analysis_results,
                "backup_id": f"backup_{timestamp}_{hashlib.md5(file_path.encode()).hexdigest()[:8]}"
            }

            # Save backup record
            backup_file = os.path.join(backup_dir, f"backup_{timestamp}.json")
            with open(backup_file, 'w') as f:
                json.dump(backup_record, f, indent=2)

            self.logger.info(f"Air-gapped backup created: {backup_file}")
            return {"success": True, "backup_file": backup_file, "backup_id": backup_record["backup_id"]}

        except Exception as e:
            self.logger.error(f"Air-gapped backup failed: {e}")
            return {"success": False, "error": str(e)}

    def store_hash_in_blockchain(self, file_info: Dict[str, Any]) -> Optional[str]:
        """
        Store file hash in blockchain for integrity verification.

        Args:
            file_info (Dict[str, Any]): File information including hash

        Returns:
            Optional[str]: Blockchain transaction ID if successful
        """
        try:
            file_hash = file_info.get("file_hash", "")
            if not file_hash:
                self.logger.error("No file hash provided for blockchain storage")
                return None

            result = self.store_blockchain_hash(file_hash, file_info)
            if result.get("success"):
                return result.get("record", {}).get("transaction_id")
            return None

        except Exception as e:
            self.logger.error(f"Error storing hash in blockchain: {e}")
            return None

    def update_threat_databases(self, workflow_results: Dict[str, Any]) -> bool:
        """
        Update threat intelligence databases with malicious file information.

        Args:
            workflow_results (Dict[str, Any]): Workflow results containing threat info

        Returns:
            bool: True if successful
        """
        try:
            # Extract threat information
            capture_info = workflow_results.get("phases", {}).get("capture", {})
            file_info = capture_info.get("file_info", {})
            file_hash = file_info.get("file_hash", "")

            if not file_hash:
                self.logger.warning("No file hash found for threat database update")
                return False

            # Load existing threat database
            threat_db_file = os.path.join(self.cache_dir, "threat_intelligence.json")

            if os.path.exists(threat_db_file):
                with open(threat_db_file, 'r') as f:
                    threat_db = json.load(f)
            else:
                threat_db = {}

            # Add new threat entry
            threat_entry = {
                "is_malicious": True,
                "threat_type": "detected_malware",
                "confidence": 95,
                "sources": ["sbards_analysis"],
                "detection_date": datetime.now().isoformat(),
                "workflow_id": workflow_results.get("workflow_id"),
                "file_info": file_info
            }

            threat_db[file_hash] = threat_entry

            # Save updated database
            os.makedirs(os.path.dirname(threat_db_file), exist_ok=True)
            with open(threat_db_file, 'w') as f:
                json.dump(threat_db, f, indent=2)

            self.logger.info(f"Updated threat database with hash: {file_hash[:16]}...")
            return True

        except Exception as e:
            self.logger.error(f"Error updating threat databases: {e}")
            return False

    def update_good_databases(self, workflow_results: Dict[str, Any]) -> bool:
        """
        Update known good file databases with clean file information.

        Args:
            workflow_results (Dict[str, Any]): Workflow results containing file info

        Returns:
            bool: True if successful
        """
        try:
            # Extract file information
            capture_info = workflow_results.get("phases", {}).get("capture", {})
            file_info = capture_info.get("file_info", {})
            file_hash = file_info.get("file_hash", "")

            if not file_hash:
                self.logger.warning("No file hash found for good database update")
                return False

            # Load existing good files database
            good_db_file = os.path.join(self.cache_dir, "known_good_files.json")

            if os.path.exists(good_db_file):
                with open(good_db_file, 'r') as f:
                    good_db = json.load(f)
            else:
                good_db = {}

            # Add new good file entry
            good_entry = {
                "is_clean": True,
                "confidence": 95,
                "sources": ["sbards_analysis"],
                "verification_date": datetime.now().isoformat(),
                "workflow_id": workflow_results.get("workflow_id"),
                "file_info": file_info
            }

            good_db[file_hash] = good_entry

            # Save updated database
            os.makedirs(os.path.dirname(good_db_file), exist_ok=True)
            with open(good_db_file, 'w') as f:
                json.dump(good_db, f, indent=2)

            self.logger.info(f"Updated good files database with hash: {file_hash[:16]}...")
            return True

        except Exception as e:
            self.logger.error(f"Error updating good databases: {e}")
            return False

    def get_integration_status(self) -> Dict[str, Any]:
        """
        Get status of external integrations.

        Returns:
            Dict[str, Any]: Integration status
        """
        return {
            "virustotal": {
                "enabled": self.virustotal_enabled,
                "api_key_configured": bool(self.virustotal_api_key)
            },
            "threat_intelligence": {
                "enabled": True,
                "cache_directory": self.cache_dir
            },
            "blockchain": {
                "enabled": self.blockchain_config.get("enabled", False)
            },
            "air_gapped_backup": {
                "enabled": True,
                "backup_directory": self.integration_config.get("backup_directory", "air_gapped_backups")
            }
        }