"""
Priority Manager for SBARDS

This module provides priority management for files and processes.
"""

import os
import time
import logging
import threading
from typing import Dict, List, Any, Optional, Set, Tuple
from datetime import datetime

class PriorityManager:
    """
    Manages priorities for files and processes across layers.
    
    This class provides mechanisms for:
    1. Assigning priority levels to files and processes
    2. Adjusting priorities based on threat intelligence
    3. Expediting processing of high-priority items
    4. Coordinating resource allocation based on priorities
    """
    
    # Priority levels
    PRIORITY_CRITICAL = 100
    PRIORITY_HIGH = 75
    PRIORITY_MEDIUM = 50
    PRIORITY_LOW = 25
    PRIORITY_NORMAL = 0
    
    def __init__(self, config: Dict[str, Any], shared_state=None):
        """
        Initialize the priority manager.
        
        Args:
            config (Dict[str, Any]): Configuration dictionary
            shared_state: Optional shared state instance
        """
        self.config = config
        self.logger = logging.getLogger("SBARDS.PriorityManager")
        
        # Initialize shared state if not provided
        if shared_state is None:
            from .shared_state import SharedState
            self.shared_state = SharedState()
        else:
            self.shared_state = shared_state
            
        # Priority configuration
        self.priority_config = config.get("integration", {}).get("priority", {})
        self.enabled = self.priority_config.get("enabled", True)
        
        # Priority rules
        self.file_rules = self.priority_config.get("file_rules", [])
        self.process_rules = self.priority_config.get("process_rules", [])
        
        # Priority thresholds
        self.critical_threshold = self.priority_config.get("critical_threshold", self.PRIORITY_CRITICAL)
        self.high_threshold = self.priority_config.get("high_threshold", self.PRIORITY_HIGH)
        self.medium_threshold = self.priority_config.get("medium_threshold", self.PRIORITY_MEDIUM)
        self.low_threshold = self.priority_config.get("low_threshold", self.PRIORITY_LOW)
        
        # Priority cache
        self._file_priority_cache = {}  # file_path -> (priority, timestamp)
        self._process_priority_cache = {}  # process_id -> (priority, timestamp)
        
        # Cache expiration (seconds)
        self.cache_expiration = self.priority_config.get("cache_expiration_seconds", 300)
        
        # Thread safety
        self._lock = threading.RLock()
        
        self.logger.info("Priority Manager initialized")
        
    def get_file_priority(self, file_path: str, file_info: Optional[Dict[str, Any]] = None) -> int:
        """
        Get priority level for a file.
        
        Args:
            file_path (str): Path to the file
            file_info (Optional[Dict[str, Any]]): Additional file information
            
        Returns:
            int: Priority level
        """
        with self._lock:
            # Check cache first
            if file_path in self._file_priority_cache:
                priority, timestamp = self._file_priority_cache[file_path]
                
                # Check if cache is still valid
                if time.time() - timestamp < self.cache_expiration:
                    return priority
                    
            # Check shared state
            priority = self.shared_state.get_file_priority(file_path)
            if priority > 0:
                # Update cache
                self._file_priority_cache[file_path] = (priority, time.time())
                return priority
                
            # Calculate priority based on rules
            priority = self._calculate_file_priority(file_path, file_info)
            
            # Update shared state and cache
            self.shared_state.set_file_priority(file_path, priority)
            self._file_priority_cache[file_path] = (priority, time.time())
            
            return priority
            
    def get_process_priority(self, process_id: str, process_info: Optional[Dict[str, Any]] = None) -> int:
        """
        Get priority level for a process.
        
        Args:
            process_id (str): Process ID
            process_info (Optional[Dict[str, Any]]): Additional process information
            
        Returns:
            int: Priority level
        """
        with self._lock:
            # Check cache first
            if process_id in self._process_priority_cache:
                priority, timestamp = self._process_priority_cache[process_id]
                
                # Check if cache is still valid
                if time.time() - timestamp < self.cache_expiration:
                    return priority
                    
            # Check shared state
            priority = self.shared_state.get_process_priority(process_id)
            if priority > 0:
                # Update cache
                self._process_priority_cache[process_id] = (priority, time.time())
                return priority
                
            # Calculate priority based on rules
            priority = self._calculate_process_priority(process_id, process_info)
            
            # Update shared state and cache
            self.shared_state.set_process_priority(process_id, priority)
            self._process_priority_cache[process_id] = (priority, time.time())
            
            return priority
            
    def set_file_priority(self, file_path: str, priority: int):
        """
        Set priority level for a file.
        
        Args:
            file_path (str): Path to the file
            priority (int): Priority level
        """
        with self._lock:
            # Update shared state
            self.shared_state.set_file_priority(file_path, priority)
            
            # Update cache
            self._file_priority_cache[file_path] = (priority, time.time())
            
            self.logger.debug(f"Set priority for file {file_path}: {priority}")
            
    def set_process_priority(self, process_id: str, priority: int):
        """
        Set priority level for a process.
        
        Args:
            process_id (str): Process ID
            priority (int): Priority level
        """
        with self._lock:
            # Update shared state
            self.shared_state.set_process_priority(process_id, priority)
            
            # Update cache
            self._process_priority_cache[process_id] = (priority, time.time())
            
            self.logger.debug(f"Set priority for process {process_id}: {priority}")
            
    def _calculate_file_priority(self, file_path: str, file_info: Optional[Dict[str, Any]] = None) -> int:
        """
        Calculate priority level for a file based on rules.
        
        Args:
            file_path (str): Path to the file
            file_info (Optional[Dict[str, Any]]): Additional file information
            
        Returns:
            int: Priority level
        """
        if not self.enabled or not file_path:
            return self.PRIORITY_NORMAL
            
        priority = self.PRIORITY_NORMAL
        
        # Apply file extension rules
        file_ext = os.path.splitext(file_path)[1].lower()
        
        # Critical file types (executables, scripts)
        if file_ext in ['.exe', '.dll', '.sys', '.bat', '.ps1', '.vbs', '.js']:
            priority = max(priority, self.PRIORITY_HIGH)
            
        # Important document types
        if file_ext in ['.doc', '.docx', '.pdf', '.xls', '.xlsx', '.ppt', '.pptx']:
            priority = max(priority, self.PRIORITY_MEDIUM)
            
        # Apply path-based rules
        if any(sensitive_dir in file_path.lower() for sensitive_dir in ['\\windows\\', '\\program files\\', '\\users\\', '\\documents\\']):
            priority += self.PRIORITY_LOW
            
        # Apply custom rules from configuration
        for rule in self.file_rules:
            rule_type = rule.get("type")
            rule_pattern = rule.get("pattern")
            rule_priority = rule.get("priority", self.PRIORITY_NORMAL)
            
            if not rule_type or not rule_pattern:
                continue
                
            if rule_type == "extension" and file_ext == rule_pattern:
                priority = max(priority, rule_priority)
            elif rule_type == "path" and rule_pattern in file_path:
                priority = max(priority, rule_priority)
            elif rule_type == "name" and rule_pattern in os.path.basename(file_path):
                priority = max(priority, rule_priority)
                
        # Apply additional file info if provided
        if file_info:
            # If file has been flagged by pre-inspection
            if file_info.get("flagged_by_pre_inspection", False):
                priority = max(priority, self.PRIORITY_HIGH)
                
            # If file has suspicious entropy
            if file_info.get("entropy", 0) > 7.5:
                priority = max(priority, self.PRIORITY_MEDIUM)
                
            # If file has been recently modified
            if file_info.get("recently_modified", False):
                priority += self.PRIORITY_LOW
                
        return priority
        
    def _calculate_process_priority(self, process_id: str, process_info: Optional[Dict[str, Any]] = None) -> int:
        """
        Calculate priority level for a process based on rules.
        
        Args:
            process_id (str): Process ID
            process_info (Optional[Dict[str, Any]]): Additional process information
            
        Returns:
            int: Priority level
        """
        if not self.enabled or not process_id:
            return self.PRIORITY_NORMAL
            
        priority = self.PRIORITY_NORMAL
        
        # Apply process info if provided
        if process_info:
            process_name = process_info.get("name", "").lower()
            
            # System processes
            if process_name in ["system", "svchost.exe", "lsass.exe", "winlogon.exe"]:
                priority = max(priority, self.PRIORITY_HIGH)
                
            # Known suspicious process names
            if any(susp in process_name for susp in ["encrypt", "ransom", "crypt"]):
                priority = max(priority, self.PRIORITY_CRITICAL)
                
            # Recently created processes
            if process_info.get("recently_created", False):
                priority += self.PRIORITY_LOW
                
            # Processes with network activity
            if process_info.get("has_network_activity", False):
                priority += self.PRIORITY_LOW
                
            # Processes accessing sensitive files
            if process_info.get("accessing_sensitive_files", False):
                priority += self.PRIORITY_MEDIUM
                
        # Apply custom rules from configuration
        for rule in self.process_rules:
            rule_type = rule.get("type")
            rule_pattern = rule.get("pattern")
            rule_priority = rule.get("priority", self.PRIORITY_NORMAL)
            
            if not rule_type or not rule_pattern:
                continue
                
            if process_info:
                if rule_type == "name" and rule_pattern in process_info.get("name", ""):
                    priority = max(priority, rule_priority)
                elif rule_type == "path" and rule_pattern in process_info.get("path", ""):
                    priority = max(priority, rule_priority)
                    
        return priority
        
    def get_priority_label(self, priority: int) -> str:
        """
        Get a human-readable label for a priority level.
        
        Args:
            priority (int): Priority level
            
        Returns:
            str: Priority label
        """
        if priority >= self.critical_threshold:
            return "CRITICAL"
        elif priority >= self.high_threshold:
            return "HIGH"
        elif priority >= self.medium_threshold:
            return "MEDIUM"
        elif priority >= self.low_threshold:
            return "LOW"
        else:
            return "NORMAL"
            
    def clear_cache(self):
        """Clear the priority cache."""
        with self._lock:
            self._file_priority_cache.clear()
            self._process_priority_cache.clear()
            self.logger.debug("Priority cache cleared")
