"""
WebSocket endpoints for real-time scan updates.

This module provides WebSocket endpoints for real-time scan updates.
"""

import asyncio
import json
from typing import Dict, List, Any, Optional

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, Query, HTTPException, status
from sqlalchemy.orm import Session

from ....core.logging import logger
from ....core.security import verify_websocket_token
from ....core.websocket import websocket_manager
from ....db.session import get_db
from ....services import scanner, reports

# Create router
router = APIRouter()


@router.websocket("/scan/{scan_id}")
async def websocket_scan_endpoint(
    websocket: WebSocket,
    scan_id: str,
    token: Optional[str] = None
):
    """
    WebSocket endpoint for real-time scan updates.

    Connect to this endpoint to receive real-time updates about a scan.

    - **scan_id**: The unique identifier of the scan
    - **token**: Optional authentication token
    """
    # Verify token if provided
    if token:
        try:
            await verify_websocket_token(token)
        except Exception as e:
            await websocket.close(code=1008, reason="Invalid token")
            return

    # Accept connection
    try:
        await websocket_manager.connect(websocket, scan_id)

        # Send initial message
        await websocket.send_text(
            json.dumps({
                "event": "connected",
                "scan_id": scan_id,
                "message": "Connected to scan updates"
            })
        )

        # Check if there's an existing scan status
        try:
            import os
            status_file = os.path.join("uploads", scan_id, "status.json")
            if os.path.exists(status_file):
                with open(status_file, "r") as f:
                    status_data = json.load(f)
                    await websocket.send_text(json.dumps({
                        "event": "status",
                        "scan_id": scan_id,
                        **status_data
                    }))
        except Exception as e:
            logger.error(f"Error sending initial status: {e}")

        # Keep connection open and handle messages
        try:
            while True:
                # Wait for messages from client
                data = await websocket.receive_text()

                # Process message (client can send commands like "get_status")
                try:
                    message = json.loads(data)
                    if message.get("command") == "get_status":
                        # Check for status file
                        status_file = os.path.join("uploads", scan_id, "status.json")
                        if os.path.exists(status_file):
                            with open(status_file, "r") as f:
                                status_data = json.load(f)
                                await websocket.send_text(json.dumps({
                                    "event": "status",
                                    "scan_id": scan_id,
                                    **status_data
                                }))
                        else:
                            # No status file, check database
                            db = next(get_db())
                            report = reports.get_scan_report(db, scan_id, include_content=False)
                            if report:
                                await websocket.send_text(json.dumps({
                                    "event": "status",
                                    "scan_id": scan_id,
                                    "status": "completed",
                                    "files_scanned": report.files_scanned,
                                    "threats_found": report.threats_found,
                                    "report_id": report.id
                                }))
                            else:
                                await websocket.send_text(json.dumps({
                                    "event": "status",
                                    "scan_id": scan_id,
                                    "status": "unknown",
                                    "message": "No status information available"
                                }))
                except Exception as e:
                    logger.error(f"Error processing WebSocket message: {e}")
                    await websocket.send_text(
                        json.dumps({
                            "event": "error",
                            "message": f"Error processing message: {str(e)}"
                        })
                    )
        except WebSocketDisconnect:
            await websocket_manager.disconnect(websocket, scan_id)
    except Exception as e:
        logger.error(f"Error in WebSocket connection: {e}")
        try:
            await websocket.close(code=1011, reason="Server error")
        except:
            pass


# Function to send scan updates (called from scanner service)
async def send_scan_update(scan_id: str, data: Dict[str, Any]):
    """
    Send a scan update to all connected clients for a specific scan.

    Args:
        scan_id (str): Scan ID.
        data (Dict[str, Any]): Update data.
    """
    try:
        # Add event type and scan ID to data
        data["event"] = data.get("event", "update")
        data["scan_id"] = scan_id

        # Send to all connected clients for this scan
        await websocket_manager.send_json(data, scan_id)
    except Exception as e:
        logger.error(f"Error sending scan update: {e}")
