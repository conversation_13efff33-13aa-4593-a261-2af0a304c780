"""
Database models for the SBARDS Backend API.

This module provides database models for the SBARDS Backend API.
"""

import datetime
from typing import List, Optional

from sqlalchemy import <PERSON>olean, Column, DateTime, ForeignKey, Integer, String, Text
from sqlalchemy.orm import relationship

from .base import Base


class ScanReport(Base):
    """Scan report model."""

    __tablename__ = "scan_reports"

    id = Column(Integer, primary_key=True, index=True)
    scan_id = Column(String, unique=True, index=True)
    timestamp = Column(DateTime, default=datetime.datetime.now(datetime.timezone.utc))
    scan_path = Column(String)
    files_scanned = Column(Integer)
    threats_found = Column(Integer)
    report_path = Column(String)
    report_content = Column(Text)

    # Relationships
    file_results = relationship("FileResult", back_populates="scan_report", cascade="all, delete-orphan")

    def __repr__(self) -> str:
        """String representation of the model."""
        return f"<ScanReport(id={self.id}, scan_id={self.scan_id}, threats_found={self.threats_found})>"


class FileResult(Base):
    """File result model."""

    __tablename__ = "file_results"

    id = Column(Integer, primary_key=True, index=True)
    scan_report_id = Column(Integer, ForeignKey("scan_reports.id"))
    file_path = Column(String)
    file_hash = Column(String)
    is_threat = Column(Boolean, default=False)
    threat_type = Column(String, nullable=True)
    virustotal_result = Column(Text, nullable=True)

    # Relationships
    scan_report = relationship("ScanReport", back_populates="file_results")

    def __repr__(self) -> str:
        """String representation of the model."""
        return f"<FileResult(id={self.id}, file_path={self.file_path}, is_threat={self.is_threat})>"
