"""
Error handling utilities for the SBARDS Backend API.

This module provides error handling utilities for the SBARDS Backend API.
"""

from typing import Any, Dict, Optional, Union

from fastapi import HTTPException, status
from pydantic import BaseModel, Field

from .logging import logger


class ErrorResponse(BaseModel):
    """Error response model."""
    
    detail: str = Field(..., description="Error message")
    error_code: Optional[str] = Field(None, description="Error code")
    error_type: Optional[str] = Field(None, description="Error type")
    path: Optional[str] = Field(None, description="Path where the error occurred")
    context: Optional[Dict[str, Any]] = Field(None, description="Additional context")


class DatabaseError(Exception):
    """Database error."""
    
    def __init__(self, message: str, original_error: Optional[Exception] = None):
        """
        Initialize database error.
        
        Args:
            message (str): Error message.
            original_error (Optional[Exception]): Original error.
        """
        self.message = message
        self.original_error = original_error
        super().__init__(self.message)


class ScanError(Exception):
    """Scan error."""
    
    def __init__(self, message: str, scan_id: Optional[str] = None, original_error: Optional[Exception] = None):
        """
        Initialize scan error.
        
        Args:
            message (str): Error message.
            scan_id (Optional[str]): Scan ID.
            original_error (Optional[Exception]): Original error.
        """
        self.message = message
        self.scan_id = scan_id
        self.original_error = original_error
        super().__init__(self.message)


class NotFoundError(Exception):
    """Not found error."""
    
    def __init__(self, message: str, resource_type: Optional[str] = None, resource_id: Optional[str] = None):
        """
        Initialize not found error.
        
        Args:
            message (str): Error message.
            resource_type (Optional[str]): Resource type.
            resource_id (Optional[str]): Resource ID.
        """
        self.message = message
        self.resource_type = resource_type
        self.resource_id = resource_id
        super().__init__(self.message)


class ValidationError(Exception):
    """Validation error."""
    
    def __init__(self, message: str, field: Optional[str] = None, value: Optional[Any] = None):
        """
        Initialize validation error.
        
        Args:
            message (str): Error message.
            field (Optional[str]): Field name.
            value (Optional[Any]): Invalid value.
        """
        self.message = message
        self.field = field
        self.value = value
        super().__init__(self.message)


def handle_error(error: Exception) -> HTTPException:
    """
    Handle error and return appropriate HTTP exception.
    
    Args:
        error (Exception): Error to handle.
        
    Returns:
        HTTPException: HTTP exception.
    """
    # Log the error
    logger.error(f"Error: {error}")
    
    # Handle specific error types
    if isinstance(error, DatabaseError):
        logger.error(f"Database error: {error.message}")
        if error.original_error:
            logger.error(f"Original error: {error.original_error}")
        
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ErrorResponse(
                detail=error.message,
                error_type="database_error",
                context={"original_error": str(error.original_error) if error.original_error else None}
            ).dict()
        )
    
    if isinstance(error, ScanError):
        logger.error(f"Scan error: {error.message}")
        if error.original_error:
            logger.error(f"Original error: {error.original_error}")
        
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ErrorResponse(
                detail=error.message,
                error_type="scan_error",
                context={
                    "scan_id": error.scan_id,
                    "original_error": str(error.original_error) if error.original_error else None
                }
            ).dict()
        )
    
    if isinstance(error, NotFoundError):
        logger.error(f"Not found error: {error.message}")
        
        return HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=ErrorResponse(
                detail=error.message,
                error_type="not_found",
                context={
                    "resource_type": error.resource_type,
                    "resource_id": error.resource_id
                }
            ).dict()
        )
    
    if isinstance(error, ValidationError):
        logger.error(f"Validation error: {error.message}")
        
        return HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=ErrorResponse(
                detail=error.message,
                error_type="validation_error",
                context={
                    "field": error.field,
                    "value": str(error.value) if error.value is not None else None
                }
            ).dict()
        )
    
    # Handle generic errors
    return HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail=ErrorResponse(
            detail=str(error),
            error_type="internal_server_error"
        ).dict()
    )
