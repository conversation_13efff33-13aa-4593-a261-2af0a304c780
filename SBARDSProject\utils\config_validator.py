"""
Configuration Validator for SBARDS

This module provides validation functionality for the SBARDS configuration.
It ensures that the configuration file contains all required fields and
that the values are of the correct type.
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional, Tuple, Union

logger = logging.getLogger("SBARDS.ConfigValidator")

# Define configuration schema
CONFIG_SCHEMA = {
    "project": {
        "required": True,
        "type": dict,
        "fields": {
            "name": {"required": True, "type": str},
            "version": {"required": True, "type": str},
            "description": {"required": False, "type": str}
        }
    },
    "paths": {
        "required": True,
        "type": dict,
        "fields": {
            "output_dir": {"required": True, "type": str},
            "log_dir": {"required": True, "type": str},
            "temp_dir": {"required": False, "type": str}
        }
    },
    "logging": {
        "required": True,
        "type": dict,
        "fields": {
            "level": {"required": True, "type": str, "values": ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]},
            "format": {"required": False, "type": str},
            "file": {"required": False, "type": dict}
        }
    },
    "monitoring": {
        "required": True,
        "type": dict,
        "fields": {
            "enabled": {"required": True, "type": bool},
            "monitor_whole_device": {"required": False, "type": bool},
            "interval_seconds": {"required": False, "type": (int, float)},
            "detailed_api_data": {"required": False, "type": bool},
            "continuous_monitoring": {"required": False, "type": bool},
            "use_mock_monitors": {"required": False, "type": bool},
            "enable_osquery": {"required": False, "type": bool},
            "enable_sysmon": {"required": False, "type": bool},
            "enable_etw": {"required": False, "type": bool}
        }
    },
    "prescanning": {
        "required": True,
        "type": dict,
        "fields": {
            "enabled": {"required": True, "type": bool},
            "max_file_size_mb": {"required": False, "type": (int, float)},
            "excluded_extensions": {"required": False, "type": list},
            "excluded_directories": {"required": False, "type": list},
            "yara": {"required": False, "type": dict}
        }
    }
}

def validate_config(config: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    Validate the configuration against the schema.
    
    Args:
        config (Dict[str, Any]): Configuration dictionary
        
    Returns:
        Tuple[bool, List[str]]: (is_valid, error_messages)
    """
    errors = []
    
    # Validate top-level sections
    for section_name, section_schema in CONFIG_SCHEMA.items():
        if section_schema["required"] and section_name not in config:
            errors.append(f"Missing required section: {section_name}")
            continue
            
        if section_name in config:
            section_value = config[section_name]
            
            # Check section type
            if not isinstance(section_value, section_schema["type"]):
                errors.append(f"Section {section_name} should be of type {section_schema['type'].__name__}")
                continue
                
            # Validate section fields
            if "fields" in section_schema:
                for field_name, field_schema in section_schema["fields"].items():
                    if field_schema["required"] and field_name not in section_value:
                        errors.append(f"Missing required field: {section_name}.{field_name}")
                        continue
                        
                    if field_name in section_value:
                        field_value = section_value[field_name]
                        
                        # Check field type
                        if not isinstance(field_value, field_schema["type"]):
                            type_name = field_schema["type"].__name__ if isinstance(field_schema["type"], type) else \
                                        ", ".join(t.__name__ for t in field_schema["type"])
                            errors.append(f"Field {section_name}.{field_name} should be of type {type_name}")
                            continue
                            
                        # Check field values if specified
                        if "values" in field_schema and field_value not in field_schema["values"]:
                            values_str = ", ".join(str(v) for v in field_schema["values"])
                            errors.append(f"Field {section_name}.{field_name} should be one of: {values_str}")
    
    return len(errors) == 0, errors

def load_and_validate_config(config_path: str = "config.json") -> Dict[str, Any]:
    """
    Load and validate the configuration file.
    
    Args:
        config_path (str): Path to the configuration file
        
    Returns:
        Dict[str, Any]: Validated configuration dictionary
        
    Raises:
        ValueError: If the configuration is invalid
        FileNotFoundError: If the configuration file does not exist
    """
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
    try:
        with open(config_path, "r") as f:
            config = json.load(f)
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON in configuration file: {e}")
        
    is_valid, errors = validate_config(config)
    if not is_valid:
        error_message = "Configuration validation failed:\n" + "\n".join(f"- {error}" for error in errors)
        logger.error(error_message)
        raise ValueError(error_message)
        
    # Create required directories
    if "paths" in config:
        for dir_key in ["output_dir", "log_dir", "temp_dir"]:
            if dir_key in config["paths"]:
                os.makedirs(config["paths"][dir_key], exist_ok=True)
                
    return config

def get_config_value(config: Dict[str, Any], path: str, default: Any = None) -> Any:
    """
    Get a value from the configuration using a dot-separated path.
    
    Args:
        config (Dict[str, Any]): Configuration dictionary
        path (str): Dot-separated path to the value (e.g., "monitoring.interval_seconds")
        default (Any): Default value to return if the path does not exist
        
    Returns:
        Any: The value at the specified path, or the default value if not found
    """
    parts = path.split(".")
    current = config
    
    for part in parts:
        if isinstance(current, dict) and part in current:
            current = current[part]
        else:
            return default
            
    return current

def set_config_value(config: Dict[str, Any], path: str, value: Any) -> None:
    """
    Set a value in the configuration using a dot-separated path.
    
    Args:
        config (Dict[str, Any]): Configuration dictionary
        path (str): Dot-separated path to the value (e.g., "monitoring.interval_seconds")
        value (Any): Value to set
    """
    parts = path.split(".")
    current = config
    
    for i, part in enumerate(parts[:-1]):
        if part not in current:
            current[part] = {}
        current = current[part]
        
    current[parts[-1]] = value

def save_config(config: Dict[str, Any], config_path: str = "config.json") -> None:
    """
    Save the configuration to a file.
    
    Args:
        config (Dict[str, Any]): Configuration dictionary
        config_path (str): Path to the configuration file
    """
    with open(config_path, "w") as f:
        json.dump(config, f, indent=4)
