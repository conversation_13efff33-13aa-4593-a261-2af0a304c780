"""
Mock Sysmon Monitor for SBARDS

This module provides a mock Sysmon monitor for the SBARDS project.
"""

import os
import time
import random
import logging
import threading
from typing import Dict, List, Any, Optional, Set
from ..mock_monitor import MockMonitor

class MockSysmonMonitor(MockMonitor):
    """
    Mock Sysmon Monitor.
    
    This class provides a mock implementation of the Sysmon monitor.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the mock Sysmon monitor.
        
        Args:
            config (Dict[str, Any]): Configuration dictionary
        """
        super().__init__(config, "MockSysmonMonitor")
        
        # Event types
        self.event_types = [
            "ProcessCreate",
            "FileCreateTime",
            "NetworkConnect",
            "ProcessTerminate",
            "FileCreate",
            "RegistryEvent"
        ]
        
        # Event counters
        self.event_counters = {event_type: 0 for event_type in self.event_types}
        
        # Last events
        self.last_events = []
        self.max_events = 100
        
    def _generate_mock_events(self) -> None:
        """Generate mock Sysmon events."""
        # Generate random number of events (1-5)
        num_events = random.randint(1, 5)
        
        for _ in range(num_events):
            # Select random event type
            event_type = random.choice(self.event_types)
            
            # Generate event
            event = self._generate_event(event_type)
            
            # Process event
            self._process_event(event)
            
            # Store event
            self.last_events.append(event)
            if len(self.last_events) > self.max_events:
                self.last_events.pop(0)
                
            # Update counter
            self.event_counters[event_type] += 1
            
    def _generate_event(self, event_type: str) -> Dict[str, Any]:
        """
        Generate mock event.
        
        Args:
            event_type (str): Event type
            
        Returns:
            Dict[str, Any]: Mock event
        """
        event = {
            "event_id": self._get_event_id(event_type),
            "event_type": event_type,
            "time": time.time(),
            "data": {}
        }
        
        if event_type == "ProcessCreate":
            process = random.choice(self.mock_data["processes"])
            event["data"] = {
                "Image": process["path"],
                "CommandLine": process["path"],
                "ProcessId": process["pid"],
                "ParentProcessId": random.randint(1, 1000),
                "User": "SYSTEM"
            }
        elif event_type == "FileCreateTime":
            file_info = random.choice(self.mock_data["files"])
            event["data"] = {
                "Image": random.choice(self.mock_data["processes"])["path"],
                "TargetFilename": os.path.join(file_info["path"], file_info["filename"]),
                "CreationUtcTime": time.time(),
                "PreviousCreationUtcTime": time.time() - 3600
            }
        elif event_type == "NetworkConnect":
            network = random.choice(self.mock_data["network"])
            event["data"] = {
                "Image": random.choice(self.mock_data["processes"])["path"],
                "User": "SYSTEM",
                "Protocol": network["protocol"],
                "SourceIp": network["source_ip"],
                "SourcePort": network["source_port"],
                "DestinationIp": network["dest_ip"],
                "DestinationPort": network["dest_port"],
                "DestinationHostname": f"host-{network['dest_ip']}"
            }
        elif event_type == "ProcessTerminate":
            process = random.choice(self.mock_data["processes"])
            event["data"] = {
                "Image": process["path"],
                "ProcessId": process["pid"],
                "User": "SYSTEM"
            }
        elif event_type == "FileCreate":
            file_info = random.choice(self.mock_data["files"])
            event["data"] = {
                "Image": random.choice(self.mock_data["processes"])["path"],
                "TargetFilename": os.path.join(file_info["path"], file_info["filename"]),
                "CreationUtcTime": time.time()
            }
        elif event_type == "RegistryEvent":
            registry = random.choice(self.mock_data["registry"])
            event["data"] = {
                "Image": random.choice(self.mock_data["processes"])["path"],
                "TargetObject": registry["key"],
                "Details": registry["data"],
                "User": "SYSTEM"
            }
            
        return event
        
    def _get_event_id(self, event_type: str) -> int:
        """
        Get event ID for event type.
        
        Args:
            event_type (str): Event type
            
        Returns:
            int: Event ID
        """
        event_ids = {
            "ProcessCreate": 1,
            "FileCreateTime": 2,
            "NetworkConnect": 3,
            "ProcessTerminate": 5,
            "FileCreate": 11,
            "RegistryEvent": 12
        }
        
        return event_ids.get(event_type, 0)
        
    def _process_event(self, event: Dict[str, Any]) -> None:
        """
        Process event.
        
        Args:
            event (Dict[str, Any]): Event to process
        """
        event_type = event["event_type"]
        
        # Process different event types
        if event_type == "ProcessCreate":
            self._handle_process_create(event)
        elif event_type == "FileCreateTime":
            self._handle_file_creation_time(event)
        elif event_type == "NetworkConnect":
            self._handle_network_connection(event)
        elif event_type == "ProcessTerminate":
            self._handle_process_termination(event)
        elif event_type == "FileCreate":
            self._handle_file_creation(event)
        elif event_type == "RegistryEvent":
            self._handle_registry_event(event)
            
    def _handle_process_create(self, event: Dict[str, Any]) -> None:
        """
        Handle process creation event.
        
        Args:
            event (Dict[str, Any]): Event to handle
        """
        if self.alert_manager and random.random() < 0.1:  # 10% chance of alert
            self.alert_manager.add_alert(
                source="MockSysmonMonitor",
                alert_type="process_create",
                message=f"Process created: {event['data'].get('Image', '')}",
                severity="info",
                details=event["data"]
            )
            
    def _handle_file_creation_time(self, event: Dict[str, Any]) -> None:
        """
        Handle file creation time change event.
        
        Args:
            event (Dict[str, Any]): Event to handle
        """
        if self.alert_manager and random.random() < 0.1:  # 10% chance of alert
            self.alert_manager.add_alert(
                source="MockSysmonMonitor",
                alert_type="file_time_change",
                message=f"File time changed: {event['data'].get('TargetFilename', '')}",
                severity="info",
                details=event["data"]
            )
            
    def _handle_network_connection(self, event: Dict[str, Any]) -> None:
        """
        Handle network connection event.
        
        Args:
            event (Dict[str, Any]): Event to handle
        """
        if self.alert_manager and random.random() < 0.1:  # 10% chance of alert
            self.alert_manager.add_alert(
                source="MockSysmonMonitor",
                alert_type="network_connection",
                message=f"Network connection: {event['data'].get('DestinationIp', '')}:{event['data'].get('DestinationPort', '')}",
                severity="info",
                details=event["data"]
            )
            
    def _handle_process_termination(self, event: Dict[str, Any]) -> None:
        """
        Handle process termination event.
        
        Args:
            event (Dict[str, Any]): Event to handle
        """
        pass  # No alerts for process termination
        
    def _handle_file_creation(self, event: Dict[str, Any]) -> None:
        """
        Handle file creation event.
        
        Args:
            event (Dict[str, Any]): Event to handle
        """
        # Update file change history
        file_path = event["data"].get("TargetFilename", "")
        if file_path:
            file_key = file_path.lower()
            self.file_change_history[file_key] = {
                "size": random.randint(1024, 10240),
                "mtime": time.time(),
                "first_seen": time.time(),
                "last_change": time.time()
            }
            
        if self.alert_manager and random.random() < 0.1:  # 10% chance of alert
            self.alert_manager.add_alert(
                source="MockSysmonMonitor",
                alert_type="file_create",
                message=f"File created: {file_path}",
                severity="info",
                details=event["data"]
            )
            
    def _handle_registry_event(self, event: Dict[str, Any]) -> None:
        """
        Handle registry event.
        
        Args:
            event (Dict[str, Any]): Event to handle
        """
        if self.alert_manager and random.random() < 0.1:  # 10% chance of alert
            self.alert_manager.add_alert(
                source="MockSysmonMonitor",
                alert_type="registry_event",
                message=f"Registry modified: {event['data'].get('TargetObject', '')}",
                severity="info",
                details=event["data"]
            )
            
    def get_detailed_status(self) -> Dict[str, Any]:
        """
        Get detailed status.
        
        Returns:
            Dict[str, Any]: Detailed status
        """
        status = super().get_detailed_status()
        status.update({
            "event_counters": self.event_counters,
            "last_events": len(self.last_events),
            "file_changes": len(self.file_change_history)
        })
        
        return status
