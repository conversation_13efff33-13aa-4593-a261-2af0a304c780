<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SBARDS Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .card {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 15px;
            margin-bottom: 20px;
        }
        .card h2 {
            margin-top: 0;
            color: #444;
        }
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .status.running {
            background-color: #d4edda;
            color: #155724;
        }
        .status.stopped {
            background-color: #f8d7da;
            color: #721c24;
        }
        .btn {
            display: inline-block;
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
        }
        .btn:hover {
            background-color: #0069d9;
        }
        .btn.danger {
            background-color: #dc3545;
        }
        .btn.danger:hover {
            background-color: #c82333;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>SBARDS Dashboard</h1>
        
        <div class="card">
            <h2>Monitoring Status</h2>
            <p>Current Status: <span class="status running" id="monitoring-status">Running</span></p>
            <button class="btn" id="start-monitoring">Start Monitoring</button>
            <button class="btn danger" id="stop-monitoring">Stop Monitoring</button>
        </div>
        
        <div class="card">
            <h2>Recent Alerts</h2>
            <table id="alerts-table">
                <thead>
                    <tr>
                        <th>Time</th>
                        <th>Source</th>
                        <th>Type</th>
                        <th>Message</th>
                        <th>Severity</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="5">Loading alerts...</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="card">
            <h2>File Changes</h2>
            <table id="file-changes-table">
                <thead>
                    <tr>
                        <th>File Path</th>
                        <th>Size</th>
                        <th>Last Modified</th>
                        <th>First Seen</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="4">Loading file changes...</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // Function to format date
        function formatDate(timestamp) {
            const date = new Date(timestamp * 1000);
            return date.toLocaleString();
        }

        // Function to update monitoring status
        async function updateMonitoringStatus() {
            try {
                const response = await fetch('/api/monitoring/status');
                const data = await response.json();
                
                const statusElement = document.getElementById('monitoring-status');
                if (data.is_running) {
                    statusElement.textContent = 'Running';
                    statusElement.className = 'status running';
                } else {
                    statusElement.textContent = 'Stopped';
                    statusElement.className = 'status stopped';
                }
            } catch (error) {
                console.error('Error fetching monitoring status:', error);
            }
        }

        // Function to load alerts
        async function loadAlerts() {
            try {
                const response = await fetch('/api/monitoring/alerts?limit=10');
                const alerts = await response.json();
                
                const tableBody = document.querySelector('#alerts-table tbody');
                
                if (alerts.length === 0) {
                    tableBody.innerHTML = '<tr><td colspan="5">No alerts found</td></tr>';
                    return;
                }
                
                tableBody.innerHTML = '';
                
                alerts.forEach(alert => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${new Date(alert.time).toLocaleString()}</td>
                        <td>${alert.source}</td>
                        <td>${alert.type}</td>
                        <td>${alert.message}</td>
                        <td>${alert.severity}</td>
                    `;
                    tableBody.appendChild(row);
                });
            } catch (error) {
                console.error('Error fetching alerts:', error);
                const tableBody = document.querySelector('#alerts-table tbody');
                tableBody.innerHTML = '<tr><td colspan="5">Error loading alerts</td></tr>';
            }
        }

        // Function to load file changes
        async function loadFileChanges() {
            try {
                const response = await fetch('/api/monitoring/file-changes?limit=10');
                const fileChanges = await response.json();
                
                const tableBody = document.querySelector('#file-changes-table tbody');
                
                if (fileChanges.length === 0) {
                    tableBody.innerHTML = '<tr><td colspan="4">No file changes found</td></tr>';
                    return;
                }
                
                tableBody.innerHTML = '';
                
                fileChanges.forEach(change => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${change.file_path}</td>
                        <td>${change.size} bytes</td>
                        <td>${formatDate(change.last_change)}</td>
                        <td>${formatDate(change.first_seen)}</td>
                    `;
                    tableBody.appendChild(row);
                });
            } catch (error) {
                console.error('Error fetching file changes:', error);
                const tableBody = document.querySelector('#file-changes-table tbody');
                tableBody.innerHTML = '<tr><td colspan="4">Error loading file changes</td></tr>';
            }
        }

        // Event listeners for buttons
        document.getElementById('start-monitoring').addEventListener('click', async () => {
            try {
                await fetch('/api/monitoring/start', { method: 'POST' });
                updateMonitoringStatus();
            } catch (error) {
                console.error('Error starting monitoring:', error);
            }
        });

        document.getElementById('stop-monitoring').addEventListener('click', async () => {
            try {
                await fetch('/api/monitoring/stop', { method: 'POST' });
                updateMonitoringStatus();
            } catch (error) {
                console.error('Error stopping monitoring:', error);
            }
        });

        // Initial load
        updateMonitoringStatus();
        loadAlerts();
        loadFileChanges();

        // Refresh data every 5 seconds
        setInterval(() => {
            updateMonitoringStatus();
            loadAlerts();
            loadFileChanges();
        }, 5000);
    </script>
</body>
</html>
