"""
Security Utilities for SBARDS

This module provides security-related utilities for the SBARDS project,
including input validation, sanitization, and secure file operations.
"""

import os
import re
import json
import hashlib
import logging
from typing import Any, Dict, List, Optional, Union, Tuple, Pattern

logger = logging.getLogger("SBARDS.Security")

# Regular expressions for validation
PATH_REGEX = re.compile(r'^[a-zA-Z0-9_\-./\\:]+$')
FILENAME_REGEX = re.compile(r'^[a-zA-Z0-9_\-.]+$')
EMAIL_REGEX = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
IP_REGEX = re.compile(r'^(\d{1,3}\.){3}\d{1,3}$')
URL_REGEX = re.compile(r'^https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+')

# Dangerous file extensions
DANGEROUS_EXTENSIONS = {
    '.exe', '.dll', '.bat', '.cmd', '.ps1', '.vbs', '.js', '.jar', '.sh',
    '.py', '.rb', '.pl', '.php', '.asp', '.aspx', '.jsp', '.cgi'
}

def validate_path(path: str) -> bool:
    """
    Validate a file path.
    
    Args:
        path (str): File path to validate
        
    Returns:
        bool: True if the path is valid, False otherwise
    """
    if not path or not isinstance(path, str):
        return False
        
    # Check for path traversal attempts
    normalized_path = os.path.normpath(path)
    if '..' in normalized_path:
        logger.warning(f"Path traversal attempt detected: {path}")
        return False
        
    # Check for invalid characters
    if not PATH_REGEX.match(path):
        logger.warning(f"Invalid characters in path: {path}")
        return False
        
    return True

def validate_filename(filename: str) -> bool:
    """
    Validate a filename.
    
    Args:
        filename (str): Filename to validate
        
    Returns:
        bool: True if the filename is valid, False otherwise
    """
    if not filename or not isinstance(filename, str):
        return False
        
    # Check for invalid characters
    if not FILENAME_REGEX.match(filename):
        logger.warning(f"Invalid characters in filename: {filename}")
        return False
        
    return True

def is_dangerous_file(filename: str) -> bool:
    """
    Check if a file is potentially dangerous based on its extension.
    
    Args:
        filename (str): Filename to check
        
    Returns:
        bool: True if the file is potentially dangerous, False otherwise
    """
    if not filename or not isinstance(filename, str):
        return False
        
    extension = os.path.splitext(filename.lower())[1]
    return extension in DANGEROUS_EXTENSIONS

def sanitize_path(path: str) -> str:
    """
    Sanitize a file path.
    
    Args:
        path (str): File path to sanitize
        
    Returns:
        str: Sanitized file path
    """
    if not path or not isinstance(path, str):
        return ""
        
    # Normalize path
    normalized_path = os.path.normpath(path)
    
    # Remove any potentially dangerous components
    components = normalized_path.split(os.path.sep)
    sanitized_components = [c for c in components if c and c != '..']
    
    # Reconstruct path
    sanitized_path = os.path.join(*sanitized_components)
    
    return sanitized_path

def sanitize_filename(filename: str) -> str:
    """
    Sanitize a filename.
    
    Args:
        filename (str): Filename to sanitize
        
    Returns:
        str: Sanitized filename
    """
    if not filename or not isinstance(filename, str):
        return ""
        
    # Remove any potentially dangerous characters
    sanitized_filename = re.sub(r'[^\w\-.]', '_', filename)
    
    return sanitized_filename

def compute_file_hash(file_path: str, algorithm: str = 'sha256') -> Optional[str]:
    """
    Compute the hash of a file.
    
    Args:
        file_path (str): Path to the file
        algorithm (str): Hash algorithm to use
        
    Returns:
        Optional[str]: File hash, or None if the file does not exist
    """
    if not os.path.exists(file_path) or not os.path.isfile(file_path):
        logger.warning(f"File does not exist: {file_path}")
        return None
        
    try:
        hash_obj = hashlib.new(algorithm)
        
        with open(file_path, 'rb') as f:
            # Read in chunks to handle large files
            for chunk in iter(lambda: f.read(4096), b''):
                hash_obj.update(chunk)
                
        return hash_obj.hexdigest()
    except Exception as e:
        logger.error(f"Error computing file hash: {e}")
        return None

def secure_delete_file(file_path: str, passes: int = 3) -> bool:
    """
    Securely delete a file by overwriting it multiple times.
    
    Args:
        file_path (str): Path to the file
        passes (int): Number of overwrite passes
        
    Returns:
        bool: True if the file was deleted, False otherwise
    """
    if not os.path.exists(file_path) or not os.path.isfile(file_path):
        logger.warning(f"File does not exist: {file_path}")
        return False
        
    try:
        # Get file size
        file_size = os.path.getsize(file_path)
        
        # Overwrite file with random data
        for _ in range(passes):
            with open(file_path, 'wb') as f:
                f.write(os.urandom(file_size))
                f.flush()
                os.fsync(f.fileno())
                
        # Delete the file
        os.remove(file_path)
        return True
    except Exception as e:
        logger.error(f"Error securely deleting file: {e}")
        return False

def validate_json_input(json_str: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
    """
    Validate and parse JSON input.
    
    Args:
        json_str (str): JSON string to validate
        
    Returns:
        Tuple[bool, Optional[Dict[str, Any]]]: (is_valid, parsed_json)
    """
    if not json_str or not isinstance(json_str, str):
        return False, None
        
    try:
        parsed_json = json.loads(json_str)
        return True, parsed_json
    except json.JSONDecodeError:
        logger.warning(f"Invalid JSON input: {json_str}")
        return False, None

def validate_email(email: str) -> bool:
    """
    Validate an email address.
    
    Args:
        email (str): Email address to validate
        
    Returns:
        bool: True if the email is valid, False otherwise
    """
    if not email or not isinstance(email, str):
        return False
        
    return bool(EMAIL_REGEX.match(email))

def validate_ip_address(ip: str) -> bool:
    """
    Validate an IP address.
    
    Args:
        ip (str): IP address to validate
        
    Returns:
        bool: True if the IP address is valid, False otherwise
    """
    if not ip or not isinstance(ip, str):
        return False
        
    if not IP_REGEX.match(ip):
        return False
        
    # Check that each octet is in range
    octets = ip.split('.')
    return all(0 <= int(octet) <= 255 for octet in octets)

def validate_url(url: str) -> bool:
    """
    Validate a URL.
    
    Args:
        url (str): URL to validate
        
    Returns:
        bool: True if the URL is valid, False otherwise
    """
    if not url or not isinstance(url, str):
        return False
        
    return bool(URL_REGEX.match(url))
