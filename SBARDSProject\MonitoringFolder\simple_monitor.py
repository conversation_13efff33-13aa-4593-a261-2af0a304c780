"""
Simple Monitoring Script for SBARDS

This script runs the Monitoring phase of the SBARDS project with a focus on monitoring the F:\ drive.
"""

import os
import sys
import json
import time
import shutil
import psutil
import datetime

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

print("Loading configuration...")
# Load configuration
config_path = 'config.json'
if not os.path.exists(config_path):
    # Try SBARDSProject directory
    config_path = os.path.join('SBARDSProject', 'config.json')
    if not os.path.exists(config_path):
        print(f"Config file not found at {config_path}")
        sys.exit(1)

print(f"Using config file: {config_path}")
with open(config_path, 'r') as f:
    config = json.load(f)

# Disable external monitoring tools since they're not installed
config["monitoring"]["enable_osquery"] = False
config["monitoring"]["enable_sysmon"] = False
config["monitoring"]["enable_etw"] = False

# Add F:\ drive to the watch directories
if "F:\\" not in config["monitoring"]["filesystem_monitoring"]["watch_directories"]:
    config["monitoring"]["filesystem_monitoring"]["watch_directories"].append("F:\\")

print(f"Monitoring directories: {config['monitoring']['filesystem_monitoring']['watch_directories']}")

print("Configuration loaded successfully")

print("Importing Monitor Manager...")
# Import the monitor manager
from phases.monitoring.monitor_manager import MonitorManager
print("Monitor Manager imported successfully")

print("Creating Monitor Manager...")
# Create monitor manager
monitor_manager = MonitorManager(config)

print("Starting monitoring...")
# Start monitoring
monitor_manager.start_monitoring()
print("Monitoring started successfully")

# Check F:\ drive information
print("\nF:\ Drive Information:")
try:
    # Check if F:\ drive exists
    if os.path.exists("F:\\"):
        # Get disk usage
        disk_usage = shutil.disk_usage("F:\\")
        print(f"Total space: {disk_usage.total / (1024**3):.2f} GB")
        print(f"Used space: {disk_usage.used / (1024**3):.2f} GB")
        print(f"Free space: {disk_usage.free / (1024**3):.2f} GB")
        print(f"Usage percentage: {disk_usage.used / disk_usage.total * 100:.2f}%")

        # List top-level directories
        print("\nTop-level directories in F:\\:")
        try:
            for item in os.listdir("F:\\"):
                item_path = os.path.join("F:\\", item)
                if os.path.isdir(item_path):
                    print(f"  Directory: {item}")
                else:
                    print(f"  File: {item}")
        except PermissionError:
            print("  Permission denied to list directories")
        except Exception as e:
            print(f"  Error listing directories: {e}")
    else:
        print("F:\\ drive not found or not accessible")
except Exception as e:
    print(f"Error checking F:\\ drive: {e}")

print("\nPress Ctrl+C to stop monitoring")
try:
    # Keep running until interrupted
    counter = 0
    while True:
        time.sleep(1)
        counter += 1

        # Every 10 seconds, check for changes in F:\ drive
        if counter % 10 == 0:
            print(f"\n[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Checking F:\\ drive for changes...")
            try:
                if os.path.exists("F:\\"):
                    # Get current disk usage
                    disk_usage = shutil.disk_usage("F:\\")
                    print(f"  Free space: {disk_usage.free / (1024**3):.2f} GB")

                    # Check for any new files in the root directory
                    try:
                        files = os.listdir("F:\\")
                        print(f"  Number of items in root directory: {len(files)}")
                    except Exception as e:
                        print(f"  Error listing F:\\ directory: {e}")
                else:
                    print("  F:\\ drive not found or not accessible")
            except Exception as e:
                print(f"  Error checking F:\\ drive: {e}")
except KeyboardInterrupt:
    print("\nStopping monitoring")
finally:
    # Stop monitoring
    monitor_manager.stop_monitoring()
    print("Monitoring stopped")
