"""
Security utilities for the SBARDS API.

This module provides security-related functionality for the SBARDS API,
including API key authentication and rate limiting.
"""

import os
import time
import secrets
import logging
from fastapi import Depends, HTTPException, Security, status, Request
from fastapi.security.api_key import API<PERSON>eyHeader
from typing import Dict, List, Any, Optional, Set, Callable

# Configure logging
logger = logging.getLogger("SBARDS.API.Security")

# API key header
API_KEY_HEADER = APIKeyHeader(name="X-API-Key", auto_error=False)

# Rate limiting
class RateLimiter:
    """
    Rate limiter for API endpoints.
    
    This class implements a simple rate limiting mechanism to prevent
    abuse of the API.
    """
    
    def __init__(self, requests_per_minute: int = 60):
        """
        Initialize the rate limiter.
        
        Args:
            requests_per_minute (int): Maximum number of requests per minute
        """
        self.requests_per_minute = requests_per_minute
        self.request_history: Dict[str, List[float]] = {}
        
    def is_rate_limited(self, client_id: str) -> bool:
        """
        Check if a client is rate limited.
        
        Args:
            client_id (str): Client identifier (e.g., IP address or API key)
            
        Returns:
            bool: True if the client is rate limited, False otherwise
        """
        # Get current time
        current_time = time.time()
        
        # Initialize request history for new clients
        if client_id not in self.request_history:
            self.request_history[client_id] = []
            
        # Remove requests older than 1 minute
        self.request_history[client_id] = [
            t for t in self.request_history[client_id]
            if current_time - t < 60
        ]
        
        # Check if the client has exceeded the rate limit
        if len(self.request_history[client_id]) >= self.requests_per_minute:
            return True
            
        # Add current request to history
        self.request_history[client_id].append(current_time)
        return False

# Create rate limiter
rate_limiter = RateLimiter()

# API key validation
class APIKeyValidator:
    """
    API key validator.
    
    This class validates API keys against a list of valid keys.
    """
    
    def __init__(self):
        """Initialize the API key validator."""
        self.api_keys: Set[str] = set()
        
    def load_api_keys(self, keys: List[str]) -> None:
        """
        Load API keys.
        
        Args:
            keys (List[str]): List of valid API keys
        """
        self.api_keys = set(keys)
        
    def load_api_keys_from_config(self, config: Dict[str, Any]) -> None:
        """
        Load API keys from configuration.
        
        Args:
            config (Dict[str, Any]): Configuration dictionary
        """
        # Get API keys from configuration
        api_keys = config.get("backend", {}).get("api_keys", {}).get("api", [])
        
        # Load API keys
        self.load_api_keys(api_keys)
        
    def is_valid_key(self, api_key: str) -> bool:
        """
        Check if an API key is valid.
        
        Args:
            api_key (str): API key to validate
            
        Returns:
            bool: True if the API key is valid, False otherwise
        """
        return api_key in self.api_keys
        
    def generate_key(self) -> str:
        """
        Generate a new API key.
        
        Returns:
            str: Generated API key
        """
        return secrets.token_urlsafe(32)

# Create API key validator
api_key_validator = APIKeyValidator()

# API key dependency
async def get_api_key(
    api_key: str = Security(API_KEY_HEADER),
    request: Request = None
) -> str:
    """
    Get and validate API key.
    
    Args:
        api_key (str): API key from request header
        request (Request): FastAPI request object
        
    Returns:
        str: Validated API key
        
    Raises:
        HTTPException: If the API key is invalid or missing
    """
    # Check if API key is required
    if not api_key_validator.api_keys:
        # No API keys configured, authentication is disabled
        return None
        
    # Check if API key is provided
    if not api_key:
        logger.warning(f"Missing API key in request from {request.client.host if request and request.client else 'unknown'}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing API key"
        )
        
    # Validate API key
    if not api_key_validator.is_valid_key(api_key):
        logger.warning(f"Invalid API key in request from {request.client.host if request and request.client else 'unknown'}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key"
        )
        
    # Check rate limit
    client_id = f"{api_key}"
    if rate_limiter.is_rate_limited(client_id):
        logger.warning(f"Rate limit exceeded for API key {api_key[:8]}...")
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Rate limit exceeded"
        )
        
    return api_key

# Optional API key dependency
async def get_optional_api_key(
    api_key: str = Security(API_KEY_HEADER),
    request: Request = None
) -> Optional[str]:
    """
    Get and validate API key, but don't require it.
    
    Args:
        api_key (str): API key from request header
        request (Request): FastAPI request object
        
    Returns:
        Optional[str]: Validated API key, or None if not provided
    """
    try:
        return await get_api_key(api_key, request)
    except HTTPException:
        return None

# Create a dependency that requires API key for specific endpoints
def require_api_key(endpoint_func: Callable) -> Callable:
    """
    Decorator to require API key for specific endpoints.
    
    Args:
        endpoint_func (Callable): Endpoint function
        
    Returns:
        Callable: Decorated endpoint function
    """
    async def wrapper(*args, api_key: str = Depends(get_api_key), **kwargs):
        return await endpoint_func(*args, **kwargs)
    return wrapper
