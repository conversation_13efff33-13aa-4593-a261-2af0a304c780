"""
SBARDS Monitoring Dashboard

This module provides a simple web-based dashboard for visualizing monitoring data.
"""

import os
import sys
import json
import time
import logging
import threading
import datetime
from typing import Dict, List, Any
from flask import Flask, render_template, jsonify, request

# Add parent directory to path to import SBARDS modules
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from scanner_core.python.orchestrator import Orchestrator

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'sbards-monitoring-dashboard'

# Initialize orchestrator
orchestrator = None
monitoring_thread = None
stop_event = threading.Event()

# Dashboard data
dashboard_data = {
    "alerts": [],
    "processes": [],
    "connections": [],
    "file_operations": [],
    "correlated_events": [],
    "responses": [],
    "status": {}
}

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("SBARDS.Dashboard")

@app.route('/')
def index():
    """Render the main dashboard page."""
    return render_template('index.html')

@app.route('/api/status')
def get_status():
    """Get the current status of the monitoring system."""
    global orchestrator, dashboard_data
    
    if orchestrator and orchestrator.monitor_manager:
        dashboard_data["status"] = orchestrator.get_monitoring_status()
    
    return jsonify(dashboard_data["status"])

@app.route('/api/alerts')
def get_alerts():
    """Get recent alerts."""
    global orchestrator, dashboard_data
    
    if orchestrator and orchestrator.monitor_manager and orchestrator.monitor_manager.alert_manager:
        dashboard_data["alerts"] = orchestrator.monitor_manager.alert_manager.get_recent_alerts(20)
    
    return jsonify(dashboard_data["alerts"])

@app.route('/api/processes')
def get_processes():
    """Get current processes."""
    global orchestrator, dashboard_data
    
    if orchestrator and orchestrator.monitor_manager and orchestrator.monitor_manager.process_monitor:
        processes = list(orchestrator.monitor_manager.process_monitor.get_current_processes())
        dashboard_data["processes"] = [{"process": p} for p in processes]
    
    return jsonify(dashboard_data["processes"])

@app.route('/api/connections')
def get_connections():
    """Get current network connections."""
    global orchestrator, dashboard_data
    
    if orchestrator and orchestrator.monitor_manager and orchestrator.monitor_manager.network_monitor:
        connections = list(orchestrator.monitor_manager.network_monitor.get_current_connections())
        dashboard_data["connections"] = [{"connection": c} for c in connections]
    
    return jsonify(dashboard_data["connections"])

@app.route('/api/file_operations')
def get_file_operations():
    """Get recent file operations."""
    global orchestrator, dashboard_data
    
    if orchestrator and orchestrator.monitor_manager and orchestrator.monitor_manager.filesystem_monitor:
        dashboard_data["file_operations"] = orchestrator.monitor_manager.filesystem_monitor.get_recent_operations(20)
    
    return jsonify(dashboard_data["file_operations"])

@app.route('/api/correlated_events')
def get_correlated_events():
    """Get correlated events."""
    global orchestrator, dashboard_data
    
    if orchestrator and orchestrator.monitor_manager and orchestrator.monitor_manager.event_correlator:
        dashboard_data["correlated_events"] = orchestrator.monitor_manager.event_correlator.get_correlated_events(10)
    
    return jsonify(dashboard_data["correlated_events"])

@app.route('/api/responses')
def get_responses():
    """Get automatic responses."""
    global orchestrator, dashboard_data
    
    if orchestrator and orchestrator.monitor_manager and orchestrator.monitor_manager.response_manager:
        dashboard_data["responses"] = orchestrator.monitor_manager.response_manager.get_response_history(10)
    
    return jsonify(dashboard_data["responses"])

@app.route('/api/start_monitoring', methods=['POST'])
def start_monitoring():
    """Start the monitoring system."""
    global orchestrator, monitoring_thread, stop_event
    
    if not orchestrator:
        orchestrator = Orchestrator("config.json")
    
    if orchestrator.monitor_manager:
        if not orchestrator.get_monitoring_status().get("is_running", False):
            # Start monitoring in a separate thread
            stop_event.clear()
            monitoring_thread = threading.Thread(
                target=orchestrator.start_monitoring,
                daemon=True
            )
            monitoring_thread.start()
            return jsonify({"status": "started"})
        else:
            return jsonify({"status": "already_running"})
    else:
        return jsonify({"status": "error", "message": "Monitoring is not enabled in configuration"})

@app.route('/api/stop_monitoring', methods=['POST'])
def stop_monitoring():
    """Stop the monitoring system."""
    global orchestrator, stop_event
    
    if orchestrator and orchestrator.monitor_manager:
        if orchestrator.get_monitoring_status().get("is_running", False):
            orchestrator.stop_monitoring()
            stop_event.set()
            return jsonify({"status": "stopped"})
        else:
            return jsonify({"status": "not_running"})
    else:
        return jsonify({"status": "error", "message": "Monitoring is not enabled in configuration"})

def update_dashboard_data():
    """Update dashboard data periodically."""
    global orchestrator, dashboard_data, stop_event
    
    while not stop_event.is_set():
        try:
            if orchestrator and orchestrator.monitor_manager:
                # Update status
                dashboard_data["status"] = orchestrator.get_monitoring_status()
                
                # Update alerts
                if orchestrator.monitor_manager.alert_manager:
                    dashboard_data["alerts"] = orchestrator.monitor_manager.alert_manager.get_recent_alerts(20)
                
                # Update processes
                if orchestrator.monitor_manager.process_monitor:
                    processes = list(orchestrator.monitor_manager.process_monitor.get_current_processes())
                    dashboard_data["processes"] = [{"process": p} for p in processes]
                
                # Update connections
                if orchestrator.monitor_manager.network_monitor:
                    connections = list(orchestrator.monitor_manager.network_monitor.get_current_connections())
                    dashboard_data["connections"] = [{"connection": c} for c in connections]
                
                # Update file operations
                if orchestrator.monitor_manager.filesystem_monitor:
                    dashboard_data["file_operations"] = orchestrator.monitor_manager.filesystem_monitor.get_recent_operations(20)
                
                # Update correlated events
                if orchestrator.monitor_manager.event_correlator:
                    dashboard_data["correlated_events"] = orchestrator.monitor_manager.event_correlator.get_correlated_events(10)
                
                # Update responses
                if orchestrator.monitor_manager.response_manager:
                    dashboard_data["responses"] = orchestrator.monitor_manager.response_manager.get_response_history(10)
                
        except Exception as e:
            logger.error(f"Error updating dashboard data: {e}")
            
        # Wait for 5 seconds before updating again
        stop_event.wait(5.0)

def main():
    """Run the dashboard application."""
    global orchestrator, monitoring_thread, stop_event
    
    # Initialize orchestrator
    try:
        orchestrator = Orchestrator("config.json")
        logger.info("Orchestrator initialized")
    except Exception as e:
        logger.error(f"Error initializing orchestrator: {e}")
        
    # Start dashboard data update thread
    update_thread = threading.Thread(target=update_dashboard_data, daemon=True)
    update_thread.start()
    
    # Start the Flask app
    try:
        app.run(debug=False, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        logger.info("Dashboard stopped by user")
    finally:
        # Stop monitoring if running
        if orchestrator and orchestrator.monitor_manager:
            if orchestrator.get_monitoring_status().get("is_running", False):
                orchestrator.stop_monitoring()
                
        # Stop update thread
        stop_event.set()
        
if __name__ == "__main__":
    main()
