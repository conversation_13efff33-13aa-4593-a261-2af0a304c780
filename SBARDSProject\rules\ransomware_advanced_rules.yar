/*
 * SBARDS Project - Advanced Ransomware Detection Rules
 * These rules are designed to detect sophisticated ransomware threats
 * with a focus on file permission changes and encryption behaviors.
 */

import "pe"
import "hash"

rule Ransomware_Permission_Manipulation
{
    meta:
        description = "Detects ransomware that manipulates file permissions before encryption"
        author = "SBARDS Project"
        date = "2023-08-15"
        category = "ransomware"
        severity = "critical"

    strings:
        // Windows permission APIs
        $win_perm1 = "SetFileSecurity" ascii wide
        $win_perm2 = "SetNamedSecurityInfo" ascii wide

        // Linux/Unix permission APIs
        $unix_perm1 = "chmod" ascii wide
        $unix_perm2 = "chown" ascii wide

        // Encryption indicators
        $encrypt1 = "CryptEncrypt" ascii wide
        $encrypt2 = "AES_encrypt" ascii wide

    condition:
        (1 of ($win_perm*) or 1 of ($unix_perm*)) and
        (1 of ($encrypt*))
}

rule Ransomware_Shadow_Copy_Deletion
{
    meta:
        description = "Detects ransomware attempting to delete shadow copies"
        author = "SBARDS Project"
        date = "2023-08-15"
        category = "ransomware"
        severity = "critical"

    strings:
        // Shadow copy deletion commands
        $shadow1 = "vssadmin delete shadows" nocase ascii wide
        $shadow2 = "wmic shadowcopy delete" nocase ascii wide

        // Command line tools
        $cmd1 = "cmd.exe" nocase ascii wide
        $cmd2 = "powershell" nocase ascii wide

    condition:
        (1 of ($shadow*)) and
        (1 of ($cmd*))
}

rule Ransomware_Indicators
{
    meta:
        description = "Detects common ransomware indicators"
        author = "SBARDS Project"
        date = "2023-08-15"
        category = "ransomware"
        severity = "high"

    strings:
        // Ransomware strings
        $ransom1 = "encrypted" nocase ascii wide
        $ransom2 = "bitcoin" nocase ascii wide
        $ransom3 = "payment" nocase ascii wide
        $ransom4 = "decrypt" nocase ascii wide
        $ransom5 = "ransom" nocase ascii wide

    condition:
        2 of them
}
