"""
OSQuery Monitor for Windows

This module provides OSQuery-based monitoring for Windows systems.
"""

import os
import time
import json
import logging
import threading
import subprocess
from typing import Dict, List, Any, Optional, Set

class OSQueryMonitor:
    """
    OSQuery-based monitoring for Windows systems.

    This class uses OSQuery to monitor processes, files, and system information on Windows.
    """

    def __init__(self, config: Dict[str, Any], alert_manager=None):
        """
        Initialize the OSQuery monitor.

        Args:
            config (Dict[str, Any]): Configuration dictionary
            alert_manager: Alert manager instance
        """
        self.config = config
        self.alert_manager = alert_manager
        self.logger = logging.getLogger("SBARDS.Windows.OSQueryMonitor")

        # Check if OSQuery is installed
        self.osquery_installed = self._check_osquery_installed()
        if not self.osquery_installed:
            self.logger.warning("OSQuery is not installed. Some monitoring features will be disabled.")

        # Monitoring thread
        self.monitoring_thread = None
        self.stop_event = threading.Event()
        self.is_running = False

        # Monitoring interval
        self.monitoring_interval = config.get("osquery", {}).get("interval_seconds", 10)

        # Monitored items
        self.processes = set()
        self.files = set()
        self.registry_keys = set()
        self.network_connections = set()

        # File change tracking
        self.file_change_history = {}

        # Query results
        self.query_results = {}

        self.logger.info("OSQuery Monitor initialized")

    def _check_osquery_installed(self) -> bool:
        """
        Check if OSQuery is installed.

        Returns:
            bool: True if installed, False otherwise
        """
        try:
            # Try to run osqueryi with --version
            result = subprocess.run(
                ["osqueryi", "--version"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                self.logger.info(f"OSQuery installed: {result.stdout.strip()}")
                return True
            else:
                return False
        except Exception:
            return False

    def start_monitoring(self, stop_event: Optional[threading.Event] = None) -> bool:
        """
        Start OSQuery monitoring.

        Args:
            stop_event (Optional[threading.Event]): Event to signal stopping

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.osquery_installed:
            self.logger.error("OSQuery is not installed. Cannot start monitoring.")
            return False

        if self.is_running:
            self.logger.warning("OSQuery monitoring is already running")
            return True

        self.logger.info("Starting OSQuery monitoring")

        # Use provided stop event or internal one
        self.stop_event = stop_event or self.stop_event
        self.stop_event.clear()

        # Start monitoring thread
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self.monitoring_thread.start()

        self.is_running = True
        return True

    def stop_monitoring(self) -> bool:
        """
        Stop OSQuery monitoring.

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.is_running:
            return True

        self.logger.info("Stopping OSQuery monitoring")
        self.stop_event.set()

        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=10.0)

        self.is_running = False
        return True

    def _monitoring_loop(self) -> None:
        """OSQuery monitoring loop."""
        while not self.stop_event.is_set():
            try:
                # Run queries
                self._run_process_query()
                self._run_file_query()
                self._run_registry_query()
                self._run_network_query()

                # Wait for next monitoring cycle
                self.stop_event.wait(self.monitoring_interval)

            except Exception as e:
                self.logger.error(f"Error during OSQuery monitoring: {e}")
                # Wait a bit before retrying
                self.stop_event.wait(1.0)

        self.logger.info("OSQuery monitoring stopped")

    def _run_process_query(self) -> None:
        """Run OSQuery process query."""
        query = "SELECT pid, name, path, cmdline FROM processes"
        result = self._run_query(query)

        if result:
            # Update processes
            current_processes = set()
            for process in result:
                process_info = f"{process['name']}:{process['pid']}:{process['path']}"
                current_processes.add(process_info)

            # Check for new processes
            new_processes = current_processes - self.processes
            for process_info in new_processes:
                self.logger.debug(f"New process detected: {process_info}")

                # Generate alert for suspicious processes
                if self.alert_manager:
                    parts = process_info.split(":", 2)
                    name = parts[0] if len(parts) > 0 else "unknown"
                    pid = parts[1] if len(parts) > 1 else "unknown"
                    path = parts[2] if len(parts) > 2 else ""

                    # Check against suspicious patterns
                    suspicious_patterns = self.config.get("process_monitoring", {}).get(
                        "suspicious_process_patterns", []
                    )

                    for pattern in suspicious_patterns:
                        if pattern.lower() in name.lower() or pattern.lower() in path.lower():
                            self.alert_manager.add_alert(
                                source="OSQueryMonitor",
                                alert_type="suspicious_process",
                                message=f"Suspicious process detected: {name} (PID: {pid})",
                                severity="warning",
                                details={
                                    "process_name": name,
                                    "process_id": pid,
                                    "process_path": path,
                                    "matched_pattern": pattern
                                }
                            )
                            break

            # Update process set
            self.processes = current_processes

    def _run_file_query(self) -> None:
        """Run OSQuery file query."""
        # Check if we should monitor the whole device
        monitor_whole_device = self.config.get("monitoring", {}).get("monitor_whole_device", True)

        if monitor_whole_device:
            # Monitor system drives
            import string
            from ctypes import windll

            # Get all drives
            drives = []
            bitmask = windll.kernel32.GetLogicalDrives()
            for letter in string.ascii_uppercase:
                if bitmask & 1:
                    drives.append(f"{letter}:")
                bitmask >>= 1

            # Filter out network drives and removable drives
            watch_directories = []
            for drive in drives:
                try:
                    drive_type = windll.kernel32.GetDriveTypeW(drive)
                    # 3 = DRIVE_FIXED (hard drive)
                    if drive_type == 3:
                        watch_directories.append(drive + "\\")
                except Exception as e:
                    self.logger.error(f"Error checking drive {drive}: {e}")
        else:
            # Monitor specific directories
            watch_directories = self.config.get("filesystem_monitoring", {}).get(
                "watch_directories", []
            )

        for directory in watch_directories:
            if os.path.exists(directory):
                query = f"SELECT path, filename, size, mtime FROM file WHERE directory = '{directory}'"
                result = self._run_query(query)

                if result:
                    # Update files
                    current_files = set()
                    for file in result:
                        file_info = f"{file['path']}\\{file['filename']}"
                        current_files.add(file_info)

                    # Check for new files
                    new_files = current_files - self.files
                    for file_info in new_files:
                        self.logger.debug(f"New file detected: {file_info}")

                        # Generate alert for suspicious files
                        if self.alert_manager:
                            # Check file extension
                            suspicious_extensions = self.config.get("filesystem_monitoring", {}).get(
                                "suspicious_extensions", []
                            )

                            _, ext = os.path.splitext(file_info)
                            if ext.lower() in suspicious_extensions:
                                self.alert_manager.add_alert(
                                    source="OSQueryMonitor",
                                    alert_type="suspicious_file",
                                    message=f"Suspicious file detected: {file_info}",
                                    severity="warning",
                                    details={
                                        "file_path": file_info,
                                        "file_extension": ext.lower()
                                    }
                                )

                    # Check for modified files
                    for file_info in result:
                        file_path = os.path.join(file_info.get("path", ""), file_info.get("filename", ""))
                        file_size = file_info.get("size", 0)
                        file_mtime = file_info.get("mtime", 0)

                        # Create a unique identifier for the file
                        file_key = file_path.lower()

                        # Check if we've seen this file before
                        if file_key in self.file_change_history:
                            old_size = self.file_change_history[file_key].get("size", 0)
                            old_mtime = self.file_change_history[file_key].get("mtime", 0)

                            # Check if the file has changed
                            if old_size != file_size or old_mtime != file_mtime:
                                # File has changed
                                self.logger.debug(f"File changed: {file_path}")

                                # Generate alert for file changes
                                if self.alert_manager:
                                    self.alert_manager.add_alert(
                                        source="OSQueryMonitor",
                                        alert_type="file_change",
                                        message=f"File changed: {file_path}",
                                        severity="info",
                                        details={
                                            "file_path": file_path,
                                            "old_size": old_size,
                                            "new_size": file_size,
                                            "old_mtime": old_mtime,
                                            "new_mtime": file_mtime
                                        }
                                    )

                                # Update file change history
                                self.file_change_history[file_key] = {
                                    "size": file_size,
                                    "mtime": file_mtime,
                                    "last_change": time.time()
                                }
                        else:
                            # New file, add to history
                            self.file_change_history[file_key] = {
                                "size": file_size,
                                "mtime": file_mtime,
                                "first_seen": time.time(),
                                "last_change": time.time()
                            }

                    # Update file set
                    self.files = current_files

    def _run_registry_query(self) -> None:
        """Run OSQuery registry query."""
        # Monitor specific registry keys
        registry_keys = self.config.get("windows", {}).get(
            "registry_keys", [
                "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Run",
                "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run"
            ]
        )

        for key in registry_keys:
            query = f"SELECT path, name, data FROM registry WHERE path LIKE '{key}%'"
            result = self._run_query(query)

            if result:
                # Update registry keys
                current_keys = set()
                for reg_key in result:
                    key_info = f"{reg_key['path']}\\{reg_key['name']}"
                    current_keys.add(key_info)

                # Check for new registry keys
                new_keys = current_keys - self.registry_keys
                for key_info in new_keys:
                    self.logger.debug(f"New registry key detected: {key_info}")

                    # Generate alert for new autorun keys
                    if self.alert_manager and "Run" in key_info:
                        self.alert_manager.add_alert(
                            source="OSQueryMonitor",
                            alert_type="registry_change",
                            message=f"New autorun registry key detected: {key_info}",
                            severity="warning",
                            details={
                                "registry_key": key_info
                            }
                        )

                # Update registry key set
                self.registry_keys = current_keys

    def _run_network_query(self) -> None:
        """Run OSQuery network query."""
        query = "SELECT pid, family, protocol, local_address, local_port, remote_address, remote_port FROM process_open_sockets WHERE family = 2"
        result = self._run_query(query)

        if result:
            # Update network connections
            current_connections = set()
            for connection in result:
                conn_info = f"{connection['pid']}:{connection['protocol']}:{connection['local_address']}:{connection['local_port']}:{connection['remote_address']}:{connection['remote_port']}"
                current_connections.add(conn_info)

            # Check for new connections
            new_connections = current_connections - self.network_connections
            for conn_info in new_connections:
                self.logger.debug(f"New network connection detected: {conn_info}")

                # Generate alert for suspicious connections
                if self.alert_manager:
                    parts = conn_info.split(":")
                    if len(parts) >= 6:
                        pid = parts[0]
                        protocol = parts[1]
                        remote_address = parts[4]
                        remote_port = parts[5]

                        # Check against suspicious domains and ports
                        suspicious_domains = self.config.get("network_monitoring", {}).get(
                            "suspicious_domains", []
                        )

                        suspicious_ports = self.config.get("network_monitoring", {}).get(
                            "suspicious_ports", []
                        )

                        is_suspicious = False
                        reason = ""

                        # Check domain
                        for domain in suspicious_domains:
                            if domain in remote_address:
                                is_suspicious = True
                                reason = f"Suspicious domain: {domain}"
                                break

                        # Check port
                        if not is_suspicious and remote_port.isdigit():
                            port = int(remote_port)
                            if port in suspicious_ports:
                                is_suspicious = True
                                reason = f"Suspicious port: {port}"

                        if is_suspicious:
                            self.alert_manager.add_alert(
                                source="OSQueryMonitor",
                                alert_type="suspicious_connection",
                                message=f"Suspicious network connection detected: {remote_address}:{remote_port}",
                                severity="warning",
                                details={
                                    "process_id": pid,
                                    "protocol": protocol,
                                    "remote_address": remote_address,
                                    "remote_port": remote_port,
                                    "reason": reason
                                }
                            )

            # Update network connection set
            self.network_connections = current_connections

    def _run_query(self, query: str) -> List[Dict[str, Any]]:
        """
        Run an OSQuery query.

        Args:
            query (str): OSQuery query

        Returns:
            List[Dict[str, Any]]: Query results
        """
        try:
            # Run osqueryi with the query
            result = subprocess.run(
                ["osqueryi", "--json", query],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0 and result.stdout:
                return json.loads(result.stdout)
            else:
                return []
        except Exception as e:
            self.logger.error(f"Error running OSQuery query: {e}")
            return []
