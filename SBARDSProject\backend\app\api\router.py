"""
API router for the SBARDS Backend API.

This module provides the main API router for the SBARDS Backend API.
"""

from fastapi import APIRouter

from .endpoints import reports, files, stats

# Create API router
api_router = APIRouter()

# Include routers from endpoints
api_router.include_router(reports.router, prefix="/reports", tags=["reports"])
api_router.include_router(files.router, prefix="/files", tags=["files"])
api_router.include_router(stats.router, prefix="/stats", tags=["stats"])
