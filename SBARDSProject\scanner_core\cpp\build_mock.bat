@echo off
echo Building Mock YARA Scanner...

REM Compile the C++ code
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Auxiliary\Build\vcvars64.bat"
    cl /EHsc /Femock_scanner.exe mock_scanner.cpp
) else (
    g++ -o mock_scanner.exe mock_scanner.cpp
)

if %ERRORLEVEL% NEQ 0 (
    echo Compilation failed.
    exit /b 1
)

echo Build successful. The executable is mock_scanner.exe
