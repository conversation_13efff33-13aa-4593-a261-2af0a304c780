"""
Logging configuration for the SBARDS Backend API.

This module provides logging configuration for the SBARDS Backend API.
"""

import logging
import os
import sys
from typing import Dict, Any

from .config import settings


def setup_logging() -> Dict[str, Any]:
    """
    Set up logging configuration.
    
    Returns:
        Dict[str, Any]: Logging configuration dictionary.
    """
    log_level = getattr(logging, settings.LOG_LEVEL.upper())
    
    # Create logs directory if it doesn't exist
    logs_dir = os.path.dirname(settings.LOG_FILE)
    if logs_dir and not os.path.exists(logs_dir):
        os.makedirs(logs_dir, exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(settings.LOG_FILE),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # Reduce noise from third-party libraries
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    
    logger = logging.getLogger("SBARDS.Backend")
    logger.info(f"Logging configured with level: {settings.LOG_LEVEL}")
    
    return {
        "level": settings.LOG_LEVEL,
        "file": settings.LOG_FILE,
        "logger": logger
    }


# Create logger instance
logger = logging.getLogger("SBARDS.Backend")
