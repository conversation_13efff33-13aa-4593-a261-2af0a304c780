FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy requirements file
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY ./app /app
COPY ./alembic /app/alembic
COPY ./alembic.ini /app/alembic.ini

# Create necessary directories
RUN mkdir -p /data /app/uploads /app/logs

# Set environment variables
ENV PYTHONPATH=/
ENV DATABASE_URL=sqlite:////data/sbards.db
ENV UPLOAD_DIR=/app/uploads
ENV LOG_FILE=/app/logs/backend_api.log

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:8000/api/health || exit 1

# Run the application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
