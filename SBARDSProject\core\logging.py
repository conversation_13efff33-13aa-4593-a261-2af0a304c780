"""
Logging Module for SBARDS

This module provides logging functionality for the SBARDS project.
"""

import os
import logging
import logging.handlers
from datetime import datetime
from typing import Optional

class Logger:
    """
    Logger for the SBARDS project.

    This class handles setting up logging for the project.
    """

    def __init__(self, log_dir: str = "logs", log_level: str = "info"):
        """
        Initialize the logger.

        Args:
            log_dir (str): Directory to store log files
            log_level (str): Logging level (debug, info, warning, error, critical)
        """
        self.log_dir = log_dir
        self.log_level = self._get_log_level(log_level)

        # Create log directory if it doesn't exist
        os.makedirs(self.log_dir, exist_ok=True)

        # Set up logging
        self._setup_logging()

    def _get_log_level(self, log_level: str) -> int:
        """
        Get logging level from string.

        Args:
            log_level (str): Logging level string

        Returns:
            int: Logging level
        """
        log_levels = {
            "debug": logging.DEBUG,
            "info": logging.INFO,
            "warning": logging.WARNING,
            "error": logging.ERROR,
            "critical": logging.CRITICAL
        }

        return log_levels.get(log_level.lower(), logging.INFO)

    def _setup_logging(self) -> None:
        """Set up logging configuration."""
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

        # Create console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(self.log_level)
        console_handler.setFormatter(formatter)

        # Create file handler
        log_file = os.path.join(
            self.log_dir,
            f"sbards_{datetime.now().strftime('%Y%m%d')}.log"
        )
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=10485760,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(self.log_level)
        file_handler.setFormatter(formatter)

        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(self.log_level)

        # Remove existing handlers
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # Add handlers
        root_logger.addHandler(console_handler)
        root_logger.addHandler(file_handler)

        # Create SBARDS logger
        self.logger = logging.getLogger("SBARDS")
        self.logger.info(f"Logging initialized at level {logging.getLevelName(self.log_level)}")

    def get_logger(self, name: Optional[str] = None) -> logging.Logger:
        """
        Get a logger instance.

        Args:
            name (Optional[str]): Logger name

        Returns:
            logging.Logger: Logger instance
        """
        if name:
            return logging.getLogger(f"SBARDS.{name}")
        else:
            return self.logger


def setup_logging(level: str = "INFO", log_dir: str = "logs") -> None:
    """
    Set up logging for SBARDS.

    Args:
        level (str): Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_dir (str): Directory to store log files
    """
    logger = Logger(log_dir, level)


def get_logger(name: str = "SBARDS") -> logging.Logger:
    """
    Get a logger instance.

    Args:
        name (str): Logger name

    Returns:
        logging.Logger: Logger instance
    """
    return logging.getLogger(name)
