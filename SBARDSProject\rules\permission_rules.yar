/*
 * SBARDS Project - File Permission and Authorization Rules
 * These rules detect suspicious permission changes and authorization patterns
 * commonly associated with ransomware and other malware.
 */

rule Ransomware_Permission_Changes {
    meta:
        description = "Detects code that modifies file permissions extensively"
        author = "SBARDS Project"
        date = "2023-07-20"
        category = "ransomware"
        severity = "high"

    strings:
        // Windows API calls for permission changes
        $win_perm1 = "SetFileSecurity" ascii wide
        $win_perm2 = "SetNamedSecurityInfo" ascii wide
        $win_perm3 = "SetSecurityDescriptorDacl" ascii wide
        $win_perm4 = "SetSecurityDescriptorSacl" ascii wide

        // Linux/Unix permission changes
        $unix_perm1 = "chmod" ascii wide
        $unix_perm2 = "fchmod" ascii wide
        $unix_perm3 = "chown" ascii wide
        $unix_perm4 = "fchown" ascii wide

        // Permission constants
        $const1 = "FILE_ALL_ACCESS" ascii wide
        $const2 = "GENERIC_ALL" ascii wide
        $const3 = "0777" ascii wide

        // Ransomware strings
        $ransom1 = "encrypt" nocase ascii wide
        $ransom2 = "decrypt" nocase ascii wide
        $ransom3 = "bitcoin" nocase ascii wide

    condition:
        (2 of ($win_perm*) or 2 of ($unix_perm*)) and
        (1 of ($const*)) and
        (1 of ($ransom*))
}

rule Suspicious_Privilege_Escalation {
    meta:
        description = "Detects attempts to escalate privileges"
        author = "SBARDS Project"
        date = "2023-07-20"
        category = "general"
        severity = "high"

    strings:
        // Windows privilege escalation
        $win_priv1 = "AdjustTokenPrivileges" ascii wide
        $win_priv2 = "CreateProcessAsUser" ascii wide
        $win_priv3 = "SeDebugPrivilege" ascii wide
        $win_priv4 = "GetTokenInformation" ascii wide
        $win_priv5 = "LookupPrivilegeValue" ascii wide

        // Linux/Unix privilege escalation
        $unix_priv1 = "setuid" ascii wide
        $unix_priv2 = "setgid" ascii wide
        $unix_priv3 = "sudo" ascii wide
        $unix_priv4 = "su " ascii wide

        // UAC bypass strings
        $uac1 = "ConsentPromptBehaviorAdmin" ascii wide
        $uac2 = "eventvwr.exe" ascii wide
        $uac3 = "fodhelper" ascii wide
        $uac4 = "cmluYWNl" ascii wide // base64 "rinace"

    condition:
        (2 of ($win_priv*) or 2 of ($unix_priv*)) or
        (1 of ($uac*) and 1 of ($win_priv*))
}

rule Unauthorized_Access_Attempts {
    meta:
        description = "Detects patterns of unauthorized access attempts"
        author = "SBARDS Project"
        date = "2023-07-20"
        category = "general"
        severity = "medium"

    strings:
        // Credential access
        $cred1 = "LsaEnumerateLogonSessions" ascii wide
        $cred2 = "SamEnumerateUsersInDomain" ascii wide
        $cred3 = "CredEnumerate" ascii wide
        $cred4 = "LogonUser" ascii wide

        // Password related
        $pass1 = "password" nocase ascii wide
        $pass2 = "mimikatz" nocase ascii wide
        $pass3 = "lsass.exe" ascii wide
        $pass4 = "SAM" ascii wide

        // Linux credential access
        $linux_cred1 = "/etc/passwd" ascii wide
        $linux_cred2 = "/etc/shadow" ascii wide
        $linux_cred3 = ".bash_history" ascii wide

    condition:
        (2 of ($cred*)) or
        (1 of ($cred*) and 1 of ($pass*)) or
        (2 of ($linux_cred*))
}

rule Ransomware_Access_Control_Tampering {
    meta:
        description = "Detects ransomware tampering with access controls"
        author = "SBARDS Project"
        date = "2023-07-20"
        category = "ransomware"
        severity = "critical"

    strings:
        // ACL manipulation
        $acl1 = "SetEntriesInAcl" ascii wide
        $acl2 = "GetSecurityDescriptorDacl" ascii wide
        $acl3 = "SetSecurityDescriptorDacl" ascii wide

        // Shadow copy deletion
        $shadow1 = "vssadmin delete shadows" nocase ascii wide
        $shadow2 = "wmic shadowcopy delete" nocase ascii wide
        $shadow3 = "Win32_ShadowCopy" ascii wide

        // Boot configuration
        $boot1 = "bcdedit" ascii wide
        $boot2 = "/set {default} recoveryenabled No" ascii wide
        $boot3 = "/set {default} bootstatuspolicy ignoreallfailures" ascii wide

        // Encryption indicators
        $enc1 = "CryptEncrypt" ascii wide
        $enc2 = "CryptGenKey" ascii wide
        $enc3 = "EVP_EncryptInit" ascii wide
        $enc4 = "EVP_CIPHER_CTX_new" ascii wide

    condition:
        (1 of ($acl*)) and
        ((1 of ($shadow*)) or (1 of ($boot*))) and
        (1 of ($enc*))
}

rule File_Permission_Enumeration {
    meta:
        description = "Detects suspicious enumeration of file permissions"
        author = "SBARDS Project"
        date = "2023-07-20"
        category = "general"
        severity = "medium"

    strings:
        // Windows permission enumeration
        $win_enum1 = "GetFileSecurity" ascii wide
        $win_enum2 = "GetSecurityInfo" ascii wide
        $win_enum3 = "AccessCheck" ascii wide
        $win_enum4 = "GetEffectiveRightsFromAcl" ascii wide

        // Linux permission enumeration
        $unix_enum1 = "stat" ascii wide
        $unix_enum2 = "access" ascii wide
        $unix_enum3 = "getfacl" ascii wide

        // File system traversal
        $fs1 = "FindFirstFile" ascii wide
        $fs2 = "FindNextFile" ascii wide
        $fs3 = "readdir" ascii wide
        $fs4 = "opendir" ascii wide

        // Suspicious combinations
        $susp1 = "encrypt" nocase ascii wide
        $susp2 = "ransom" nocase ascii wide

    condition:
        ((2 of ($win_enum*)) or (2 of ($unix_enum*))) and
        (2 of ($fs*)) and
        (any of ($susp*))
}

rule Advanced_Ransomware_File_Operations {
    meta:
        description = "Detects ransomware that manipulates file operations and permissions"
        author = "SBARDS Project"
        date = "2025-05-14"
        category = "ransomware"
        severity = "critical"
        compatibility = "python,cpp"

    strings:
        // Advanced file operations
        $file_op1 = "MoveFileEx" ascii wide
        $file_op2 = "ReplaceFile" ascii wide
        $file_op3 = "CopyFile" ascii wide
        $file_op4 = "DeleteFile" ascii wide
        $file_op5 = "rename" ascii wide
        $file_op6 = "unlink" ascii wide

        // Permission manipulation
        $perm1 = "SetFileSecurity" ascii wide
        $perm2 = "SetNamedSecurityInfo" ascii wide
        $perm3 = "chmod" ascii wide
        $perm4 = "icacls" ascii wide
        $perm5 = "cacls" ascii wide

        // Encryption indicators
        $enc1 = "CryptProtectData" ascii wide
        $enc2 = "CryptEncrypt" ascii wide
        $enc3 = "EVP_EncryptUpdate" ascii wide
        $enc4 = "AES_encrypt" ascii wide
        $enc5 = "CCCrypt" ascii wide
        $enc6 = "RtlEncryptMemory" ascii wide

        // Ransomware behaviors
        $ransom1 = "readme.txt" nocase ascii wide
        $ransom2 = "how_to_decrypt" nocase ascii wide
        $ransom3 = "your_files_are_encrypted" nocase ascii wide
        $ransom4 = "payment" nocase ascii wide
        $ransom5 = "bitcoin" nocase ascii wide
        $ransom6 = "monero" nocase ascii wide
        $ransom7 = "wallet" nocase ascii wide
        $ransom8 = ".locked" nocase ascii wide
        $ransom9 = ".encrypted" nocase ascii wide
        $ransom10 = ".crypted" nocase ascii wide

    condition:
        (3 of ($file_op*)) and
        (2 of ($perm*)) and
        (2 of ($enc*)) and
        (2 of ($ransom*))
}

rule Suspicious_Permission_Monitoring {
    meta:
        description = "Detects malware that monitors file permission changes"
        author = "SBARDS Project"
        date = "2025-05-14"
        category = "malware"
        severity = "medium"
        compatibility = "python,cpp"

    strings:
        // File monitoring APIs
        $monitor1 = "ReadDirectoryChangesW" ascii wide
        $monitor2 = "FindFirstChangeNotification" ascii wide
        $monitor3 = "inotify_init" ascii wide
        $monitor4 = "inotify_add_watch" ascii wide
        $monitor5 = "kqueue" ascii wide
        $monitor6 = "kevent" ascii wide

        // Permission checking
        $check1 = "GetFileSecurity" ascii wide
        $check2 = "GetNamedSecurityInfo" ascii wide
        $check3 = "GetSecurityDescriptorDacl" ascii wide
        $check4 = "stat" ascii wide
        $check5 = "access" ascii wide

        // Suspicious behaviors
        $susp1 = "hidden" nocase ascii wide
        $susp2 = "stealth" nocase ascii wide
        $susp3 = "inject" nocase ascii wide
        $susp4 = "hook" nocase ascii wide
        $susp5 = "keylog" nocase ascii wide
        $susp6 = "screenshot" nocase ascii wide

    condition:
        (2 of ($monitor*)) and
        (2 of ($check*)) and
        (1 of ($susp*))
}

rule Kernel_Permission_Manipulation {
    meta:
        description = "Detects threats that manipulate kernel-level permissions"
        author = "SBARDS Project"
        date = "2025-05-14"
        category = "rootkit"
        severity = "critical"
        compatibility = "python,cpp"

    strings:
        // Kernel mode operations
        $kernel1 = "NtLoadDriver" ascii wide
        $kernel2 = "ZwLoadDriver" ascii wide
        $kernel3 = "NtSetSystemInformation" ascii wide
        $kernel4 = "ZwSetSystemInformation" ascii wide
        $kernel5 = "insmod" ascii wide
        $kernel6 = "modprobe" ascii wide

        // Security token manipulation
        $token1 = "SeDebugPrivilege" ascii wide
        $token2 = "SeTcbPrivilege" ascii wide
        $token3 = "SeAssignPrimaryTokenPrivilege" ascii wide
        $token4 = "SeLoadDriverPrivilege" ascii wide
        $token5 = "AdjustTokenPrivileges" ascii wide
        $token6 = "NtAdjustPrivilegesToken" ascii wide

        // Rootkit behaviors
        $rootkit1 = "DKOM" ascii wide
        $rootkit2 = "DirectoryObject" ascii wide
        $rootkit3 = "ObRegisterCallbacks" ascii wide
        $rootkit4 = "PsSetCreateProcessNotifyRoutine" ascii wide
        $rootkit5 = "PsSetLoadImageNotifyRoutine" ascii wide
        $rootkit6 = "IoCreateDriver" ascii wide

    condition:
        (2 of ($kernel*)) and
        (2 of ($token*)) and
        (1 of ($rootkit*))
}

rule Ransomware_Volume_Shadow_Manipulation {
    meta:
        description = "Detects ransomware that targets volume shadow copies with permission changes"
        author = "SBARDS Project"
        date = "2025-05-14"
        category = "ransomware"
        severity = "high"
        compatibility = "python,cpp"

    strings:
        // Volume Shadow Copy deletion
        $vss1 = "vssadmin delete shadows" nocase ascii wide
        $vss2 = "wmic shadowcopy delete" nocase ascii wide
        $vss3 = "Win32_ShadowCopy" ascii wide
        $vss4 = "VSSVC" ascii wide
        $vss5 = "swprv.dll" ascii wide
        $vss6 = "VSS_SHADOW_COPY" ascii wide

        // Windows Management Instrumentation
        $wmi1 = "winmgmts:" ascii wide
        $wmi2 = "Win32_Process" ascii wide
        $wmi3 = "Win32_Service" ascii wide
        $wmi4 = "ExecQuery" ascii wide

        // Boot configuration
        $boot1 = "bcdedit" ascii wide
        $boot2 = "/set {default} recoveryenabled No" ascii wide
        $boot3 = "/set {default} bootstatuspolicy ignoreallfailures" ascii wide

        // Encryption operations
        $enc1 = "CryptAcquireContext" ascii wide
        $enc2 = "CryptGenKey" ascii wide
        $enc3 = "CryptEncrypt" ascii wide
        $enc4 = "CryptImportKey" ascii wide

    condition:
        (2 of ($vss*)) and
        (1 of ($wmi*)) and
        (1 of ($boot*)) and
        (1 of ($enc*))
}

rule Fileless_Malware_Permission_Abuse {
    meta:
        description = "Detects fileless malware that abuses permissions"
        author = "SBARDS Project"
        date = "2025-05-14"
        category = "malware"
        severity = "high"
        compatibility = "python,cpp"

    strings:
        // Memory injection techniques
        $inject1 = "VirtualAlloc" ascii wide
        $inject2 = "VirtualProtect" ascii wide
        $inject3 = "WriteProcessMemory" ascii wide
        $inject4 = "CreateRemoteThread" ascii wide
        $inject5 = "NtMapViewOfSection" ascii wide
        $inject6 = "mmap" ascii wide
        $inject7 = "mprotect" ascii wide

        // Registry persistence
        $reg1 = "RegSetValue" ascii wide
        $reg2 = "RegCreateKey" ascii wide
        $reg3 = "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run" ascii wide
        $reg4 = "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\RunOnce" ascii wide

        // WMI persistence
        $wmi1 = "__EventFilter" ascii wide
        $wmi2 = "CommandLineEventConsumer" ascii wide
        $wmi3 = "__FilterToConsumerBinding" ascii wide
        $wmi4 = "ActiveScriptEventConsumer" ascii wide

        // PowerShell/script execution
        $script1 = "powershell" nocase ascii wide
        $script2 = "-encodedcommand" nocase ascii wide
        $script3 = "-noprofile" nocase ascii wide
        $script4 = "-windowstyle hidden" nocase ascii wide
        $script5 = "IEX" nocase ascii wide
        $script6 = "Invoke-Expression" nocase ascii wide

    condition:
        (2 of ($inject*)) and
        ((1 of ($reg*)) or (1 of ($wmi*))) and
        (2 of ($script*))
}