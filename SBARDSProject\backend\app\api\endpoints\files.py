"""
API endpoints for file operations.

This module provides API endpoints for file operations.
"""

import os
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, File, UploadFile, Form, Query, status
from sqlalchemy.orm import Session

from ...core.config import settings
from ...core.logging import logger
from ...core.security import calculate_file_hash
from ...db.session import get_db
from ...db.models import FileResult
from ...schemas.files import FileCheckResponse, FileHashCheck
from ...services.virustotal import check_file_hash, submit_file, parse_virustotal_result

# Create router
router = APIRouter()


@router.post(
    "/check",
    response_model=FileCheckResponse,
    summary="Check a file against VirusTotal",
    description="Upload a file to check it against VirusTotal's database of known threats."
)
async def check_file(
    file: UploadFile = File(..., description="The file to check"),
    save_result: bool = Form(False, description="Whether to save the result to the database"),
    db: Session = Depends(get_db)
):
    """
    Check a file against VirusTotal.
    
    - **file**: The file to check
    - **save_result**: Whether to save the result to the database (default: false)
    
    The file will be temporarily saved, hashed, and then the hash will be checked against VirusTotal.
    The file is deleted after processing.
    """
    logger.info(f"Checking file against VirusTotal: {file.filename}")
    
    # Create uploads directory if it doesn't exist
    os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
    
    # Save the uploaded file temporarily
    temp_file_path = os.path.join(settings.UPLOAD_DIR, f"temp_{file.filename}")
    try:
        # Read file content
        file_content = await file.read()
        
        # Check file size
        if len(file_content) > settings.MAX_UPLOAD_SIZE:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"File size exceeds maximum allowed size of {settings.MAX_UPLOAD_SIZE} bytes"
            )
        
        # Save file temporarily
        with open(temp_file_path, "wb") as f:
            f.write(file_content)
        
        # Calculate file hash
        file_hash = calculate_file_hash(file_content)
        logger.info(f"File hash: {file_hash}")
        
        # Check VirusTotal
        vt_result = await check_file_hash(file_hash)
        
        # If file not found in VirusTotal and file is small enough, submit it
        if "error" in vt_result and "not found" in vt_result["error"] and len(file_content) <= 32 * 1024 * 1024:
            logger.info(f"File not found in VirusTotal, submitting: {file.filename}")
            submit_result = await submit_file(file_content, file.filename)
            if "error" not in submit_result:
                logger.info(f"File submitted to VirusTotal: {file.filename}")
                vt_result = {"message": "File submitted to VirusTotal for analysis"}
        
        # Save result to database if requested
        if save_result and "error" not in vt_result:
            try:
                # Create a file result entry
                file_result = FileResult(
                    file_path=file.filename,
                    file_hash=file_hash,
                    is_threat=False,  # Will be updated based on VT result
                    virustotal_result=str(vt_result)
                )
                
                # Check if it's a threat based on VirusTotal result
                if "data" in vt_result and "attributes" in vt_result["data"]:
                    attributes = vt_result["data"]["attributes"]
                    if "last_analysis_stats" in attributes:
                        stats = attributes["last_analysis_stats"]
                        if stats.get("malicious", 0) > 0:
                            file_result.is_threat = True
                            file_result.threat_type = f"VirusTotal: {stats.get('malicious', 0)} detections"
                
                db.add(file_result)
                db.commit()
                logger.info(f"Saved VirusTotal result for {file.filename}")
            except Exception as e:
                logger.error(f"Error saving VirusTotal result: {e}")
                db.rollback()
        
        # Parse VirusTotal result
        parsed_result = parse_virustotal_result(vt_result)
        
        # Prepare response
        response = {
            "filename": file.filename,
            "file_hash": file_hash,
            "virustotal_result": vt_result
        }
        
        # Add a summary if available
        if "stats" in parsed_result:
            response["summary"] = parsed_result["stats"]
        
        return response
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error checking file against VirusTotal: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error checking file: {str(e)}"
        )
    finally:
        # Clean up temporary file
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)


@router.post(
    "/check-hash",
    response_model=dict,
    summary="Check a file hash against VirusTotal",
    description="Check a file hash against VirusTotal's database of known threats."
)
async def check_file_hash_endpoint(
    file_hash: FileHashCheck
):
    """
    Check a file hash against VirusTotal.
    
    - **file_hash**: SHA-256 hash of the file
    
    Returns the VirusTotal result for the specified file hash.
    """
    try:
        logger.info(f"Checking file hash against VirusTotal: {file_hash.file_hash}")
        
        # Check VirusTotal
        vt_result = await check_file_hash(file_hash.file_hash)
        
        # Parse VirusTotal result
        parsed_result = parse_virustotal_result(vt_result)
        
        return {
            "file_hash": file_hash.file_hash,
            "virustotal_result": vt_result,
            "summary": parsed_result.get("stats", {})
        }
    except Exception as e:
        logger.error(f"Error checking file hash against VirusTotal: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error checking file hash: {str(e)}"
        )
