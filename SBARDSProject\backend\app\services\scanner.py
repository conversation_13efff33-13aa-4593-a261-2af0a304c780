"""
Scanner integration service.

This module provides integration with the SBARDS scanner.
"""

import os
import re
import json
import asyncio
import subprocess
from typing import Dict, List, Any, Optional

from ..core.logging import logger
from ..core.config import settings
from ..core.security import calculate_file_hash, generate_scan_id
from .reports import parse_html_report


def extract_file_results(log_file: str) -> List[Dict[str, Any]]:
    """
    Extract file results from a scanner log file.

    Args:
        log_file (str): Path to the scanner log file.

    Returns:
        List[Dict[str, Any]]: List of file results.
    """
    file_results = []
    try:
        with open(log_file, "r", encoding="utf-8") as f:
            log_content = f.readlines()

        for line in log_content:
            if "[WARNING]" in line and "Potential threat found in" in line:
                # Extract file path
                file_path_match = line.split("Potential threat found in")[1].strip()
                if file_path_match:
                    file_path = file_path_match
                    file_hash = ""

                    # Calculate file hash if file exists
                    if os.path.exists(file_path):
                        with open(file_path, "rb") as f:
                            content = f.read()
                            file_hash = calculate_file_hash(content)

                    file_results.append({
                        "file_path": file_path,
                        "file_hash": file_hash,
                        "is_threat": True,
                        "threat_type": "Potential Threat"
                    })
    except Exception as e:
        logger.error(f"Error extracting file results from log {log_file}: {e}")

    return file_results


def process_scan_report(report_path: str, log_file: str) -> Dict[str, Any]:
    """
    Process a scan report and extract relevant information.

    Args:
        report_path (str): Path to the HTML report.
        log_file (str): Path to the scanner log file.

    Returns:
        Dict[str, Any]: Processed scan report data.
    """
    try:
        # Parse the HTML report
        report_data = parse_html_report(report_path)

        # Extract file results from log
        file_results = extract_file_results(log_file)

        # Create the report data structure
        from datetime import datetime
        scan_id = f"scan_{datetime.now().strftime('%Y%m%d%H%M%S')}"

        report_payload = {
            "scan_id": scan_id,
            "scan_path": report_data["scan_path"],
            "files_scanned": report_data["files_scanned"],
            "threats_found": report_data["threats_found"],
            "report_path": report_path,
            "report_content": report_data["report_content"],
            "file_results": file_results
        }

        return report_payload
    except Exception as e:
        logger.error(f"Error processing scan report: {e}")
        return {"error": str(e)}


async def run_scan(scan_path: str, scan_id: str, recursive: bool = True) -> Dict[str, Any]:
    """
    Run a scan on a file or directory.

    Args:
        scan_path (str): Path to scan.
        scan_id (str): Scan ID.
        recursive (bool): Whether to scan recursively.

    Returns:
        Dict[str, Any]: Scan result.
    """
    try:
        logger.info(f"Running scan on {scan_path} with ID {scan_id}")

        # Import here to avoid circular imports
        from ..api.v1.endpoints.websocket import send_scan_update

        # Create output directory for this scan
        output_dir = os.path.join(settings.UPLOAD_DIR, scan_id, "output")
        os.makedirs(output_dir, exist_ok=True)

        # Create logs directory for this scan
        logs_dir = os.path.join(settings.UPLOAD_DIR, scan_id, "logs")
        os.makedirs(logs_dir, exist_ok=True)

        # Create status file
        status_dir = os.path.join(settings.UPLOAD_DIR, scan_id)
        os.makedirs(status_dir, exist_ok=True)
        status_file = os.path.join(status_dir, "status.json")

        # Update status to "starting"
        status_data = {
            "scan_id": scan_id,
            "status": "starting",
            "message": "Preparing to scan"
        }
        with open(status_file, "w") as f:
            json.dump(status_data, f)

        # Send WebSocket update
        await send_scan_update(scan_id, status_data)

        # Determine if it's a file or directory
        is_directory = os.path.isdir(scan_path)

        # Update status to "scanning"
        status_data = {
            "scan_id": scan_id,
            "status": "scanning",
            "message": f"Scanning {'directory' if is_directory else 'file'}: {scan_path}"
        }
        with open(status_file, "w") as f:
            json.dump(status_data, f)

        # Send WebSocket update
        await send_scan_update(scan_id, status_data)

        # Prepare command
        cmd = [
            "python",
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), "run_scanner.py"),
            "scan",
            scan_path,
            "--output", output_dir,
            "--format", "html",
        ]

        if not recursive and is_directory:
            cmd.append("--no-recursive")

        # Run the command
        logger.debug(f"Running command: {' '.join(cmd)}")
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        # Update status to "processing"
        status_data = {
            "scan_id": scan_id,
            "status": "processing",
            "message": "Scan in progress"
        }
        with open(status_file, "w") as f:
            json.dump(status_data, f)

        # Send WebSocket update
        await send_scan_update(scan_id, status_data)

        stdout, stderr = await process.communicate()

        # Check if the process was successful
        if process.returncode != 0:
            logger.error(f"Scan process failed with return code {process.returncode}")
            logger.error(f"Stderr: {stderr.decode()}")

            error_data = {
                "scan_id": scan_id,
                "status": "error",
                "message": f"Scan process failed with return code {process.returncode}"
            }

            # Update status file
            with open(status_file, "w") as f:
                json.dump(error_data, f)

            # Send WebSocket update
            await send_scan_update(scan_id, error_data)

            return error_data

        # Update status to "analyzing"
        status_data = {
            "scan_id": scan_id,
            "status": "analyzing",
            "message": "Analyzing scan results"
        }
        with open(status_file, "w") as f:
            json.dump(status_data, f)

        # Send WebSocket update
        await send_scan_update(scan_id, status_data)

        # Find the report file
        report_files = []
        for root, _, files in os.walk(output_dir):
            for file in files:
                if file.startswith("scan_report_") and file.endswith(".html"):
                    report_files.append(os.path.join(root, file))

        if not report_files:
            logger.error(f"No report file found for scan {scan_id}")

            error_data = {
                "scan_id": scan_id,
                "status": "error",
                "message": "No report file found"
            }

            # Update status file
            with open(status_file, "w") as f:
                json.dump(error_data, f)

            # Send WebSocket update
            await send_scan_update(scan_id, error_data)

            return error_data

        # Sort by modification time (newest first)
        report_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
        report_path = report_files[0]

        # Find the log file
        log_file = os.path.join(logs_dir, "scanner.log")
        if not os.path.exists(log_file):
            logger.warning(f"No log file found for scan {scan_id}")
            log_file = None

        # Process the report
        if log_file:
            report_data = process_scan_report(report_path, log_file)
        else:
            # Parse the HTML report without log file
            report_data = parse_html_report(report_path)
            report_data = {
                "scan_id": scan_id,
                "scan_path": scan_path,
                "files_scanned": report_data.get("files_scanned", 0),
                "threats_found": report_data.get("threats_found", 0),
                "report_path": report_path,
                "report_content": report_data.get("report_content", ""),
                "file_results": []
            }

        # Save report to database
        try:
            from ..db.session import SessionLocal
            from ..db.models import ScanReport, FileResult

            db = SessionLocal()
            try:
                # Create scan report
                db_report = ScanReport(
                    scan_id=scan_id,
                    scan_path=scan_path,
                    files_scanned=report_data.get("files_scanned", 0),
                    threats_found=report_data.get("threats_found", 0),
                    report_path=report_path,
                    report_content=report_data.get("report_content", "")
                )
                db.add(db_report)
                db.flush()

                # Create file results
                for file_result in report_data.get("file_results", []):
                    db_file_result = FileResult(
                        scan_report_id=db_report.id,
                        file_path=file_result.get("file_path", ""),
                        file_hash=file_result.get("file_hash", ""),
                        is_threat=file_result.get("is_threat", False),
                        threat_type=file_result.get("threat_type"),
                        virustotal_result=file_result.get("virustotal_result")
                    )
                    db.add(db_file_result)

                db.commit()
                logger.info(f"Saved scan report to database: {db_report.id}")
            except Exception as db_error:
                db.rollback()
                logger.error(f"Error saving scan report to database: {db_error}")
            finally:
                db.close()
        except Exception as db_error:
            logger.error(f"Error with database operations: {db_error}")

        # Create final result
        result = {
            "scan_id": scan_id,
            "status": "completed",
            "files_scanned": report_data.get("files_scanned", 0),
            "threats_found": report_data.get("threats_found", 0),
            "report_path": report_path,
            "message": "Scan completed successfully"
        }

        # Update status file
        with open(status_file, "w") as f:
            json.dump(result, f)

        # Send WebSocket update
        await send_scan_update(scan_id, result)

        return result
    except Exception as e:
        logger.error(f"Error running scan: {e}")

        error_data = {
            "scan_id": scan_id,
            "status": "error",
            "message": f"Error running scan: {str(e)}"
        }

        # Update status file
        try:
            status_file = os.path.join(settings.UPLOAD_DIR, scan_id, "status.json")
            with open(status_file, "w") as f:
                json.dump(error_data, f)
        except Exception:
            pass

        # Send WebSocket update
        try:
            from ..api.v1.endpoints.websocket import send_scan_update
            await send_scan_update(scan_id, error_data)
        except Exception:
            pass

        return error_data
