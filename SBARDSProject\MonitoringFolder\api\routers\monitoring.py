"""
Monitoring API Router for SBARDS

This module provides API endpoints for the Monitoring phase of the SBARDS project.
"""

import os
import logging
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from typing import Dict, List, Any, Optional

# Import models
from api.models.monitoring import MonitoringStatus, AlertResponse

# Create router
router = APIRouter()

# Global variables
monitoring_manager = None

# Dependency to get the manager
def get_manager():
    """
    Get the Monitoring manager.
    
    Returns:
        Any: Monitoring manager
    """
    if monitoring_manager is None:
        raise HTTPException(status_code=503, detail="Monitoring manager not initialized")
    return monitoring_manager

# Set the manager
def set_manager(manager):
    """
    Set the Monitoring manager.
    
    Args:
        manager: Monitoring manager
    """
    global monitoring_manager
    monitoring_manager = manager

# Endpoints
@router.post("/start", response_model=MonitoringStatus)
async def start_monitoring(
    background_tasks: BackgroundTasks,
    manager = Depends(get_manager)
):
    """
    Start monitoring.
    
    Args:
        background_tasks (BackgroundTasks): Background tasks
        manager: Monitoring manager
        
    Returns:
        MonitoringStatus: Monitoring status
    """
    # Start monitoring in background
    background_tasks.add_task(manager.start_monitoring)
    
    return {
        "status": "starting",
        "message": "Monitoring is starting"
    }

@router.post("/stop", response_model=MonitoringStatus)
async def stop_monitoring(
    background_tasks: BackgroundTasks,
    manager = Depends(get_manager)
):
    """
    Stop monitoring.
    
    Args:
        background_tasks (BackgroundTasks): Background tasks
        manager: Monitoring manager
        
    Returns:
        MonitoringStatus: Monitoring status
    """
    # Stop monitoring in background
    background_tasks.add_task(manager.stop_monitoring)
    
    return {
        "status": "stopping",
        "message": "Monitoring is stopping"
    }

@router.get("/status", response_model=Dict[str, Any])
async def get_monitoring_status(
    manager = Depends(get_manager)
):
    """
    Get monitoring status.
    
    Args:
        manager: Monitoring manager
        
    Returns:
        Dict[str, Any]: Monitoring status
    """
    return manager.get_status()

@router.get("/alerts", response_model=List[AlertResponse])
async def get_alerts(
    limit: int = 100,
    severity: Optional[str] = None,
    manager = Depends(get_manager)
):
    """
    Get alerts.
    
    Args:
        limit (int): Maximum number of alerts to return
        severity (Optional[str]): Filter alerts by severity
        manager: Monitoring manager
        
    Returns:
        List[AlertResponse]: List of alerts
    """
    return manager.get_alerts(limit, severity)
