import os
import yara
import platform
import logging
from typing import List, Dict, Any, Optional

class YaraScanner:
    """
    Enhanced YARA scanner for the SBARDS Project.
    Supports multiple rule files and rule categories.
    """

    def __init__(self, rule_files: List[str], categories: List[str] = None):
        """
        Initialize the YARA scanner with the specified rule files.

        Args:
            rule_files (List[str]): List of paths to YARA rule files
            categories (List[str], optional): List of rule categories to enable
        """
        self.rule_files = [os.path.abspath(rule_file) for rule_file in rule_files]
        self.categories = categories or ["all"]
        self.platform = platform.system()
        self.logger = logging.getLogger("SBARDS.YaraScanner")

        self.rules = self.compile_rules()

        if self.rules:
            self.logger.info(f"Successfully compiled YARA rules from {len(self.rule_files)} files")
        else:
            self.logger.warning("Failed to compile YARA rules")

    def compile_rules(self) -> Optional[Any]:
        """
        Compile YARA rules from the specified files.

        Returns:
            Optional[Any]: Compiled YARA rules or None if compilation failed
        """
        try:
            # Check if rule files exist
            for rule_file in self.rule_files:
                if not os.path.exists(rule_file):
                    self.logger.error(f"Rule file not found: {rule_file}")
                    return None

            # Compile rules with namespace
            namespaces = {}
            for rule_file in self.rule_files:
                file_name = os.path.basename(rule_file)
                namespace = os.path.splitext(file_name)[0]
                namespaces[namespace] = rule_file

            # If categories include "all", don't filter by category
            if "all" in self.categories:
                return yara.compile(filepaths=namespaces)

            # Otherwise, compile with category filtering
            rules_source = ""
            for rule_file in self.rule_files:
                with open(rule_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()

                    # Extract rules that match the specified categories
                    import re
                    rule_blocks = re.findall(r'rule\s+([^{]+){([^}]+)}', content, re.DOTALL)

                    for rule_name, rule_body in rule_blocks:
                        # Check if the rule has a category that matches
                        category_match = re.search(r'category\s*=\s*"([^"]+)"', rule_body)
                        if category_match:
                            category = category_match.group(1).lower()
                            if category in self.categories:
                                rules_source += f"rule {rule_name}{{{rule_body}}}\n\n"
                        else:
                            # If no category is specified, include the rule
                            rules_source += f"rule {rule_name}{{{rule_body}}}\n\n"

            return yara.compile(source=rules_source)

        except yara.SyntaxError as e:
            self.logger.error(f"YARA syntax issue: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Failed to compile rules: {e}")
            return None

    def scan_file(self, file_path: str) -> List[Dict[str, Any]]:
        """
        Scan a file with the compiled YARA rules.

        Args:
            file_path (str): Path to the file to scan

        Returns:
            List[Dict[str, Any]]: List of match dictionaries with rule information
        """
        if not self.rules:
            return []

        # Convert to absolute path for consistency
        abs_file_path = os.path.abspath(file_path)

        # Check if the file exists
        if not os.path.exists(abs_file_path):
            self.logger.error(f"File not found: {abs_file_path}")
            return []

        # Check if the file is accessible
        if not os.access(abs_file_path, os.R_OK):
            self.logger.error(f"File not readable: {abs_file_path}")
            return []

        try:
            matches = self.rules.match(filepath=abs_file_path)

            # Convert matches to a more detailed format
            detailed_matches = []
            for match in matches:
                match_info = {
                    "rule": match.rule,
                    "namespace": match.namespace,
                    "tags": match.tags,
                    "meta": match.meta,
                    "strings": [
                        {
                            "identifier": s[1],
                            "data": s[2].hex() if isinstance(s[2], bytes) else s[2],
                            "offset": s[0]
                        } for s in match.strings
                    ]
                }
                detailed_matches.append(match_info)

            return detailed_matches
        except Exception as e:
            self.logger.error(f"Scan failed for {abs_file_path}: {e}")
            return []