#include <yara.h>
#include <iostream>

int main(int argc, char* argv[]) {
    if (argc < 3) {
        std::cerr << "Usage: yara_scanner.exe <rules.yar> <target_file>\n";
        return 1;
    }

    const char* rules_file = argv[1];
    const char* target_file = argv[2];

    YR_RULES* rules = nullptr;
    YR_COMPILER* compiler = nullptr;

    if (yr_initialize() != ERROR_SUCCESS) return 1;

    if (yr_compiler_create(&compiler) != ERROR_SUCCESS) return 1;

    FILE* rule_fp = fopen(rules_file, "r");
    if (!rule_fp) {
        std::cerr << "Could not open rules file.\n";
        return 1;
    }

    yr_compiler_add_file(compiler, rule_fp, nullptr, rules_file);
    fclose(rule_fp);

    yr_compiler_get_rules(compiler, &rules);

    yr_rules_scan_file(
        rules, target_file, 0,
        [](YR_<PERSON>AN_CONTEXT* context, int message, void* message_data, void* user_data) -> int {
            if (message == CALLBACK_MSG_RULE_MATCHING) {
                YR_RULE* rule = (YR_RULE*)message_data;
                std::cout << "Matched rule: " << rule->identifier << "\n";
            }
            return CALLBACK_CONTINUE;
        },
        nullptr, 0
    );

    yr_rules_destroy(rules);
    yr_compiler_destroy(compiler);
    yr_finalize();
    return 0;
}