"""
Mock Monitor Base Class for SBARDS

This module provides a mock monitor base class for the SBARDS project.

The MockMonitor class serves as a base class for all mock monitoring implementations,
providing common functionality and a consistent interface. It is designed to:

1. Simulate real monitoring behavior without requiring actual monitoring tools
2. Generate realistic mock events and alerts for testing and development
3. Maintain the same interface as real monitors for seamless integration
4. Provide detailed status information for debugging and monitoring

Mock monitors are particularly useful during development, testing, and in environments
where installing actual monitoring tools is not feasible or desirable.

Each specialized mock monitor (e.g., MockSysmonMonitor, MockETWMonitor) should inherit
from this base class and implement specific event generation logic.

Dependencies:
- None external, uses only standard Python libraries

Usage:
    # Typically not used directly, but through specialized implementations
    class MySpecializedMockMonitor(MockMonitor):
        def __init__(self, config):
            super().__init__(config, "MySpecializedMockMonitor")
            # Additional initialization...

        def _generate_mock_events(self):
            # Implement specialized event generation logic
"""

import os
import time
import random
import logging
import threading
from typing import Dict, List, Any, Optional, Set

class MockMonitor:
    """
    Mock Monitor Base Class.

    This class provides a base implementation for mock monitors.
    """

    def __init__(self, config: Dict[str, Any], name: str = "MockMonitor"):
        """
        Initialize the mock monitor.

        Args:
            config (Dict[str, Any]): Configuration dictionary
            name (str): Monitor name
        """
        self.config = config
        self.name = name
        self.logger = logging.getLogger(f"SBARDS.Mock.{name}")

        # Monitoring thread
        self.monitoring_thread = None
        self.stop_event = threading.Event()
        self.is_running = False

        # Alert manager
        self.alert_manager = None

        # Monitoring interval
        self.monitoring_interval = config.get("monitoring", {}).get("interval_seconds", 5)

        # Mock data
        self.mock_data = {
            "processes": [
                {"name": "explorer.exe", "pid": 1234, "path": "C:\\Windows\\explorer.exe"},
                {"name": "chrome.exe", "pid": 5678, "path": "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"},
                {"name": "notepad.exe", "pid": 9012, "path": "C:\\Windows\\notepad.exe"}
            ],
            "files": [
                {"path": "C:\\Users\\<USER>\\Documents", "filename": "test.txt", "size": 1024, "mtime": time.time()},
                {"path": "C:\\Users\\<USER>\\Pictures", "filename": "image.jpg", "size": 2048, "mtime": time.time()},
                {"path": "C:\\Users\\<USER>\\Downloads", "filename": "setup.exe", "size": 4096, "mtime": time.time()}
            ],
            "network": [
                {"source_ip": "*************", "source_port": 12345, "dest_ip": "*******", "dest_port": 443, "protocol": "TCP"},
                {"source_ip": "*************", "source_port": 54321, "dest_ip": "*******", "dest_port": 80, "protocol": "TCP"}
            ],
            "registry": [
                {"key": "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run", "value": "StartupApp", "data": "C:\\Program Files\\StartupApp\\app.exe"},
                {"key": "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Advanced", "value": "Hidden", "data": "1"}
            ]
        }

        # File change tracking
        self.file_change_history = {}

        self.logger.info(f"{name} initialized")

    def set_alert_manager(self, alert_manager) -> None:
        """
        Set alert manager.

        Args:
            alert_manager: Alert manager
        """
        self.alert_manager = alert_manager

    def start_monitoring(self, stop_event: Optional[threading.Event] = None) -> bool:
        """
        Start monitoring.

        Args:
            stop_event (Optional[threading.Event]): Stop event

        Returns:
            bool: True if successful, False otherwise
        """
        if self.is_running:
            self.logger.warning("Monitoring is already running")
            return True

        self.logger.info(f"Starting {self.name} monitoring")

        if stop_event:
            self.stop_event = stop_event
        else:
            self.stop_event.clear()

        # Start monitoring thread
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self.monitoring_thread.start()

        self.is_running = True
        return True

    def stop_monitoring(self) -> bool:
        """
        Stop monitoring.

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.is_running:
            return True

        self.logger.info(f"Stopping {self.name} monitoring")
        self.stop_event.set()

        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=10.0)

        self.is_running = False
        return True

    def _monitoring_loop(self) -> None:
        """Monitoring loop."""
        while not self.stop_event.is_set():
            try:
                # Generate mock events
                self._generate_mock_events()

                # Wait for next monitoring cycle
                self.stop_event.wait(self.monitoring_interval)

            except Exception as e:
                self.logger.error(f"Error during monitoring: {e}")
                # Wait a bit before retrying
                self.stop_event.wait(1.0)

        self.logger.info(f"{self.name} monitoring stopped")

    def _generate_mock_events(self) -> None:
        """Generate mock events."""
        # Override in subclasses
        pass

    def get_detailed_status(self) -> Dict[str, Any]:
        """
        Get detailed status.

        Returns:
            Dict[str, Any]: Detailed status
        """
        return {
            "name": self.name,
            "is_running": self.is_running,
            "monitoring_interval": self.monitoring_interval,
            "mock_mode": True,
            "events_generated": len(self.file_change_history)
        }
