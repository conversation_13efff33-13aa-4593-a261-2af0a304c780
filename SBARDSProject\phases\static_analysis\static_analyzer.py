"""
SBARDS Static Analysis Layer (طبقة التحليل الثابت)

This module implements comprehensive static analysis including:
- Signature scanning (فحص التوقيعات)
- Permission analysis (تحليل الصلاحيات)
- SHA-256 hash generation (توليد الهاش)
- Hash comparison with databases (مقارنة الهاش مع قواعد البيانات)
- Entropy analysis (فحص الإنتروبيا)
- YARA rules application (تطبيق قواعد YARA)
- VirusTotal integration
"""

import hashlib
import math
import logging
import stat
import requests
import time
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path
import yara
import magic
from collections import Counter


class StaticAnalyzer:
    """
    Comprehensive Static Analysis Engine for SBARDS

    Performs multiple static analysis techniques on files to detect malware.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the Static Analyzer.

        Args:
            config (Dict[str, Any]): Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger("SBARDS.StaticAnalyzer")

        # Analysis configuration
        self.static_config = config.get("static_analysis", {})

        # Initialize components
        self._init_yara_rules()
        self._init_hash_databases()
        self._init_virustotal()
        self._init_magic()

        # Entropy thresholds
        self.entropy_threshold = self.static_config.get("entropy_threshold", 7.5)
        self.high_entropy_threshold = self.static_config.get("high_entropy_threshold", 7.8)

        # File size limits
        self.max_analysis_size = self.static_config.get("max_analysis_size_mb", 50) * 1024 * 1024

        # Known good hashes cache
        self.known_good_hashes = set()
        self.known_bad_hashes = set()
        self._load_hash_databases()

    def _init_yara_rules(self):
        """Initialize YARA rules."""
        try:
            rules_dir = Path(self.static_config.get("yara_rules_dir", "rules"))
            rule_files = []

            if rules_dir.exists():
                rule_files.extend(rules_dir.glob("*.yar"))
                rule_files.extend(rules_dir.glob("*.yara"))

            if rule_files:
                # Compile all rules
                rules_dict = {}
                for i, rule_file in enumerate(rule_files):
                    rules_dict[f"rule_{i}"] = str(rule_file)

                self.yara_rules = yara.compile(filepaths=rules_dict)
                self.logger.info(f"Loaded {len(rule_files)} YARA rule files")
            else:
                self.yara_rules = None
                self.logger.warning("No YARA rules found")

        except Exception as e:
            self.logger.error(f"Error initializing YARA rules: {e}")
            self.yara_rules = None

    def _init_hash_databases(self):
        """Initialize hash databases."""
        self.hash_db_dir = Path(self.static_config.get("hash_db_dir", "hash_databases"))
        self.hash_db_dir.mkdir(exist_ok=True)

        # Local hash database files
        self.known_good_db = self.hash_db_dir / "known_good.txt"
        self.known_bad_db = self.hash_db_dir / "known_bad.txt"

    def _init_virustotal(self):
        """Initialize VirusTotal integration."""
        vt_config = self.static_config.get("virustotal", {})
        self.vt_api_key = vt_config.get("api_key")
        self.vt_enabled = vt_config.get("enabled", False) and self.vt_api_key
        self.vt_rate_limit = vt_config.get("rate_limit_seconds", 15)
        self.vt_last_request = 0

        if self.vt_enabled:
            self.logger.info("VirusTotal integration enabled")
        else:
            self.logger.info("VirusTotal integration disabled")

    def _init_magic(self):
        """Initialize file type detection."""
        try:
            self.magic = magic.Magic(mime=True)
            self.magic_desc = magic.Magic()
        except Exception as e:
            self.logger.warning(f"Could not initialize python-magic: {e}")
            self.magic = None
            self.magic_desc = None

    def _load_hash_databases(self):
        """Load hash databases into memory."""
        # Load known good hashes
        if self.known_good_db.exists():
            try:
                with open(self.known_good_db, 'r') as f:
                    self.known_good_hashes = set(line.strip().lower() for line in f if line.strip())
                self.logger.info(f"Loaded {len(self.known_good_hashes)} known good hashes")
            except Exception as e:
                self.logger.error(f"Error loading known good hashes: {e}")

        # Load known bad hashes
        if self.known_bad_db.exists():
            try:
                with open(self.known_bad_db, 'r') as f:
                    self.known_bad_hashes = set(line.strip().lower() for line in f if line.strip())
                self.logger.info(f"Loaded {len(self.known_bad_hashes)} known bad hashes")
            except Exception as e:
                self.logger.error(f"Error loading known bad hashes: {e}")

    def analyze_file(self, file_path: str, capture_info: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Perform comprehensive static analysis on a file.

        Args:
            file_path (str): Path to the file to analyze
            capture_info (Optional[Dict[str, Any]]): Information from capture layer

        Returns:
            Dict[str, Any]: Complete static analysis results
        """
        try:
            file_path = Path(file_path)

            if not file_path.exists():
                return {"error": f"File not found: {file_path}"}

            # Check file size
            file_size = file_path.stat().st_size
            if file_size > self.max_analysis_size:
                return {"error": f"File too large for analysis: {file_size} bytes"}

            self.logger.info(f"Starting static analysis of: {file_path}")

            # Initialize results
            results = {
                "file_path": str(file_path),
                "timestamp": datetime.now().isoformat(),
                "file_size": file_size,
                "analysis_components": {}
            }

            # 1. Basic file information
            results["file_info"] = self._analyze_file_info(file_path)

            # 2. Hash analysis
            results["hash_analysis"] = self._analyze_hashes(file_path)

            # 3. Entropy analysis
            results["entropy_analysis"] = self._analyze_entropy(file_path)

            # 4. Permission analysis
            results["permission_analysis"] = self._analyze_permissions(file_path)

            # 5. YARA rules analysis
            if self.yara_rules:
                results["yara_analysis"] = self._analyze_yara(file_path)

            # 6. PE analysis (if applicable)
            if file_path.suffix.lower() in ['.exe', '.dll', '.sys']:
                results["pe_analysis"] = self._analyze_pe(file_path)

            # 7. VirusTotal analysis
            if self.vt_enabled:
                results["virustotal_analysis"] = self._analyze_virustotal(results["hash_analysis"]["sha256"])

            # 8. Signature analysis
            results["signature_analysis"] = self._analyze_signatures(file_path)

            # 9. Generate risk assessment
            results["risk_assessment"] = self._assess_risk(results)

            self.logger.info(f"Static analysis completed for: {file_path}")
            return results

        except Exception as e:
            self.logger.error(f"Error during static analysis of {file_path}: {e}")
            return {"error": str(e), "file_path": str(file_path)}

    def _analyze_file_info(self, file_path: Path) -> Dict[str, Any]:
        """Analyze basic file information."""
        stat_info = file_path.stat()

        # Detect MIME type and description
        mime_type = "application/octet-stream"
        file_description = "Unknown"

        if self.magic:
            try:
                mime_type = self.magic.from_file(str(file_path))
            except Exception as e:
                self.logger.warning(f"Could not detect MIME type: {e}")

        if self.magic_desc:
            try:
                file_description = self.magic_desc.from_file(str(file_path))
            except Exception as e:
                self.logger.warning(f"Could not get file description: {e}")

        return {
            "filename": file_path.name,
            "extension": file_path.suffix.lower(),
            "size": stat_info.st_size,
            "mime_type": mime_type,
            "description": file_description,
            "created_time": datetime.fromtimestamp(getattr(stat_info, 'st_birthtime', stat_info.st_ctime)).isoformat(),
            "modified_time": datetime.fromtimestamp(stat_info.st_mtime).isoformat(),
            "accessed_time": datetime.fromtimestamp(stat_info.st_atime).isoformat()
        }

    def _analyze_hashes(self, file_path: Path) -> Dict[str, Any]:
        """Calculate and analyze file hashes."""
        hashes = {}

        # Calculate multiple hashes
        hash_algorithms = {
            'md5': hashlib.md5(),
            'sha1': hashlib.sha1(),
            'sha256': hashlib.sha256(),
            'sha512': hashlib.sha512()
        }

        try:
            with open(file_path, 'rb') as f:
                while chunk := f.read(8192):
                    for hasher in hash_algorithms.values():
                        hasher.update(chunk)

            for name, hasher in hash_algorithms.items():
                hashes[name] = hasher.hexdigest()

            # Check against known databases
            sha256_hash = hashes['sha256'].lower()
            hash_reputation = "unknown"

            if sha256_hash in self.known_good_hashes:
                hash_reputation = "known_good"
            elif sha256_hash in self.known_bad_hashes:
                hash_reputation = "known_bad"

            hashes['reputation'] = hash_reputation

        except Exception as e:
            self.logger.error(f"Error calculating hashes: {e}")
            hashes['error'] = str(e)

        return hashes

    def _analyze_entropy(self, file_path: Path) -> Dict[str, Any]:
        """Calculate file entropy to detect packed/encrypted content."""
        try:
            with open(file_path, 'rb') as f:
                data = f.read()

            if not data:
                return {"entropy": 0.0, "assessment": "empty_file"}

            # Calculate Shannon entropy
            byte_counts = Counter(data)
            entropy = 0.0
            data_len = len(data)

            for count in byte_counts.values():
                probability = count / data_len
                if probability > 0:
                    entropy -= probability * math.log2(probability)

            # Assess entropy level
            if entropy < 1.0:
                assessment = "very_low"
            elif entropy < 4.0:
                assessment = "low"
            elif entropy < 6.0:
                assessment = "medium"
            elif entropy < self.entropy_threshold:
                assessment = "high"
            elif entropy < self.high_entropy_threshold:
                assessment = "very_high"
            else:
                assessment = "suspicious"

            return {
                "entropy": round(entropy, 3),
                "assessment": assessment,
                "is_suspicious": entropy >= self.entropy_threshold,
                "threshold": self.entropy_threshold
            }

        except Exception as e:
            self.logger.error(f"Error calculating entropy: {e}")
            return {"error": str(e)}

    def _analyze_permissions(self, file_path: Path) -> Dict[str, Any]:
        """Analyze file permissions and attributes."""
        try:
            stat_info = file_path.stat()

            # Get permission bits
            permissions = {
                "owner_read": bool(stat_info.st_mode & stat.S_IRUSR),
                "owner_write": bool(stat_info.st_mode & stat.S_IWUSR),
                "owner_execute": bool(stat_info.st_mode & stat.S_IXUSR),
                "group_read": bool(stat_info.st_mode & stat.S_IRGRP),
                "group_write": bool(stat_info.st_mode & stat.S_IWGRP),
                "group_execute": bool(stat_info.st_mode & stat.S_IXGRP),
                "other_read": bool(stat_info.st_mode & stat.S_IROTH),
                "other_write": bool(stat_info.st_mode & stat.S_IWOTH),
                "other_execute": bool(stat_info.st_mode & stat.S_IXOTH)
            }

            # Check for suspicious permissions
            suspicious_flags = []

            # World-writable files are suspicious
            if permissions["other_write"]:
                suspicious_flags.append("world_writable")

            # Executable files with unusual permissions
            if permissions["owner_execute"] and not permissions["owner_read"]:
                suspicious_flags.append("execute_without_read")

            # SUID/SGID bits (Unix-like systems)
            if hasattr(stat, 'S_ISUID') and stat_info.st_mode & stat.S_ISUID:
                suspicious_flags.append("suid_bit_set")

            if hasattr(stat, 'S_ISGID') and stat_info.st_mode & stat.S_ISGID:
                suspicious_flags.append("sgid_bit_set")

            return {
                "permissions": permissions,
                "octal_mode": oct(stat_info.st_mode)[-3:],
                "suspicious_flags": suspicious_flags,
                "is_suspicious": len(suspicious_flags) > 0
            }

        except Exception as e:
            self.logger.error(f"Error analyzing permissions: {e}")
            return {"error": str(e)}

    def _analyze_yara(self, file_path: Path) -> Dict[str, Any]:
        """Run YARA rules against the file."""
        try:
            matches = self.yara_rules.match(str(file_path))

            yara_results = {
                "matches_found": len(matches),
                "matches": []
            }

            for match in matches:
                match_info = {
                    "rule": match.rule,
                    "namespace": match.namespace,
                    "tags": list(match.tags),
                    "meta": dict(match.meta),
                    "strings": []
                }

                # Add string matches
                for string_match in match.strings:
                    match_info["strings"].append({
                        "identifier": string_match.identifier,
                        "instances": len(string_match.instances)
                    })

                yara_results["matches"].append(match_info)

            # Assess threat level based on matches
            threat_level = "clean"
            if matches:
                # Check for high-severity rules
                for match in matches:
                    severity = match.meta.get("severity", "medium").lower()
                    if severity in ["high", "critical"]:
                        threat_level = "malicious"
                        break
                    elif severity == "medium":
                        threat_level = "suspicious"

                if threat_level == "clean":
                    threat_level = "suspicious"

            yara_results["threat_level"] = threat_level

            return yara_results

        except Exception as e:
            self.logger.error(f"Error running YARA analysis: {e}")
            return {"error": str(e)}

    def _analyze_pe(self, file_path: Path) -> Dict[str, Any]:
        """Analyze PE (Portable Executable) files."""
        try:
            import pefile

            pe = pefile.PE(str(file_path))

            pe_info = {
                "is_pe": True,
                "machine": hex(pe.FILE_HEADER.Machine),
                "timestamp": pe.FILE_HEADER.TimeDateStamp,
                "characteristics": hex(pe.FILE_HEADER.Characteristics),
                "sections": [],
                "imports": [],
                "exports": [],
                "suspicious_indicators": []
            }

            # Analyze sections
            for section in pe.sections:
                section_info = {
                    "name": section.Name.decode('utf-8', errors='ignore').strip('\x00'),
                    "virtual_address": hex(section.VirtualAddress),
                    "virtual_size": section.Misc_VirtualSize,
                    "raw_size": section.SizeOfRawData,
                    "characteristics": hex(section.Characteristics),
                    "entropy": section.get_entropy()
                }

                # Check for suspicious section characteristics
                if section.get_entropy() > 7.0:
                    pe_info["suspicious_indicators"].append(f"High entropy section: {section_info['name']}")

                if section.SizeOfRawData == 0 and section.Misc_VirtualSize > 0:
                    pe_info["suspicious_indicators"].append(f"Virtual section: {section_info['name']}")

                pe_info["sections"].append(section_info)

            # Analyze imports
            if hasattr(pe, 'DIRECTORY_ENTRY_IMPORT'):
                for entry in pe.DIRECTORY_ENTRY_IMPORT:
                    dll_name = entry.dll.decode('utf-8', errors='ignore')
                    imports = []

                    for imp in entry.imports:
                        if imp.name:
                            imports.append(imp.name.decode('utf-8', errors='ignore'))

                    pe_info["imports"].append({
                        "dll": dll_name,
                        "functions": imports[:10]  # Limit to first 10 functions
                    })

                    # Check for suspicious imports
                    suspicious_dlls = ['kernel32.dll', 'ntdll.dll', 'advapi32.dll']
                    suspicious_functions = ['CreateProcess', 'VirtualAlloc', 'WriteProcessMemory']

                    if dll_name.lower() in [dll.lower() for dll in suspicious_dlls]:
                        for func in imports:
                            if any(sus_func.lower() in func.lower() for sus_func in suspicious_functions):
                                pe_info["suspicious_indicators"].append(f"Suspicious import: {dll_name}:{func}")

            # Analyze exports
            if hasattr(pe, 'DIRECTORY_ENTRY_EXPORT'):
                for exp in pe.DIRECTORY_ENTRY_EXPORT.symbols:
                    if exp.name:
                        pe_info["exports"].append(exp.name.decode('utf-8', errors='ignore'))

            pe.close()
            return pe_info

        except ImportError:
            return {"error": "pefile library not available"}
        except Exception as e:
            self.logger.error(f"Error analyzing PE file: {e}")
            return {"error": str(e)}

    def _analyze_virustotal(self, file_hash: str) -> Dict[str, Any]:
        """Query VirusTotal for file reputation."""
        if not self.vt_enabled:
            return {"error": "VirusTotal integration disabled"}

        try:
            # Rate limiting
            current_time = time.time()
            if current_time - self.vt_last_request < self.vt_rate_limit:
                time.sleep(self.vt_rate_limit - (current_time - self.vt_last_request))

            headers = {
                'x-apikey': self.vt_api_key
            }

            url = f"https://www.virustotal.com/api/v3/files/{file_hash}"
            response = requests.get(url, headers=headers, timeout=30)

            self.vt_last_request = time.time()

            if response.status_code == 200:
                data = response.json()
                attributes = data.get('data', {}).get('attributes', {})

                stats = attributes.get('last_analysis_stats', {})

                return {
                    "found": True,
                    "malicious": stats.get('malicious', 0),
                    "suspicious": stats.get('suspicious', 0),
                    "undetected": stats.get('undetected', 0),
                    "harmless": stats.get('harmless', 0),
                    "total_engines": sum(stats.values()),
                    "reputation": self._assess_vt_reputation(stats),
                    "scan_date": attributes.get('last_analysis_date')
                }
            elif response.status_code == 404:
                return {"found": False, "message": "File not found in VirusTotal"}
            else:
                return {"error": f"VirusTotal API error: {response.status_code}"}

        except Exception as e:
            self.logger.error(f"Error querying VirusTotal: {e}")
            return {"error": str(e)}

    def _assess_vt_reputation(self, stats: Dict[str, int]) -> str:
        """Assess VirusTotal reputation based on detection stats."""
        total = sum(stats.values())
        if total == 0:
            return "unknown"

        malicious_ratio = stats.get('malicious', 0) / total
        suspicious_ratio = stats.get('suspicious', 0) / total

        if malicious_ratio > 0.1:  # More than 10% detect as malicious
            return "malicious"
        elif malicious_ratio > 0.05 or suspicious_ratio > 0.2:
            return "suspicious"
        else:
            return "clean"

    def _analyze_signatures(self, file_path: Path) -> Dict[str, Any]:
        """Analyze file for known malware signatures."""
        signatures = {
            "known_signatures": [],
            "suspicious_patterns": [],
            "file_type_anomalies": []
        }

        try:
            with open(file_path, 'rb') as f:
                data = f.read(1024 * 1024)  # Read first 1MB

            # Check for common malware signatures
            malware_signatures = [
                (b'This program cannot be run in DOS mode', 'PE_DOS_STUB'),
                (b'MZ', 'PE_HEADER'),
                (b'TVqQAAMAAAAEAAAA', 'BASE64_PE_HEADER'),
                (b'powershell -e ', 'POWERSHELL_ENCODED'),
                (b'cmd.exe /c ', 'CMD_EXECUTION'),
                (b'CreateObject("WScript.Shell")', 'VBSCRIPT_SHELL'),
                (b'eval(', 'JAVASCRIPT_EVAL'),
                (b'\\x', 'HEX_ENCODING')
            ]

            for signature, name in malware_signatures:
                if signature in data:
                    signatures["known_signatures"].append(name)

            # Check for suspicious patterns
            suspicious_patterns = [
                (b'encrypt', 'ENCRYPTION_REFERENCE'),
                (b'ransom', 'RANSOM_REFERENCE'),
                (b'bitcoin', 'CRYPTOCURRENCY_REFERENCE'),
                (b'payload', 'PAYLOAD_REFERENCE'),
                (b'backdoor', 'BACKDOOR_REFERENCE')
            ]

            for pattern, name in suspicious_patterns:
                if pattern.lower() in data.lower():
                    signatures["suspicious_patterns"].append(name)

            return signatures

        except Exception as e:
            self.logger.error(f"Error analyzing signatures: {e}")
            return {"error": str(e)}

    def _assess_risk(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate overall risk assessment based on all analysis results."""
        risk_score = 0
        risk_factors = []

        # Hash reputation
        hash_rep = results.get("hash_analysis", {}).get("reputation", "unknown")
        if hash_rep == "known_bad":
            risk_score += 100
            risk_factors.append("Known malicious hash")
        elif hash_rep == "known_good":
            risk_score -= 20
            risk_factors.append("Known good hash")

        # Entropy analysis
        entropy_analysis = results.get("entropy_analysis", {})
        if entropy_analysis.get("is_suspicious", False):
            risk_score += 30
            risk_factors.append(f"High entropy: {entropy_analysis.get('entropy', 0)}")

        # YARA matches
        yara_analysis = results.get("yara_analysis", {})
        if yara_analysis.get("matches_found", 0) > 0:
            threat_level = yara_analysis.get("threat_level", "clean")
            if threat_level == "malicious":
                risk_score += 80
                risk_factors.append("YARA malicious detection")
            elif threat_level == "suspicious":
                risk_score += 40
                risk_factors.append("YARA suspicious detection")

        # VirusTotal results
        vt_analysis = results.get("virustotal_analysis", {})
        if vt_analysis.get("found", False):
            vt_rep = vt_analysis.get("reputation", "unknown")
            if vt_rep == "malicious":
                risk_score += 70
                risk_factors.append("VirusTotal malicious detection")
            elif vt_rep == "suspicious":
                risk_score += 35
                risk_factors.append("VirusTotal suspicious detection")

        # PE analysis
        pe_analysis = results.get("pe_analysis", {})
        if pe_analysis.get("suspicious_indicators"):
            risk_score += len(pe_analysis["suspicious_indicators"]) * 10
            risk_factors.extend(pe_analysis["suspicious_indicators"])

        # Signature analysis
        sig_analysis = results.get("signature_analysis", {})
        if sig_analysis.get("known_signatures"):
            risk_score += len(sig_analysis["known_signatures"]) * 15
            risk_factors.extend([f"Signature: {sig}" for sig in sig_analysis["known_signatures"]])

        if sig_analysis.get("suspicious_patterns"):
            risk_score += len(sig_analysis["suspicious_patterns"]) * 10
            risk_factors.extend([f"Pattern: {pat}" for pat in sig_analysis["suspicious_patterns"]])

        # Permission analysis
        perm_analysis = results.get("permission_analysis", {})
        if perm_analysis.get("is_suspicious", False):
            risk_score += 20
            risk_factors.extend([f"Permission: {flag}" for flag in perm_analysis.get("suspicious_flags", [])])

        # Determine overall risk level
        if risk_score >= 80:
            risk_level = "HIGH"
            recommendation = "QUARANTINE_IMMEDIATELY"
        elif risk_score >= 50:
            risk_level = "MEDIUM"
            recommendation = "DYNAMIC_ANALYSIS_REQUIRED"
        elif risk_score >= 20:
            risk_level = "LOW"
            recommendation = "MONITOR"
        else:
            risk_level = "MINIMAL"
            recommendation = "ALLOW"

        return {
            "risk_score": min(risk_score, 100),  # Cap at 100
            "risk_level": risk_level,
            "recommendation": recommendation,
            "risk_factors": risk_factors,
            "analysis_summary": {
                "total_components": len([k for k in results.keys() if k.endswith("_analysis")]),
                "components_with_findings": len([k for k in results.keys() if k.endswith("_analysis") and results[k].get("error") is None])
            }
        }


def StaticAnalyze(file_path: str, config: Dict[str, Any],
                 capture_info: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Main static analysis function for backward compatibility.

    Args:
        file_path (str): Path to file to analyze
        config (Dict[str, Any]): Configuration dictionary
        capture_info (Optional[Dict[str, Any]]): Capture layer information

    Returns:
        Dict[str, Any]: Static analysis results
    """
    analyzer = StaticAnalyzer(config)
    return analyzer.analyze_file(file_path, capture_info)