"""
Tests for the orchestrator module.
"""

import os
import json
import tempfile
import unittest
from unittest.mock import patch, MagicMock
from phases.prescanning.orchestrator import Orchestrator

class TestOrchestrator(unittest.TestCase):
    """Tests for the Orchestrator class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary directory
        self.temp_dir = tempfile.TemporaryDirectory()
        
        # Create a temporary config file
        self.config_path = os.path.join(self.temp_dir.name, "config.json")
        self.test_config = {
            "scanner": {
                "target_directory": self.temp_dir.name,
                "recursive": True,
                "max_depth": 3,
                "exclude_dirs": [],
                "exclude_extensions": [],
                "max_file_size_mb": 10
            },
            "rules": {
                "rule_files": []
            },
            "output": {
                "log_directory": os.path.join(self.temp_dir.name, "logs"),
                "output_directory": os.path.join(self.temp_dir.name, "output"),
                "log_level": "info"
            },
            "performance": {
                "threads": 2,
                "batch_size": 10,
                "timeout_seconds": 10,
                "adaptive_threading": True,
                "memory_limit_mb": 512
            }
        }
        
        with open(self.config_path, "w") as f:
            json.dump(self.test_config, f)
        
        # Create test files
        self.test_files_dir = os.path.join(self.temp_dir.name, "test_files")
        os.makedirs(self.test_files_dir, exist_ok=True)
        
        for i in range(5):
            file_path = os.path.join(self.test_files_dir, f"test_file_{i}.txt")
            with open(file_path, "w") as f:
                f.write(f"Test content {i}")
    
    def tearDown(self):
        """Tear down test fixtures."""
        # Remove the temporary directory
        self.temp_dir.cleanup()
    
    def test_prepare_scan(self):
        """Test preparing a scan."""
        # Create orchestrator
        orchestrator = Orchestrator(self.config_path)
        
        # Prepare scan
        scan_id = orchestrator.prepare_scan(
            target_directory=self.test_files_dir,
            recursive=True,
            max_depth=3,
            exclude_dirs=[],
            exclude_extensions=[],
            max_file_size_mb=10
        )
        
        # Check scan ID
        self.assertIsNotNone(scan_id)
        self.assertIsInstance(scan_id, str)
        
        # Check scan
        self.assertIn(scan_id, orchestrator.scans)
        scan = orchestrator.scans[scan_id]
        self.assertEqual(scan["status"], "prepared")
        self.assertEqual(scan["config"]["target_directory"], os.path.abspath(self.test_files_dir))
    
    @patch("phases.prescanning.orchestrator.Orchestrator._run_scan")
    def test_run_scan_async(self, mock_run_scan):
        """Test running a scan asynchronously."""
        # Create orchestrator
        orchestrator = Orchestrator(self.config_path)
        
        # Prepare scan
        scan_id = orchestrator.prepare_scan(
            target_directory=self.test_files_dir,
            recursive=True,
            max_depth=3,
            exclude_dirs=[],
            exclude_extensions=[],
            max_file_size_mb=10
        )
        
        # Run scan asynchronously
        orchestrator.run_scan_async(scan_id)
        
        # Check if _run_scan was called
        mock_run_scan.assert_called_once_with(scan_id)
    
    def test_discover_files(self):
        """Test discovering files."""
        # Create orchestrator
        orchestrator = Orchestrator(self.config_path)
        
        # Discover files
        files = orchestrator._discover_files(
            target_directory=self.test_files_dir,
            recursive=True,
            max_depth=3,
            exclude_dirs=[],
            exclude_extensions=[],
            max_file_size_mb=10
        )
        
        # Check files
        self.assertEqual(len(files), 5)
        for i in range(5):
            self.assertIn(os.path.join(self.test_files_dir, f"test_file_{i}.txt"), files)
    
    def test_cancel_scan(self):
        """Test cancelling a scan."""
        # Create orchestrator
        orchestrator = Orchestrator(self.config_path)
        
        # Prepare scan
        scan_id = orchestrator.prepare_scan(
            target_directory=self.test_files_dir,
            recursive=True,
            max_depth=3,
            exclude_dirs=[],
            exclude_extensions=[],
            max_file_size_mb=10
        )
        
        # Add to active scans
        orchestrator.active_scans.add(scan_id)
        
        # Cancel scan
        result = orchestrator.cancel_scan(scan_id)
        
        # Check result
        self.assertTrue(result)
        self.assertEqual(orchestrator.scans[scan_id]["status"], "cancelled")
        self.assertNotIn(scan_id, orchestrator.active_scans)
    
    def test_get_scan_status(self):
        """Test getting scan status."""
        # Create orchestrator
        orchestrator = Orchestrator(self.config_path)
        
        # Prepare scan
        scan_id = orchestrator.prepare_scan(
            target_directory=self.test_files_dir,
            recursive=True,
            max_depth=3,
            exclude_dirs=[],
            exclude_extensions=[],
            max_file_size_mb=10
        )
        
        # Get scan status
        status = orchestrator.get_scan_status(scan_id)
        
        # Check status
        self.assertIsNotNone(status)
        self.assertEqual(status["scan_id"], scan_id)
        self.assertEqual(status["status"], "prepared")
    
    def test_get_all_scans(self):
        """Test getting all scans."""
        # Create orchestrator
        orchestrator = Orchestrator(self.config_path)
        
        # Prepare scans
        scan_id_1 = orchestrator.prepare_scan(
            target_directory=self.test_files_dir,
            recursive=True,
            max_depth=3,
            exclude_dirs=[],
            exclude_extensions=[],
            max_file_size_mb=10
        )
        
        scan_id_2 = orchestrator.prepare_scan(
            target_directory=self.test_files_dir,
            recursive=False,
            max_depth=1,
            exclude_dirs=[],
            exclude_extensions=[],
            max_file_size_mb=5
        )
        
        # Get all scans
        scans = orchestrator.get_all_scans()
        
        # Check scans
        self.assertEqual(len(scans), 2)
        scan_ids = [scan["scan_id"] for scan in scans]
        self.assertIn(scan_id_1, scan_ids)
        self.assertIn(scan_id_2, scan_ids)

if __name__ == "__main__":
    unittest.main()
