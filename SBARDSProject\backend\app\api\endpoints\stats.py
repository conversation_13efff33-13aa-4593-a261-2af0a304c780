"""
API endpoints for statistics.

This module provides API endpoints for statistics.
"""

from datetime import datetime, timedelta
from typing import List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy import func
from sqlalchemy.orm import Session

from ...core.logging import logger
from ...db.session import get_db
from ...db.models import ScanReport, FileResult
from ...schemas.stats import StatsResponse, ThreatType

# Create router
router = APIRouter()


@router.get(
    "/",
    response_model=StatsResponse,
    summary="Get system statistics",
    description="Get statistics about scans and threats from the system."
)
async def get_stats(db: Session = Depends(get_db)):
    """
    Get statistics about scans and threats.
    
    Returns:
    - **total_scans**: Total number of scans performed
    - **total_files_scanned**: Total number of files scanned
    - **total_threats**: Total number of threats found
    - **recent_scans**: Number of scans in the last 24 hours
    - **recent_threats**: Number of threats found in the last 24 hours
    - **top_threat_types**: Top threat types
    """
    try:
        # Basic stats
        total_scans = db.query(ScanReport).count()
        total_files_scanned = db.query(ScanReport).with_entities(
            func.sum(ScanReport.files_scanned)
        ).scalar() or 0
        total_threats = db.query(ScanReport).with_entities(
            func.sum(ScanReport.threats_found)
        ).scalar() or 0
        
        # Recent stats (last 24 hours)
        yesterday = datetime.now(datetime.timezone.utc) - timedelta(days=1)
        
        recent_scans = db.query(ScanReport).filter(
            ScanReport.timestamp >= yesterday
        ).count()
        
        recent_threats = db.query(ScanReport).filter(
            ScanReport.timestamp >= yesterday
        ).with_entities(
            func.sum(ScanReport.threats_found)
        ).scalar() or 0
        
        # Top threats by type
        threat_types = db.query(
            FileResult.threat_type, 
            func.count(FileResult.id).label('count')
        ).filter(
            FileResult.is_threat == True
        ).group_by(
            FileResult.threat_type
        ).order_by(
            func.count(FileResult.id).desc()
        ).limit(5).all()
        
        top_threats = [
            {"type": t[0] or "Unknown", "count": t[1]} 
            for t in threat_types
        ]
        
        return {
            "total_scans": total_scans,
            "total_files_scanned": total_files_scanned,
            "total_threats": total_threats,
            "recent_scans": recent_scans,
            "recent_threats": recent_threats,
            "top_threat_types": top_threats
        }
    except Exception as e:
        logger.error(f"Error getting statistics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting statistics: {str(e)}"
        )
