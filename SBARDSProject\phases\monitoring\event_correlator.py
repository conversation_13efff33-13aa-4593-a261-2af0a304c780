"""
Event Correlator for SBARDS

This module provides event correlation for the monitoring phase of the SBARDS project.
"""

import time
import logging
import threading
from typing import Dict, List, Any, Optional, Set

class EventCorrelator:
    """
    Event correlator for the monitoring phase.
    
    This class provides mechanisms for:
    1. Correlating events from different sources
    2. Detecting complex attack patterns
    3. Generating high-level alerts
    4. Tracking event sequences
    """
    
    def __init__(self, config: Dict[str, Any], alert_manager):
        """
        Initialize event correlator.
        
        Args:
            config (Dict[str, Any]): Event correlation configuration
            alert_manager: Alert manager
        """
        self.config = config
        self.enabled = config.get("enabled", True)
        self.check_interval_seconds = config.get("check_interval_seconds", 5)
        self.correlation_window_seconds = config.get("correlation_window_seconds", 300)
        self.correlation_rules = config.get("correlation_rules", [])
        
        # Set up logging
        self.logger = logging.getLogger("EventCorrelator")
        
        # Set alert manager
        self.alert_manager = alert_manager
        
        # Initialize event storage
        self.events = []
        self.correlated_events = []
        self.event_count = 0
        
        # Initialize monitoring state
        self.is_running_flag = False
        self.stop_event = threading.Event()
        self.monitoring_thread = None
    
    def start(self) -> bool:
        """
        Start event correlation.
        
        Returns:
            bool: True if started successfully, False otherwise
        """
        if not self.enabled:
            self.logger.warning("Event correlation is not enabled in configuration")
            return False
        
        if self.is_running_flag:
            self.logger.warning("Event correlation is already running")
            return True
        
        self.logger.info("Starting event correlation")
        
        try:
            # Reset stop event
            self.stop_event.clear()
            
            # Start monitoring thread
            self.monitoring_thread = threading.Thread(
                target=self._monitoring_loop,
                daemon=True
            )
            self.monitoring_thread.start()
            
            self.is_running_flag = True
            self.logger.info("Event correlation started")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error starting event correlation: {e}")
            return False
    
    def stop(self) -> bool:
        """
        Stop event correlation.
        
        Returns:
            bool: True if stopped successfully, False otherwise
        """
        if not self.is_running_flag:
            self.logger.warning("Event correlation is not running")
            return True
        
        self.logger.info("Stopping event correlation")
        
        try:
            # Set stop event
            self.stop_event.set()
            
            # Wait for monitoring thread to stop
            if self.monitoring_thread:
                self.monitoring_thread.join(timeout=5.0)
            
            self.is_running_flag = False
            self.logger.info("Event correlation stopped")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error stopping event correlation: {e}")
            return False
    
    def is_running(self) -> bool:
        """
        Check if event correlation is running.
        
        Returns:
            bool: True if running, False otherwise
        """
        return self.is_running_flag
    
    def _monitoring_loop(self) -> None:
        """Event correlation loop."""
        while not self.stop_event.is_set():
            try:
                # Get recent alerts
                recent_alerts = self.alert_manager.get_recent_alerts(100)
                
                # Add alerts to events
                for alert in recent_alerts:
                    self._add_event("alert", alert)
                
                # Correlate events
                self._correlate_events()
                
                # Clean up old events
                self._cleanup_events()
                
                # Wait for next check
                self.stop_event.wait(self.check_interval_seconds)
                
            except Exception as e:
                self.logger.error(f"Error in event correlation loop: {e}")
                # Wait a bit before retrying
                self.stop_event.wait(1.0)
    
    def _add_event(self, event_type: str, event_data: Dict[str, Any]) -> None:
        """
        Add an event.
        
        Args:
            event_type (str): Event type
            event_data (Dict[str, Any]): Event data
        """
        # Create event
        event = {
            "id": str(len(self.events) + 1),
            "timestamp": time.time(),
            "type": event_type,
            "data": event_data
        }
        
        # Add to events
        self.events.append(event)
        self.event_count += 1
    
    def _correlate_events(self) -> None:
        """Correlate events."""
        # Get recent events
        now = time.time()
        window_start = now - self.correlation_window_seconds
        
        # Get events in the correlation window
        recent_events = [
            event for event in self.events
            if event["timestamp"] >= window_start
        ]
        
        # Apply correlation rules
        for rule in self.correlation_rules:
            self._apply_correlation_rule(rule, recent_events)
        
        # Apply built-in correlation rules
        self._detect_ransomware_pattern(recent_events)
        self._detect_data_exfiltration_pattern(recent_events)
        self._detect_brute_force_pattern(recent_events)
    
    def _apply_correlation_rule(self, rule: Dict[str, Any], events: List[Dict[str, Any]]) -> None:
        """
        Apply a correlation rule.
        
        Args:
            rule (Dict[str, Any]): Correlation rule
            events (List[Dict[str, Any]]): Events to correlate
        """
        # This would implement custom correlation rules
        pass
    
    def _detect_ransomware_pattern(self, events: List[Dict[str, Any]]) -> None:
        """
        Detect ransomware pattern.
        
        Args:
            events (List[Dict[str, Any]]): Events to correlate
        """
        # Get file operations
        file_operations = [
            event for event in events
            if event["type"] == "alert" and event["data"].get("source") == "filesystem_monitor"
        ]
        
        # Check for mass file operations
        mass_operations = [
            event for event in file_operations
            if "Mass file" in event["data"].get("message", "")
        ]
        
        # Check for suspicious extensions
        suspicious_extensions = [
            event for event in file_operations
            if "Suspicious file" in event["data"].get("message", "")
        ]
        
        # Check for ransomware pattern
        if mass_operations and suspicious_extensions:
            # Create correlated event
            correlated_event = {
                "id": str(len(self.correlated_events) + 1),
                "timestamp": time.time(),
                "type": "ransomware",
                "events": [event["id"] for event in mass_operations + suspicious_extensions],
                "data": {
                    "mass_operations": len(mass_operations),
                    "suspicious_extensions": len(suspicious_extensions)
                }
            }
            
            # Add to correlated events
            self.correlated_events.append(correlated_event)
            
            # Create alert
            self.alert_manager.add_alert(
                level="critical",
                source="event_correlator",
                message="Possible ransomware activity detected",
                details={
                    "mass_operations": len(mass_operations),
                    "suspicious_extensions": len(suspicious_extensions),
                    "correlated_event_id": correlated_event["id"]
                }
            )
    
    def _detect_data_exfiltration_pattern(self, events: List[Dict[str, Any]]) -> None:
        """
        Detect data exfiltration pattern.
        
        Args:
            events (List[Dict[str, Any]]): Events to correlate
        """
        # Get file operations
        file_operations = [
            event for event in events
            if event["type"] == "alert" and event["data"].get("source") == "filesystem_monitor"
        ]
        
        # Get network connections
        network_connections = [
            event for event in events
            if event["type"] == "alert" and event["data"].get("source") == "network_monitor"
        ]
        
        # Check for mass file operations
        mass_operations = [
            event for event in file_operations
            if "Mass file" in event["data"].get("message", "")
        ]
        
        # Check for connection spikes
        connection_spikes = [
            event for event in network_connections
            if "Connection spike" in event["data"].get("message", "")
        ]
        
        # Check for data exfiltration pattern
        if mass_operations and connection_spikes:
            # Create correlated event
            correlated_event = {
                "id": str(len(self.correlated_events) + 1),
                "timestamp": time.time(),
                "type": "data_exfiltration",
                "events": [event["id"] for event in mass_operations + connection_spikes],
                "data": {
                    "mass_operations": len(mass_operations),
                    "connection_spikes": len(connection_spikes)
                }
            }
            
            # Add to correlated events
            self.correlated_events.append(correlated_event)
            
            # Create alert
            self.alert_manager.add_alert(
                level="critical",
                source="event_correlator",
                message="Possible data exfiltration activity detected",
                details={
                    "mass_operations": len(mass_operations),
                    "connection_spikes": len(connection_spikes),
                    "correlated_event_id": correlated_event["id"]
                }
            )
    
    def _detect_brute_force_pattern(self, events: List[Dict[str, Any]]) -> None:
        """
        Detect brute force pattern.
        
        Args:
            events (List[Dict[str, Any]]): Events to correlate
        """
        # This would implement brute force detection
        pass
    
    def _cleanup_events(self) -> None:
        """Clean up old events."""
        # Get current time
        now = time.time()
        
        # Remove events older than the correlation window
        self.events = [
            event for event in self.events
            if event["timestamp"] >= now - self.correlation_window_seconds
        ]
    
    def get_event_count(self) -> int:
        """
        Get event count.
        
        Returns:
            int: Event count
        """
        return self.event_count
    
    def get_correlated_events(self, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Get correlated events.
        
        Args:
            limit (int, optional): Maximum number of events to return
            
        Returns:
            List[Dict[str, Any]]: Correlated events
        """
        # Sort events by timestamp (newest first)
        sorted_events = sorted(self.correlated_events, key=lambda e: e["timestamp"], reverse=True)
        
        # Return limited number of events
        return sorted_events[:limit]
