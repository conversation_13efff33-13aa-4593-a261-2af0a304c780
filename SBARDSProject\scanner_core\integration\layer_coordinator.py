"""
Layer Coordinator for SBARDS

This module provides coordination between the pre-inspection and monitoring layers.
"""

import os
import time
import logging
import threading
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime

class LayerCoordinator:
    """
    Coordinates activities between the pre-inspection and monitoring layers.
    
    This class provides mechanisms for:
    1. Sharing information between layers
    2. Coordinating responses to threats
    3. Managing shared resources
    4. Optimizing performance across layers
    """
    
    def __init__(self, config: Dict[str, Any], shared_state=None):
        """
        Initialize the layer coordinator.
        
        Args:
            config (Dict[str, Any]): Configuration dictionary
            shared_state: Optional shared state instance
        """
        self.config = config
        self.logger = logging.getLogger("SBARDS.LayerCoordinator")
        
        # Initialize shared state if not provided
        if shared_state is None:
            from .shared_state import SharedState
            self.shared_state = SharedState()
        else:
            self.shared_state = shared_state
            
        # Integration configuration
        self.integration_config = config.get("integration", {})
        self.enabled = self.integration_config.get("enabled", True)
        
        # Layer references
        self.pre_inspection_layer = None
        self.monitoring_layer = None
        
        # Event handlers
        self.event_handlers = {
            "pre_inspection_detection": [],
            "monitoring_alert": [],
            "fast_track_request": [],
            "priority_change": []
        }
        
        # Coordination state
        self.is_running = False
        self.stop_event = threading.Event()
        
        # Performance metrics
        self.metrics = {
            "coordination_calls": 0,
            "fast_track_requests": 0,
            "shared_detections": 0,
            "response_time_ms": []
        }
        
        self.logger.info("Layer Coordinator initialized")
        
    def register_pre_inspection_layer(self, layer):
        """
        Register the pre-inspection layer with the coordinator.
        
        Args:
            layer: Pre-inspection layer instance
        """
        self.pre_inspection_layer = layer
        self.logger.info("Pre-inspection layer registered")
        
    def register_monitoring_layer(self, layer):
        """
        Register the monitoring layer with the coordinator.
        
        Args:
            layer: Monitoring layer instance
        """
        self.monitoring_layer = layer
        self.logger.info("Monitoring layer registered")
        
    def start_coordination(self):
        """Start coordination between layers."""
        if not self.enabled:
            self.logger.warning("Layer coordination is not enabled in configuration")
            return False
            
        if self.is_running:
            self.logger.warning("Layer coordination is already running")
            return True
            
        self.logger.info("Starting layer coordination")
        self.stop_event.clear()
        
        # Start coordination thread
        self.coordination_thread = threading.Thread(
            target=self._coordination_loop,
            daemon=True
        )
        self.coordination_thread.start()
        
        self.is_running = True
        self.logger.info("Layer coordination started")
        return True
        
    def stop_coordination(self):
        """Stop coordination between layers."""
        if not self.is_running:
            self.logger.warning("Layer coordination is not running")
            return True
            
        self.logger.info("Stopping layer coordination")
        self.stop_event.set()
        
        # Wait for coordination thread to complete
        if hasattr(self, 'coordination_thread') and self.coordination_thread.is_alive():
            self.coordination_thread.join(timeout=5.0)
            
        self.is_running = False
        self.logger.info("Layer coordination stopped")
        return True
        
    def _coordination_loop(self):
        """Main coordination loop."""
        check_interval = self.integration_config.get("coordination_interval_seconds", 1.0)
        
        while not self.stop_event.is_set():
            try:
                start_time = time.time()
                
                # Perform coordination activities
                self._sync_detection_data()
                self._check_fast_track_requests()
                self._update_priorities()
                
                # Update metrics
                self.metrics["coordination_calls"] += 1
                elapsed_ms = (time.time() - start_time) * 1000
                self.metrics["response_time_ms"].append(elapsed_ms)
                
                # Keep only the last 100 response times
                if len(self.metrics["response_time_ms"]) > 100:
                    self.metrics["response_time_ms"] = self.metrics["response_time_ms"][-100:]
                
                # Wait for next coordination cycle
                self.stop_event.wait(check_interval)
                
            except Exception as e:
                self.logger.error(f"Error during coordination: {e}")
                # Wait a bit before retrying
                self.stop_event.wait(1.0)
                
    def _sync_detection_data(self):
        """Synchronize detection data between layers."""
        if not self.pre_inspection_layer or not self.monitoring_layer:
            return
            
        # Get new detections from pre-inspection layer
        pre_inspection_detections = self.shared_state.get_new_pre_inspection_detections()
        
        # Share with monitoring layer
        if pre_inspection_detections:
            self.shared_state.mark_pre_inspection_detections_as_processed()
            self._notify_monitoring_layer(pre_inspection_detections)
            self.metrics["shared_detections"] += len(pre_inspection_detections)
            
        # Get new alerts from monitoring layer
        monitoring_alerts = self.shared_state.get_new_monitoring_alerts()
        
        # Share with pre-inspection layer
        if monitoring_alerts:
            self.shared_state.mark_monitoring_alerts_as_processed()
            self._notify_pre_inspection_layer(monitoring_alerts)
            
    def _check_fast_track_requests(self):
        """Check for fast-track requests."""
        fast_track_requests = self.shared_state.get_fast_track_requests()
        
        if fast_track_requests:
            self.metrics["fast_track_requests"] += len(fast_track_requests)
            self._process_fast_track_requests(fast_track_requests)
            
    def _update_priorities(self):
        """Update priorities based on shared information."""
        # This would implement priority adjustment logic based on
        # information from both layers
        pass
        
    def _notify_monitoring_layer(self, detections: List[Dict[str, Any]]):
        """
        Notify the monitoring layer about pre-inspection detections.
        
        Args:
            detections (List[Dict[str, Any]]): List of detection objects
        """
        if not self.monitoring_layer:
            return
            
        # Call event handlers
        for handler in self.event_handlers["pre_inspection_detection"]:
            try:
                handler(detections)
            except Exception as e:
                self.logger.error(f"Error in pre-inspection detection handler: {e}")
                
    def _notify_pre_inspection_layer(self, alerts: List[Dict[str, Any]]):
        """
        Notify the pre-inspection layer about monitoring alerts.
        
        Args:
            alerts (List[Dict[str, Any]]): List of alert objects
        """
        if not self.pre_inspection_layer:
            return
            
        # Call event handlers
        for handler in self.event_handlers["monitoring_alert"]:
            try:
                handler(alerts)
            except Exception as e:
                self.logger.error(f"Error in monitoring alert handler: {e}")
                
    def _process_fast_track_requests(self, requests: List[Dict[str, Any]]):
        """
        Process fast-track requests.
        
        Args:
            requests (List[Dict[str, Any]]): List of fast-track request objects
        """
        # Call event handlers
        for handler in self.event_handlers["fast_track_request"]:
            try:
                handler(requests)
            except Exception as e:
                self.logger.error(f"Error in fast-track request handler: {e}")
                
    def register_event_handler(self, event_type: str, handler: Callable):
        """
        Register an event handler.
        
        Args:
            event_type (str): Type of event to handle
            handler (Callable): Handler function
        """
        if event_type in self.event_handlers:
            self.event_handlers[event_type].append(handler)
            self.logger.debug(f"Registered handler for {event_type} events")
        else:
            self.logger.warning(f"Unknown event type: {event_type}")
            
    def get_metrics(self) -> Dict[str, Any]:
        """
        Get coordination metrics.
        
        Returns:
            Dict[str, Any]: Metrics dictionary
        """
        metrics = self.metrics.copy()
        
        # Calculate average response time
        if metrics["response_time_ms"]:
            metrics["avg_response_time_ms"] = sum(metrics["response_time_ms"]) / len(metrics["response_time_ms"])
        else:
            metrics["avg_response_time_ms"] = 0
            
        return metrics
