"""Create scan reports and file results tables

Revision ID: 8333c76922ae
Revises: 
Create Date: 2025-05-16 21:26:26.519918

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8333c76922ae'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('scan_reports',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('scan_id', sa.String(), nullable=True),
    sa.Column('timestamp', sa.DateTime(), nullable=True),
    sa.Column('scan_path', sa.String(), nullable=True),
    sa.Column('files_scanned', sa.Integer(), nullable=True),
    sa.Column('threats_found', sa.Integer(), nullable=True),
    sa.Column('report_path', sa.String(), nullable=True),
    sa.Column('report_content', sa.Text(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_scan_reports_id'), 'scan_reports', ['id'], unique=False)
    op.create_index(op.f('ix_scan_reports_scan_id'), 'scan_reports', ['scan_id'], unique=True)
    op.create_table('file_results',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('scan_report_id', sa.Integer(), nullable=True),
    sa.Column('file_path', sa.String(), nullable=True),
    sa.Column('file_hash', sa.String(), nullable=True),
    sa.Column('is_threat', sa.Boolean(), nullable=True),
    sa.Column('threat_type', sa.String(), nullable=True),
    sa.Column('virustotal_result', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['scan_report_id'], ['scan_reports.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_file_results_id'), 'file_results', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_file_results_id'), table_name='file_results')
    op.drop_table('file_results')
    op.drop_index(op.f('ix_scan_reports_scan_id'), table_name='scan_reports')
    op.drop_index(op.f('ix_scan_reports_id'), table_name='scan_reports')
    op.drop_table('scan_reports')
    # ### end Alembic commands ###
