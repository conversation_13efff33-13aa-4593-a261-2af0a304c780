"""
Utility Functions for SBARDS

This module provides utility functions used across all phases of the SBARDS project.
"""

import os
import sys
import platform
import subprocess
from typing import List, Dict, Any, Optional, Tuple

def get_platform() -> str:
    """
    Get the current platform.

    Returns:
        str: Platform name (windows, linux, darwin)
    """
    return platform.system().lower()

def is_windows() -> bool:
    """
    Check if the current platform is Windows.

    Returns:
        bool: True if Windows, False otherwise
    """
    return platform.system().lower() == "windows"

def is_linux() -> bool:
    """
    Check if the current platform is Linux.

    Returns:
        bool: True if Linux, False otherwise
    """
    return platform.system().lower() == "linux"

def is_macos() -> bool:
    """
    Check if the current platform is macOS.

    Returns:
        bool: True if macOS, False otherwise
    """
    return platform.system().lower() == "darwin"

def run_command(command: List[str], timeout: int = 30) -> <PERSON>ple[int, str, str]:
    """
    Run a command and return the result.

    Args:
        command (List[str]): Command to run
        timeout (int): Timeout in seconds

    Returns:
        Tuple[int, str, str]: Return code, stdout, stderr
    """
    try:
        process = subprocess.run(
            command,
            capture_output=True,
            text=True,
            timeout=timeout
        )

        return process.returncode, process.stdout, process.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Command timed out"
    except Exception as e:
        return -1, "", str(e)

def check_program_installed(program: str) -> bool:
    """
    Check if a program is installed.

    Args:
        program (str): Program name

    Returns:
        bool: True if installed, False otherwise
    """
    if is_windows():
        # Check using where command
        result = run_command(["where", program])
        return result[0] == 0
    else:
        # Check using which command
        result = run_command(["which", program])
        return result[0] == 0

def get_file_extension(file_path: str) -> str:
    """
    Get the file extension.

    Args:
        file_path (str): Path to the file

    Returns:
        str: File extension
    """
    _, ext = os.path.splitext(file_path)
    return ext.lower()

def calculate_file_hash(file_path: str, algorithm: str = "sha256") -> Optional[str]:
    """
    Calculate file hash.

    Args:
        file_path (str): Path to the file
        algorithm (str): Hash algorithm (md5, sha1, sha256)

    Returns:
        Optional[str]: File hash or None if error
    """
    import hashlib

    algorithms = {
        "md5": hashlib.md5,
        "sha1": hashlib.sha1,
        "sha256": hashlib.sha256
    }

    if algorithm.lower() not in algorithms:
        return None

    try:
        hash_obj = algorithms[algorithm.lower()]()

        with open(file_path, "rb") as f:
            # Read in chunks to handle large files
            for chunk in iter(lambda: f.read(4096), b""):
                hash_obj.update(chunk)

        return hash_obj.hexdigest()
    except Exception:
        return None


def get_file_hash(file_path: str, algorithm: str = "sha256") -> Optional[str]:
    """
    Get file hash (alias for calculate_file_hash).

    Args:
        file_path (str): Path to the file
        algorithm (str): Hash algorithm (md5, sha1, sha256)

    Returns:
        Optional[str]: File hash or None if error
    """
    return calculate_file_hash(file_path, algorithm)


def get_file_info(file_path: str) -> Dict[str, Any]:
    """
    Get comprehensive file information.

    Args:
        file_path (str): Path to the file

    Returns:
        Dict[str, Any]: File information dictionary
    """
    import os
    import stat
    from datetime import datetime

    try:
        if not os.path.exists(file_path):
            return {"error": "File not found"}

        file_stat = os.stat(file_path)

        file_info = {
            "file_path": os.path.abspath(file_path),
            "file_name": os.path.basename(file_path),
            "file_size": file_stat.st_size,
            "file_extension": get_file_extension(file_path),
            "created_time": datetime.fromtimestamp(file_stat.st_ctime).isoformat(),
            "modified_time": datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
            "accessed_time": datetime.fromtimestamp(file_stat.st_atime).isoformat(),
            "permissions": oct(file_stat.st_mode)[-3:],
            "is_executable": bool(file_stat.st_mode & stat.S_IEXEC),
            "is_readable": os.access(file_path, os.R_OK),
            "is_writable": os.access(file_path, os.W_OK),
            "file_hash_md5": get_file_hash(file_path, "md5"),
            "file_hash_sha1": get_file_hash(file_path, "sha1"),
            "file_hash_sha256": get_file_hash(file_path, "sha256"),
        }

        # Add platform-specific information
        if is_windows():
            file_info["platform"] = "windows"
        elif is_linux():
            file_info["platform"] = "linux"
        else:
            file_info["platform"] = "other"

        return file_info

    except Exception as e:
        return {"error": str(e)}


def save_json(data: Dict[str, Any], file_path: str) -> bool:
    """
    Save data to JSON file.

    Args:
        data (Dict[str, Any]): Data to save
        file_path (str): Path to save the file

    Returns:
        bool: True if successful, False otherwise
    """
    import json

    try:
        # Ensure directory exists
        directory = os.path.dirname(file_path)
        if directory:
            os.makedirs(directory, exist_ok=True)

        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, default=str, ensure_ascii=False)

        return True

    except Exception as e:
        print(f"Error saving JSON to {file_path}: {e}")
        return False


def load_json(file_path: str) -> Optional[Dict[str, Any]]:
    """
    Load data from JSON file.

    Args:
        file_path (str): Path to the JSON file

    Returns:
        Optional[Dict[str, Any]]: Loaded data or None if error
    """
    import json

    try:
        if not os.path.exists(file_path):
            return None

        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)

    except Exception as e:
        print(f"Error loading JSON from {file_path}: {e}")
        return None


def format_bytes(bytes_value: int) -> str:
    """
    Format bytes into human readable format.

    Args:
        bytes_value (int): Number of bytes

    Returns:
        str: Formatted string (e.g., "1.5 MB")
    """
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if bytes_value < 1024.0:
            return f"{bytes_value:.1f} {unit}"
        bytes_value /= 1024.0
    return f"{bytes_value:.1f} PB"


def is_safe_path(file_path: str, base_path: str = ".") -> bool:
    """
    Check if a file path is safe (no directory traversal).

    Args:
        file_path (str): File path to check
        base_path (str): Base directory path

    Returns:
        bool: True if safe, False otherwise
    """
    try:
        # Resolve paths
        base_path = os.path.abspath(base_path)
        file_path = os.path.abspath(file_path)

        # Check if file path is within base path
        return file_path.startswith(base_path)

    except Exception:
        return False


def format_time(seconds: float) -> str:
    """
    Format time duration into human readable format.

    Args:
        seconds (float): Time in seconds

    Returns:
        str: Formatted time string (e.g., "1m 30s")
    """
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        remaining_seconds = seconds % 60
        return f"{minutes}m {remaining_seconds:.1f}s"
    else:
        hours = int(seconds // 3600)
        remaining_minutes = int((seconds % 3600) // 60)
        remaining_seconds = seconds % 60
        return f"{hours}h {remaining_minutes}m {remaining_seconds:.1f}s"


def create_temp_file(content: bytes = b"", suffix: str = ".tmp") -> str:
    """
    Create a temporary file with content.

    Args:
        content (bytes): Content to write to the file
        suffix (str): File suffix

    Returns:
        str: Path to the temporary file
    """
    import tempfile

    try:
        with tempfile.NamedTemporaryFile(mode='wb', delete=False, suffix=suffix) as f:
            f.write(content)
            return f.name
    except Exception as e:
        print(f"Error creating temporary file: {e}")
        return ""


def cleanup_temp_file(file_path: str) -> bool:
    """
    Clean up a temporary file.

    Args:
        file_path (str): Path to the temporary file

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            return True
        return False
    except Exception as e:
        print(f"Error cleaning up temporary file {file_path}: {e}")
        return False


def get_memory_usage() -> Dict[str, Any]:
    """
    Get current memory usage information.

    Returns:
        Dict[str, Any]: Memory usage information
    """
    try:
        import psutil

        # Get system memory info
        memory = psutil.virtual_memory()

        # Get current process memory info
        process = psutil.Process()
        process_memory = process.memory_info()

        return {
            "system_memory": {
                "total": memory.total,
                "available": memory.available,
                "used": memory.used,
                "percentage": memory.percent,
                "total_formatted": format_bytes(memory.total),
                "available_formatted": format_bytes(memory.available),
                "used_formatted": format_bytes(memory.used)
            },
            "process_memory": {
                "rss": process_memory.rss,
                "vms": process_memory.vms,
                "rss_formatted": format_bytes(process_memory.rss),
                "vms_formatted": format_bytes(process_memory.vms)
            }
        }

    except ImportError:
        # Fallback if psutil is not available
        return {
            "system_memory": {"error": "psutil not available"},
            "process_memory": {"error": "psutil not available"}
        }
    except Exception as e:
        return {
            "system_memory": {"error": str(e)},
            "process_memory": {"error": str(e)}
        }


def get_cpu_usage() -> Dict[str, Any]:
    """
    Get current CPU usage information.

    Returns:
        Dict[str, Any]: CPU usage information
    """
    try:
        import psutil

        # Get CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        cpu_count_logical = psutil.cpu_count(logical=True)

        # Get current process CPU info
        process = psutil.Process()
        process_cpu = process.cpu_percent()

        return {
            "system_cpu": {
                "usage_percent": cpu_percent,
                "cpu_count": cpu_count,
                "cpu_count_logical": cpu_count_logical
            },
            "process_cpu": {
                "usage_percent": process_cpu
            }
        }

    except ImportError:
        return {
            "system_cpu": {"error": "psutil not available"},
            "process_cpu": {"error": "psutil not available"}
        }
    except Exception as e:
        return {
            "system_cpu": {"error": str(e)},
            "process_cpu": {"error": str(e)}
        }


def validate_environment(config: Dict[str, Any]) -> bool:
    """
    Validate the environment for SBARDS.

    Args:
        config (Dict[str, Any]): Configuration dictionary

    Returns:
        bool: True if environment is valid, False otherwise
    """
    import logging
    logger = logging.getLogger("SBARDS.Utils")

    try:
        # Check Python version
        if sys.version_info < (3, 8):
            logger.error("Python 3.8 or higher is required")
            return False

        # Check required directories
        required_dirs = ["logs", "output", "temp"]
        for directory in required_dirs:
            try:
                os.makedirs(directory, exist_ok=True)
            except Exception as e:
                logger.error(f"Cannot create directory {directory}: {e}")
                return False

        # Check write permissions
        test_file = "test_write_permission.tmp"
        try:
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
        except Exception as e:
            logger.error(f"No write permission in current directory: {e}")
            return False

        logger.info("Environment validation passed")
        return True

    except Exception as e:
        logger.error(f"Environment validation failed: {e}")
        return False


def ensure_directory(directory: str) -> bool:
    """
    Ensure a directory exists.

    Args:
        directory (str): Directory path

    Returns:
        bool: True if directory exists or was created, False otherwise
    """
    try:
        os.makedirs(directory, exist_ok=True)
        return True
    except Exception:
        return False


def get_system_info() -> Dict[str, Any]:
    """
    Get system information.

    Returns:
        Dict[str, Any]: System information dictionary
    """
    return {
        "platform": platform.system(),
        "platform_version": platform.version(),
        "architecture": platform.architecture()[0],
        "processor": platform.processor(),
        "python_version": platform.python_version(),
        "hostname": platform.node()
    }