
# 1. First time:
1- <NAME_EMAIL>:SBARDS/SBARDS.git
2- cd SBARDS // and open th project with the vscode
3- git checkout -b yara // if there is no previous branch if so (git checkout yara)

# 2. Add new changes:

1- git add .
2- git commit -m "message"
3- git push origin yara

# 3. If there is a conflicts // or there is a lots of changes:

1- git checkout main 
2- git pull origin | git fetch // for branches
3- git checkout  yara
4- git merge main  // if there is a conflicts // or there is alots of changes
5- git push origin yara

