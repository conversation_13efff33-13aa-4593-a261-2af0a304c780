"""
Performance Monitoring for SBARDS

This module provides performance monitoring and optimization utilities for the SBARDS project.
"""

import os
import time
import logging
import threading
import functools
import tracemalloc
from typing import Dict, Any, Callable, Optional, List, Tuple, TypeVar, Union

logger = logging.getLogger("SBARDS.Performance")

# Type variables for function decorators
F = TypeVar('F', bound=Callable[..., Any])
T = TypeVar('T')

# Performance metrics
performance_metrics = {
    "function_calls": {},
    "memory_usage": {},
    "execution_time": {}
}

# Thread-local storage for nested timing
thread_local = threading.local()

def timer(func: F) -> F:
    """
    Decorator to measure function execution time.
    
    Args:
        func (Callable): Function to measure
        
    Returns:
        Callable: Wrapped function
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # Initialize thread-local nesting level
        if not hasattr(thread_local, 'nesting_level'):
            thread_local.nesting_level = 0
            
        # Increase nesting level
        thread_local.nesting_level += 1
        indent = '  ' * (thread_local.nesting_level - 1)
        
        # Get function name
        func_name = func.__qualname__
        
        # Initialize metrics if not exists
        if func_name not in performance_metrics["function_calls"]:
            performance_metrics["function_calls"][func_name] = 0
        if func_name not in performance_metrics["execution_time"]:
            performance_metrics["execution_time"][func_name] = {
                "total_time": 0.0,
                "calls": 0,
                "min_time": float('inf'),
                "max_time": 0.0
            }
            
        # Increment call count
        performance_metrics["function_calls"][func_name] += 1
        performance_metrics["execution_time"][func_name]["calls"] += 1
        
        # Measure execution time
        start_time = time.time()
        logger.debug(f"{indent}Starting {func_name}")
        
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            # Calculate execution time
            end_time = time.time()
            execution_time = end_time - start_time
            
            # Update metrics
            performance_metrics["execution_time"][func_name]["total_time"] += execution_time
            performance_metrics["execution_time"][func_name]["min_time"] = min(
                performance_metrics["execution_time"][func_name]["min_time"],
                execution_time
            )
            performance_metrics["execution_time"][func_name]["max_time"] = max(
                performance_metrics["execution_time"][func_name]["max_time"],
                execution_time
            )
            
            # Log execution time
            logger.debug(f"{indent}Finished {func_name} in {execution_time:.6f} seconds")
            
            # Decrease nesting level
            thread_local.nesting_level -= 1
            
    return wrapper  # type: ignore

def memory_profiler(func: F) -> F:
    """
    Decorator to measure function memory usage.
    
    Args:
        func (Callable): Function to measure
        
    Returns:
        Callable: Wrapped function
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # Get function name
        func_name = func.__qualname__
        
        # Initialize metrics if not exists
        if func_name not in performance_metrics["memory_usage"]:
            performance_metrics["memory_usage"][func_name] = {
                "peak_usage": 0,
                "calls": 0
            }
            
        # Increment call count
        performance_metrics["memory_usage"][func_name]["calls"] += 1
        
        # Start memory tracing
        tracemalloc.start()
        
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            # Get memory usage
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            
            # Update metrics
            performance_metrics["memory_usage"][func_name]["peak_usage"] = max(
                performance_metrics["memory_usage"][func_name]["peak_usage"],
                peak
            )
            
            # Log memory usage
            logger.debug(f"{func_name} used {current / 1024:.2f} KB, peak: {peak / 1024:.2f} KB")
            
    return wrapper  # type: ignore

def async_task(func: F) -> F:
    """
    Decorator to run a function asynchronously in a separate thread.
    
    Args:
        func (Callable): Function to run asynchronously
        
    Returns:
        Callable: Wrapped function that returns a thread
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        thread = threading.Thread(target=func, args=args, kwargs=kwargs)
        thread.daemon = True
        thread.start()
        return thread
        
    return wrapper  # type: ignore

def batch_process(batch_size: int = 100):
    """
    Decorator to process items in batches.
    
    Args:
        batch_size (int): Size of each batch
        
    Returns:
        Callable: Decorator function
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(items, *args, **kwargs):
            results = []
            for i in range(0, len(items), batch_size):
                batch = items[i:i + batch_size]
                batch_result = func(batch, *args, **kwargs)
                results.extend(batch_result)
            return results
            
        return wrapper  # type: ignore
        
    return decorator

def get_performance_metrics() -> Dict[str, Any]:
    """
    Get performance metrics.
    
    Returns:
        Dict[str, Any]: Performance metrics
    """
    # Calculate average execution time
    for func_name, metrics in performance_metrics["execution_time"].items():
        if metrics["calls"] > 0:
            metrics["avg_time"] = metrics["total_time"] / metrics["calls"]
            
    return performance_metrics

def reset_performance_metrics() -> None:
    """Reset performance metrics."""
    global performance_metrics
    performance_metrics = {
        "function_calls": {},
        "memory_usage": {},
        "execution_time": {}
    }

def print_performance_report() -> None:
    """Print performance report."""
    metrics = get_performance_metrics()
    
    print("\nPerformance Report:")
    print("-----------------")
    
    # Print execution time metrics
    print("\nExecution Time:")
    for func_name, time_metrics in sorted(
        metrics["execution_time"].items(),
        key=lambda x: x[1]["total_time"],
        reverse=True
    ):
        calls = time_metrics["calls"]
        total_time = time_metrics["total_time"]
        avg_time = total_time / calls if calls > 0 else 0
        min_time = time_metrics["min_time"] if time_metrics["min_time"] != float('inf') else 0
        max_time = time_metrics["max_time"]
        
        print(f"  {func_name}:")
        print(f"    Calls: {calls}")
        print(f"    Total Time: {total_time:.6f} seconds")
        print(f"    Avg Time: {avg_time:.6f} seconds")
        print(f"    Min Time: {min_time:.6f} seconds")
        print(f"    Max Time: {max_time:.6f} seconds")
    
    # Print memory usage metrics
    print("\nMemory Usage:")
    for func_name, memory_metrics in sorted(
        metrics["memory_usage"].items(),
        key=lambda x: x[1]["peak_usage"],
        reverse=True
    ):
        calls = memory_metrics["calls"]
        peak_usage = memory_metrics["peak_usage"]
        
        print(f"  {func_name}:")
        print(f"    Calls: {calls}")
        print(f"    Peak Usage: {peak_usage / 1024:.2f} KB")

class PerformanceMonitor:
    """
    Performance monitoring context manager.
    
    This class provides a context manager for monitoring performance of a block of code.
    """
    
    def __init__(self, name: str):
        """
        Initialize the performance monitor.
        
        Args:
            name (str): Name of the monitored block
        """
        self.name = name
        self.start_time = 0.0
        self.start_memory = (0, 0)
        
    def __enter__(self):
        """Enter the context manager."""
        # Start timing
        self.start_time = time.time()
        
        # Start memory tracing
        tracemalloc.start()
        self.start_memory = tracemalloc.get_traced_memory()
        
        logger.debug(f"Starting performance monitoring: {self.name}")
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit the context manager."""
        # Calculate execution time
        end_time = time.time()
        execution_time = end_time - self.start_time
        
        # Calculate memory usage
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        
        # Calculate memory difference
        memory_diff = current - self.start_memory[0]
        
        # Log performance metrics
        logger.debug(f"Performance monitoring: {self.name}")
        logger.debug(f"  Execution Time: {execution_time:.6f} seconds")
        logger.debug(f"  Memory Usage: {memory_diff / 1024:.2f} KB")
        logger.debug(f"  Peak Memory: {peak / 1024:.2f} KB")
