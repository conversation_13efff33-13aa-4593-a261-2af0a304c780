"""
Test script to simulate ransomware behavior.

This script performs actions that mimic ransomware behavior:
1. Creates multiple files
2. Modifies them rapidly
3. Adds encryption-like extensions
4. Uses suspicious process names

Run this to test the monitoring layer's detection capabilities.
"""

import os
import sys
import time
import random
import string
import threading
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def create_random_content(size=1024):
    """Create random content of specified size."""
    chars = string.ascii_letters + string.digits
    return ''.join(random.choice(chars) for _ in range(size))

def simulate_mass_file_operations(directory, file_count=20):
    """Simulate mass file operations (creation, modification, renaming)."""
    print(f"Creating {file_count} files in {directory}...")
    
    # Create directory if it doesn't exist
    os.makedirs(directory, exist_ok=True)
    
    # Create multiple files rapidly
    files = []
    for i in range(file_count):
        filename = f"test_file_{i}.txt"
        filepath = os.path.join(directory, filename)
        with open(filepath, 'w') as f:
            f.write(create_random_content())
        files.append(filepath)
        print(f"Created: {filepath}")
        # Small delay to make it detectable but still rapid
        time.sleep(0.1)
    
    # Modify files rapidly
    print("Modifying files rapidly...")
    for filepath in files:
        with open(filepath, 'a') as f:
            f.write(create_random_content(512))
        print(f"Modified: {filepath}")
        time.sleep(0.1)
    
    # Rename files with suspicious extensions
    print("Renaming files with suspicious extensions...")
    renamed_files = []
    for filepath in files:
        new_filepath = f"{filepath}.encrypted"
        os.rename(filepath, new_filepath)
        renamed_files.append(new_filepath)
        print(f"Renamed: {filepath} -> {new_filepath}")
        time.sleep(0.1)
    
    return renamed_files

def simulate_high_entropy_content(directory, file_count=5):
    """Create files with high entropy content (simulating encryption)."""
    print(f"Creating {file_count} high-entropy files in {directory}...")
    
    # Create directory if it doesn't exist
    os.makedirs(directory, exist_ok=True)
    
    # Create files with high entropy content
    files = []
    for i in range(file_count):
        filename = f"high_entropy_{i}.bin"
        filepath = os.path.join(directory, filename)
        
        # Create high entropy content (random bytes)
        with open(filepath, 'wb') as f:
            f.write(os.urandom(4096))  # 4KB of random bytes
        
        files.append(filepath)
        print(f"Created high entropy file: {filepath}")
        time.sleep(0.2)
    
    return files

def create_ransom_note(directory):
    """Create a simulated ransom note."""
    print("Creating ransom note...")
    
    ransom_content = """
    YOUR FILES HAVE BEEN ENCRYPTED!
    
    All your important documents, photos, and data have been encrypted with military-grade encryption.
    
    To decrypt your files, you need to pay 1 Bitcoin to the following address:
    1A2B3C4D5E6F7G8H9I0J1K2L3M4N5O6P7Q8R9S0T
    
    After payment, contact <NAME_EMAIL> with your payment proof.
    
    DO NOT attempt to decrypt the files yourself or they will be permanently lost.
    
    You have 72 hours to pay or all your files will be deleted.
    """
    
    filepath = os.path.join(directory, "RANSOM_NOTE.txt")
    with open(filepath, 'w') as f:
        f.write(ransom_content)
    
    print(f"Created ransom note: {filepath}")
    return filepath

def main():
    """Main function to run the ransomware simulation."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    test_dir = os.path.join("samples", f"ransomware_test_{timestamp}")
    
    print(f"Starting ransomware behavior simulation in {test_dir}")
    print("This will trigger alerts in the monitoring system")
    print("=" * 60)
    
    # Simulate mass file operations
    renamed_files = simulate_mass_file_operations(test_dir)
    
    # Simulate high entropy content
    high_entropy_files = simulate_high_entropy_content(test_dir)
    
    # Create ransom note
    ransom_note = create_ransom_note(test_dir)
    
    print("=" * 60)
    print(f"Ransomware simulation complete. Check the monitoring alerts.")
    print(f"Test directory: {test_dir}")
    print(f"Total files created: {len(renamed_files) + len(high_entropy_files) + 1}")

if __name__ == "__main__":
    main()
