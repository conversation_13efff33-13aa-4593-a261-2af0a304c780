"""
Unified Logging for SBARDS

This module provides unified logging capabilities across all SBARDS components.
"""

import os
import sys
import time
import json
import logging
import threading
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging.handlers

class UnifiedLogger:
    """
    Provides unified logging across all SBARDS components.
    
    This class provides:
    1. Centralized logging configuration
    2. Consistent log formatting
    3. Log aggregation from different components
    4. Log rotation and management
    """
    
    # Singleton instance
    _instance = None
    _lock = threading.RLock()
    
    @classmethod
    def get_instance(cls, config: Optional[Dict[str, Any]] = None):
        """
        Get the singleton instance of the unified logger.
        
        Args:
            config (Optional[Dict[str, Any]]): Configuration dictionary
            
        Returns:
            UnifiedLogger: Singleton instance
        """
        with cls._lock:
            if cls._instance is None:
                cls._instance = cls(config)
            elif config is not None:
                # Update configuration if provided
                cls._instance.update_config(config)
            return cls._instance
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the unified logger.
        
        Args:
            config (Optional[Dict[str, Any]]): Configuration dictionary
        """
        self.config = config or {}
        
        # Get logging configuration
        self.logging_config = self.config.get("integration", {}).get("unified_logging", {})
        self.enabled = self.logging_config.get("enabled", True)
        
        # Configure root logger
        self.root_logger = logging.getLogger()
        
        # Clear existing handlers
        for handler in self.root_logger.handlers[:]:
            self.root_logger.removeHandler(handler)
            
        # Set log level
        log_level_str = self.logging_config.get("log_level", "info").upper()
        log_level = getattr(logging, log_level_str, logging.INFO)
        self.root_logger.setLevel(log_level)
        
        # Create formatters
        self.console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        self.file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
        )
        
        self.json_formatter = JsonFormatter()
        
        # Add console handler
        self.console_handler = logging.StreamHandler(sys.stdout)
        self.console_handler.setFormatter(self.console_formatter)
        self.root_logger.addHandler(self.console_handler)
        
        # Add file handler if enabled
        self.file_handler = None
        if self.enabled:
            log_file = self.logging_config.get("log_file", "logs/unified_sbards.log")
            
            # Create directory if it doesn't exist
            log_dir = os.path.dirname(log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)
                
            # Create rotating file handler
            self.file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=10 * 1024 * 1024,  # 10 MB
                backupCount=5
            )
            self.file_handler.setFormatter(self.file_formatter)
            self.root_logger.addHandler(self.file_handler)
            
        # Add JSON file handler if enabled
        self.json_handler = None
        if self.logging_config.get("enable_json_logging", False):
            json_log_file = self.logging_config.get("json_log_file", "logs/sbards.json.log")
            
            # Create directory if it doesn't exist
            log_dir = os.path.dirname(json_log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)
                
            # Create rotating file handler
            self.json_handler = logging.handlers.RotatingFileHandler(
                json_log_file,
                maxBytes=10 * 1024 * 1024,  # 10 MB
                backupCount=5
            )
            self.json_handler.setFormatter(self.json_formatter)
            self.root_logger.addHandler(self.json_handler)
            
        # Create logger for this class
        self.logger = logging.getLogger("SBARDS.UnifiedLogger")
        self.logger.info("Unified Logger initialized")
        
    def update_config(self, config: Dict[str, Any]):
        """
        Update logger configuration.
        
        Args:
            config (Dict[str, Any]): New configuration dictionary
        """
        self.config = config
        
        # Get logging configuration
        self.logging_config = self.config.get("integration", {}).get("unified_logging", {})
        self.enabled = self.logging_config.get("enabled", True)
        
        # Update log level
        log_level_str = self.logging_config.get("log_level", "info").upper()
        log_level = getattr(logging, log_level_str, logging.INFO)
        self.root_logger.setLevel(log_level)
        
        # Update file handler if enabled
        if self.enabled:
            log_file = self.logging_config.get("log_file", "logs/unified_sbards.log")
            
            # Remove existing file handler
            if self.file_handler:
                self.root_logger.removeHandler(self.file_handler)
                self.file_handler.close()
                
            # Create directory if it doesn't exist
            log_dir = os.path.dirname(log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)
                
            # Create rotating file handler
            self.file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=10 * 1024 * 1024,  # 10 MB
                backupCount=5
            )
            self.file_handler.setFormatter(self.file_formatter)
            self.root_logger.addHandler(self.file_handler)
            
        # Update JSON file handler if enabled
        if self.logging_config.get("enable_json_logging", False):
            json_log_file = self.logging_config.get("json_log_file", "logs/sbards.json.log")
            
            # Remove existing JSON handler
            if self.json_handler:
                self.root_logger.removeHandler(self.json_handler)
                self.json_handler.close()
                
            # Create directory if it doesn't exist
            log_dir = os.path.dirname(json_log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)
                
            # Create rotating file handler
            self.json_handler = logging.handlers.RotatingFileHandler(
                json_log_file,
                maxBytes=10 * 1024 * 1024,  # 10 MB
                backupCount=5
            )
            self.json_handler.setFormatter(self.json_formatter)
            self.root_logger.addHandler(self.json_handler)
            
        self.logger.info("Unified Logger configuration updated")
        
    def get_logger(self, name: str) -> logging.Logger:
        """
        Get a logger with the specified name.
        
        Args:
            name (str): Logger name
            
        Returns:
            logging.Logger: Logger instance
        """
        return logging.getLogger(name)
        
class JsonFormatter(logging.Formatter):
    """
    JSON formatter for logging.
    """
    
    def format(self, record):
        """
        Format the log record as JSON.
        
        Args:
            record: Log record
            
        Returns:
            str: JSON-formatted log record
        """
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "name": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "filename": record.filename,
            "lineno": record.lineno,
            "funcName": record.funcName,
            "thread": record.thread,
            "threadName": record.threadName,
            "process": record.process
        }
        
        # Add exception info if available
        if record.exc_info:
            log_data["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": self.formatException(record.exc_info)
            }
            
        # Add extra attributes
        if hasattr(record, "extra"):
            log_data["extra"] = record.extra
            
        return json.dumps(log_data)

def configure_logging(config: Dict[str, Any]):
    """
    Configure unified logging for SBARDS.
    
    Args:
        config (Dict[str, Any]): Configuration dictionary
        
    Returns:
        UnifiedLogger: Unified logger instance
    """
    return UnifiedLogger.get_instance(config)
