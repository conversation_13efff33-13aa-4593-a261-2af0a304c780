"""
API endpoints for scan reports.

This module provides API endpoints for scan reports.
"""

from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query, Path, status
from sqlalchemy.orm import Session

from ...core.logging import logger
from ...db.session import get_db
from ...schemas.reports import ScanReportCreate, ScanReportResponse, ScanReportList
from ...services import reports
from ...services.virustotal import check_file_hash

# Create router
router = APIRouter()


@router.post(
    "/",
    response_model=ScanReportResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new scan report",
    description="Create a new scan report and store it in the database. For threats, it will automatically check VirusTotal in the background."
)
async def create_scan_report(
    report: ScanReportCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Create a new scan report.
    
    - **scan_id**: Unique identifier for the scan
    - **scan_path**: Path that was scanned
    - **files_scanned**: Number of files scanned
    - **threats_found**: Number of threats found
    - **report_path**: Path to the HTML report file
    - **report_content**: Content of the HTML report
    - **file_results**: List of file results with threat information
    
    For any files marked as threats, the system will automatically check them against VirusTotal
    in the background and update the database with the results.
    """
    try:
        # Create the scan report
        db_report = reports.create_scan_report(db, report)
        
        # Check VirusTotal in background for threats
        for file_result in db_report.file_results:
            if file_result.is_threat and file_result.file_hash:
                logger.info(f"Scheduling VirusTotal check for file: {file_result.file_path}")
                background_tasks.add_task(check_and_update_virustotal, file_result.id)
        
        return db_report
    except Exception as e:
        logger.error(f"Error creating scan report: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating scan report: {str(e)}"
        )


@router.get(
    "/",
    response_model=List[ScanReportResponse],
    summary="Get all scan reports",
    description="Retrieve a list of all scan reports with pagination support."
)
async def get_scan_reports(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
    include_content: bool = Query(False, description="Whether to include the full HTML report content"),
    db: Session = Depends(get_db)
):
    """
    Get all scan reports with pagination.
    
    - **skip**: Number of records to skip (for pagination)
    - **limit**: Maximum number of records to return
    - **include_content**: Whether to include the full HTML report content (default: false)
    
    Returns a list of scan reports ordered by timestamp (newest first).
    """
    try:
        return reports.get_scan_reports(db, skip, limit, include_content)
    except Exception as e:
        logger.error(f"Error retrieving scan reports: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving scan reports: {str(e)}"
        )


@router.get(
    "/{scan_id}",
    response_model=ScanReportResponse,
    summary="Get a specific scan report",
    description="Retrieve a specific scan report by its ID."
)
async def get_scan_report(
    scan_id: str = Path(..., description="The unique identifier of the scan report"),
    include_content: bool = Query(True, description="Whether to include the full HTML report content"),
    db: Session = Depends(get_db)
):
    """
    Get a specific scan report by ID.
    
    - **scan_id**: The unique identifier of the scan report
    - **include_content**: Whether to include the full HTML report content (default: true)
    
    Returns the scan report with the specified ID, including all file results.
    """
    try:
        db_report = reports.get_scan_report(db, scan_id, include_content)
        if db_report is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Scan report with ID {scan_id} not found"
            )
        return db_report
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving scan report {scan_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving scan report: {str(e)}"
        )


async def check_and_update_virustotal(file_result_id: int):
    """
    Check VirusTotal for a file result and update the database.
    
    Args:
        file_result_id (int): File result ID.
    """
    db = next(get_db())
    try:
        # Get the file result
        file_result = db.query(reports.FileResult).filter(reports.FileResult.id == file_result_id).first()
        if file_result:
            # Check VirusTotal
            vt_result = await check_file_hash(file_result.file_hash)
            
            # Update the file result
            reports.update_file_result_virustotal(db, file_result_id, vt_result)
    except Exception as e:
        logger.error(f"Error updating VirusTotal result for file result {file_result_id}: {e}")
    finally:
        db.close()
