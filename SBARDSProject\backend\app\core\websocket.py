"""
WebSocket manager for the SBARDS Backend API.

This module provides a WebSocket manager for the SBARDS Backend API.
"""

import asyncio
import json
from typing import Dict, List, Any, Optional

from fastapi import WebSocket

from .logging import logger


class WebSocketManager:
    """WebSocket connection manager."""
    
    def __init__(self):
        """Initialize the WebSocket manager."""
        self.active_connections: Dict[str, List[WebSocket]] = {}
        self._lock = asyncio.Lock()
    
    async def connect(self, websocket: WebSocket, scan_id: str) -> None:
        """
        Connect a WebSocket client.
        
        Args:
            websocket (WebSocket): WebSocket connection.
            scan_id (str): Scan ID.
        """
        await websocket.accept()
        async with self._lock:
            if scan_id not in self.active_connections:
                self.active_connections[scan_id] = []
            self.active_connections[scan_id].append(websocket)
        
        logger.debug(f"WebSocket client connected for scan {scan_id}")
    
    async def disconnect(self, websocket: WebSocket, scan_id: str) -> None:
        """
        Disconnect a WebSocket client.
        
        Args:
            websocket (WebSocket): WebSocket connection.
            scan_id (str): Scan ID.
        """
        async with self._lock:
            if scan_id in self.active_connections:
                if websocket in self.active_connections[scan_id]:
                    self.active_connections[scan_id].remove(websocket)
                if not self.active_connections[scan_id]:
                    del self.active_connections[scan_id]
        
        logger.debug(f"WebSocket client disconnected from scan {scan_id}")
    
    async def send_message(self, message: str, scan_id: str) -> None:
        """
        Send a message to all clients for a specific scan.
        
        Args:
            message (str): Message to send.
            scan_id (str): Scan ID.
        """
        if not scan_id:
            return
        
        async with self._lock:
            if scan_id in self.active_connections:
                disconnected_websockets = []
                for websocket in self.active_connections[scan_id]:
                    try:
                        await websocket.send_text(message)
                    except Exception as e:
                        logger.error(f"Error sending message to WebSocket client: {e}")
                        disconnected_websockets.append(websocket)
                
                # Remove disconnected websockets
                for websocket in disconnected_websockets:
                    if websocket in self.active_connections[scan_id]:
                        self.active_connections[scan_id].remove(websocket)
                
                # Clean up empty scan connections
                if not self.active_connections[scan_id]:
                    del self.active_connections[scan_id]
    
    async def broadcast(self, message: str) -> None:
        """
        Broadcast a message to all clients.
        
        Args:
            message (str): Message to send.
        """
        async with self._lock:
            for scan_id in list(self.active_connections.keys()):
                await self.send_message(message, scan_id)
    
    async def send_json(self, data: Dict[str, Any], scan_id: str) -> None:
        """
        Send JSON data to all clients for a specific scan.
        
        Args:
            data (Dict[str, Any]): JSON data to send.
            scan_id (str): Scan ID.
        """
        try:
            message = json.dumps(data)
            await self.send_message(message, scan_id)
        except Exception as e:
            logger.error(f"Error sending JSON data to WebSocket clients: {e}")
    
    def get_connected_clients_count(self, scan_id: Optional[str] = None) -> int:
        """
        Get the number of connected clients.
        
        Args:
            scan_id (Optional[str]): Scan ID. If None, returns the total number of clients.
            
        Returns:
            int: Number of connected clients.
        """
        if scan_id:
            return len(self.active_connections.get(scan_id, []))
        
        return sum(len(clients) for clients in self.active_connections.values())


# Create a global WebSocket manager instance
websocket_manager = WebSocketManager()
