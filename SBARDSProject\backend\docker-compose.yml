version: '3'

services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./app:/app
      - ./data:/data
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    env_file:
      - .env
    environment:
      - DATABASE_URL=sqlite:////data/sbards.db
      - LOG_FILE=/app/logs/backend_api.log
    restart: unless-stopped
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
