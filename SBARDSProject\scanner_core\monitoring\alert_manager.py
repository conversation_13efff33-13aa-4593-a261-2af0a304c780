"""
Alert Manager for SBARDS

This module provides alert management capabilities for the monitoring layer.
"""

import os
import time
import json
import logging
import threading
import queue
import platform
import subprocess
from typing import Dict, List, Any, Optional
from datetime import datetime

class AlertManager:
    """
    Manages alerts generated by monitoring components.

    This class handles alert generation, deduplication, throttling, and delivery.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the alert manager.

        Args:
            config (Dict[str, Any]): Alert configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger("SBARDS.AlertManager")

        # Alert configuration
        self.log_alerts = config.get("log_alerts", True)
        self.alert_level = config.get("alert_level", "warning").lower()
        self.max_alerts_per_minute = config.get("max_alerts_per_minute", 10)

        # Alert queue and processing thread
        self.alert_queue = queue.Queue()
        self.processing_thread = None
        self.stop_event = threading.Event()

        # Alert history for deduplication
        self.alert_history = {}
        self.alert_counts = {}
        self.last_alert_time = time.time()

        # Alert storage
        self.alerts_dir = os.path.join("output", "alerts")
        os.makedirs(self.alerts_dir, exist_ok=True)

        self.logger.info("Alert Manager initialized")

    def start(self):
        """Start the alert processing thread."""
        if self.processing_thread is not None and self.processing_thread.is_alive():
            self.logger.warning("Alert processing thread is already running")
            return

        self.stop_event.clear()
        self.processing_thread = threading.Thread(
            target=self._process_alerts,
            daemon=True
        )
        self.processing_thread.start()
        self.logger.info("Alert processing thread started")

    def stop(self):
        """Stop the alert processing thread."""
        if self.processing_thread is None or not self.processing_thread.is_alive():
            return

        self.stop_event.set()
        self.processing_thread.join(timeout=5.0)
        self.logger.info("Alert processing thread stopped")

    def add_alert(self, source: str, alert_type: str, message: str,
                  severity: str = "warning", details: Optional[Dict[str, Any]] = None):
        """
        Add a new alert to the processing queue.

        Args:
            source (str): Source of the alert (component name)
            alert_type (str): Type of alert
            message (str): Alert message
            severity (str): Alert severity (info, warning, error, critical)
            details (Optional[Dict[str, Any]]): Additional alert details
        """
        # Create alert object
        alert = {
            "timestamp": time.time(),
            "datetime": datetime.now().isoformat(),
            "source": source,
            "type": alert_type,
            "message": message,
            "severity": severity,
            "details": details or {}
        }

        # Add to queue for processing
        self.alert_queue.put(alert)

    def _process_alerts(self):
        """Process alerts from the queue."""
        while not self.stop_event.is_set():
            try:
                # Get alert with timeout to allow checking stop_event
                try:
                    alert = self.alert_queue.get(timeout=1.0)
                except queue.Empty:
                    continue

                # Check for alert throttling
                current_time = time.time()
                minute_window = int(current_time / 60)

                # Reset counter for new minute
                if minute_window not in self.alert_counts:
                    self.alert_counts = {minute_window: 0}

                # Check if we've exceeded the maximum alerts per minute
                if self.alert_counts[minute_window] >= self.max_alerts_per_minute:
                    # Log that we're throttling alerts
                    if current_time - self.last_alert_time > 60:  # Only log once per minute
                        self.logger.warning(f"Alert throttling active: exceeded {self.max_alerts_per_minute} alerts per minute")
                        self.last_alert_time = current_time
                    continue

                # Increment alert count
                self.alert_counts[minute_window] += 1

                # Process the alert
                self._handle_alert(alert)

                # Mark task as done
                self.alert_queue.task_done()

            except Exception as e:
                self.logger.error(f"Error processing alert: {e}")

    def _handle_alert(self, alert: Dict[str, Any]):
        """
        Handle a single alert.

        Args:
            alert (Dict[str, Any]): Alert object
        """
        # Add a unique ID to the alert
        alert["id"] = f"{int(time.time())}_{alert['source']}_{alert['severity']}"

        # Log the alert if enabled
        if self.log_alerts:
            log_method = getattr(self.logger, alert["severity"], self.logger.warning)
            log_method(f"ALERT: [{alert['source']}] {alert['message']}")

        # Save alert to file
        self._save_alert(alert)

        # Check if we should trigger automatic response
        if hasattr(self, "_response_manager") and self._response_manager:
            # Get alert actions from config
            alert_actions = self.config.get("alert_actions", {})
            severity = alert["severity"]

            # Check if this severity level should trigger a response
            if severity in alert_actions and "respond" in alert_actions[severity]:
                try:
                    response = self._response_manager.respond_to_alert(alert)
                    self.logger.info(f"Automatic response triggered: {response.get('status')}")
                except Exception as e:
                    self.logger.error(f"Error triggering automatic response: {e}")

        # Implement desktop notifications if enabled
        if self.config.get("enable_desktop_notifications", False) and alert["severity"] in ["critical", "warning"]:
            try:
                self._send_desktop_notification(alert)
            except Exception as e:
                self.logger.error(f"Error sending desktop notification: {e}")

    def _send_desktop_notification(self, alert: Dict[str, Any]):
        """
        Send a desktop notification for an alert.

        Args:
            alert (Dict[str, Any]): Alert object
        """
        title = f"SBARDS Alert: {alert['severity'].upper()}"
        message = f"{alert['message']} [{alert['source']}]"

        try:
            if platform.system() == "Windows":
                # Use PowerShell to show a notification on Windows
                ps_script = f'powershell -Command "& {{Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.MessageBox]::Show(\'{message}\', \'{title}\', \'OK\', \'Warning\')}}"'
                subprocess.run(ps_script, shell=True, check=True)
            else:
                # Use notify-send on Linux
                subprocess.run(["notify-send", title, message], check=True)

        except Exception as e:
            self.logger.error(f"Error sending desktop notification: {e}")

    def _save_alert(self, alert: Dict[str, Any]):
        """
        Save alert to file.

        Args:
            alert (Dict[str, Any]): Alert object
        """
        try:
            # Create filename based on date
            date_str = datetime.fromtimestamp(alert["timestamp"]).strftime("%Y%m%d")
            alerts_file = os.path.join(self.alerts_dir, f"alerts_{date_str}.json")

            # Append to file
            with open(alerts_file, "a") as f:
                f.write(json.dumps(alert) + "\n")

        except Exception as e:
            self.logger.error(f"Error saving alert to file: {e}")

    def get_recent_alerts(self, count: int = 10) -> List[Dict[str, Any]]:
        """
        Get recent alerts.

        Args:
            count (int): Number of alerts to retrieve

        Returns:
            List[Dict[str, Any]]: List of recent alerts
        """
        alerts = []
        try:
            # Get most recent alerts file
            date_str = datetime.now().strftime("%Y%m%d")
            alerts_file = os.path.join(self.alerts_dir, f"alerts_{date_str}.json")

            if os.path.exists(alerts_file):
                with open(alerts_file, "r") as f:
                    lines = f.readlines()
                    # Get last 'count' lines
                    for line in lines[-count:]:
                        alerts.append(json.loads(line))

        except Exception as e:
            self.logger.error(f"Error retrieving recent alerts: {e}")

        return alerts
