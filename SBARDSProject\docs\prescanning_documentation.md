# SBARDS Project: Pre-scanning Phase Documentation

## Table of Contents
1. [Introduction](#introduction)
2. [Project Overview](#project-overview)
3. [Pre-scanning Phase Architecture](#pre-scanning-phase-architecture)
4. [Workflow](#workflow)
5. [Component Details](#component-details)
6. [Configuration](#configuration)
7. [Best Practices](#best-practices)
8. [Troubleshooting](#troubleshooting)
9. [Cross-Platform Compatibility](#cross-platform-compatibility)

## Introduction

The SBARDS (Security-Based Automated Recursive Directory Scanner) Project is designed to scan directories recursively for potential security threats using YARA rules. The pre-scanning phase is the initial phase of the scanning process that identifies potential threats before a more detailed analysis is performed.

This document provides comprehensive information about the pre-scanning phase, including its architecture, workflow, components, and best practices for development and maintenance.

## Project Overview

The SBARDS project consists of multiple phases:

1. **Pre-scanning Phase**: Initial scan of files using YARA rules to identify potential threats
2. **Deep Analysis Phase**: Detailed analysis of files flagged during pre-scanning
3. **Reporting Phase**: Generation of comprehensive reports about identified threats

This documentation focuses on the pre-scanning phase, which is responsible for:
- Recursively scanning directories for files
- Applying YARA rules to identify potential threats
- Generating initial reports for further analysis

## Pre-scanning Phase Architecture

The pre-scanning phase follows a modular architecture with the following key components:

```
SBARDS Project
├── config.json                 # Configuration file
├── run_prescanning.py          # Main entry point for pre-scanning
├── rules/                      # YARA rules directory
│   ├── custom_rules.yar        # Custom YARA rules
│   └── malware_rules.yar       # Malware detection rules
├── scanner_core/               # Core scanning components
│   ├── cpp/                    # C++ scanner components
│   │   ├── mock_scanner.cpp    # C++ scanner implementation
│   │   └── mock_scanner.py     # Python wrapper for C++ scanner
│   ├── python/                 # Python scanner components
│   │   ├── orchestrator.py     # Main orchestrator for scanning
│   │   └── scanner.py          # Python scanner implementation
│   └── utils/                  # Utility functions
│       ├── config_loader.py    # Configuration loader
│       └── logger.py           # Logging utilities
├── output/                     # Output directory for reports
└── logs/                       # Log directory
```

## Workflow

The pre-scanning phase follows this workflow:

1. **Initialization**:
   - Load configuration from `config.json`
   - Set up logging
   - Initialize scanners

2. **File Discovery**:
   - Recursively scan target directories
   - Filter files based on configuration (size, extension, etc.)
   - Create a list of files to scan

3. **Scanning**:
   - Load YARA rules
   - Apply rules to each file
   - Collect results

4. **Reporting**:
   - Generate JSON, CSV, and HTML reports
   - Log scan results
   - Prepare data for deep analysis phase

## Component Details

### 1. Orchestrator (`scanner_core/python/orchestrator.py`)

The orchestrator is the central component that coordinates the scanning process. It:
- Initializes the scanning environment
- Manages file discovery
- Coordinates parallel scanning
- Handles memory management
- Generates reports

Key methods:
- `run_scan()`: Main entry point for scanning
- `_discover_files()`: Discovers files to scan
- `_parallel_scan()`: Scans files in parallel
- `_generate_reports()`: Generates reports

### 2. YARA Scanner (`scanner_core/python/scanner.py`)

The YARA scanner applies YARA rules to files. It:
- Compiles YARA rules
- Scans files for matches
- Handles Unicode and encoding issues
- Returns match results

Key methods:
- `compile_rules()`: Compiles YARA rules
- `scan_file()`: Scans a single file
- `get_rule_metadata()`: Retrieves rule metadata

### 3. C++ Scanner (`scanner_core/cpp/mock_scanner.cpp` and `mock_scanner.py`)

The C++ scanner provides a high-performance scanning option. It:
- Implements a subset of YARA functionality in C++
- Handles binary file scanning
- Supports Unicode filenames
- Communicates results via JSON

Key features:
- Unicode support for cross-platform compatibility
- Binary file handling
- JSON output for interoperability

### 4. Configuration Loader (`scanner_core/utils/config_loader.py`)

The configuration loader manages the application configuration. It:
- Loads configuration from JSON
- Provides default values
- Validates configuration

Key methods:
- `get_config()`: Returns the configuration
- `validate_config()`: Validates configuration values

### 5. Logger (`scanner_core/utils/logger.py`)

The logger manages application logging. It:
- Sets up logging to file and console
- Configures log levels
- Formats log messages

Key methods:
- `get_logger()`: Returns a configured logger
- `set_log_level()`: Sets the logging level

## Configuration

The pre-scanning phase is configured through the `config.json` file:

```json
{
    "scanner": {
        "target_directory": "E:\\WA",
        "recursive": true,
        "max_depth": 10,
        "max_file_size_mb": 100,
        "excluded_extensions": [".exe", ".dll", ".sys"],
        "excluded_directories": ["node_modules", ".git"]
    },
    "rules": {
        "rule_files": ["rules/custom_rules.yar", "rules/malware_rules.yar"],
        "compile_externals": true,
        "timeout": 60
    },
    "performance": {
        "threads": 8,
        "batch_size": 20,
        "timeout_seconds": 60,
        "max_memory_mb": 1024,
        "adaptive_threading": true,
        "priority_extensions": [".doc", ".docx", ".xls", ".xlsx", ".pdf", ".zip", ".rar", ".7z", ".exe", ".dll"]
    },
    "reporting": {
        "output_directory": "output",
        "generate_json": true,
        "generate_csv": true,
        "generate_html": true
    },
    "logging": {
        "log_directory": "logs",
        "log_level": "info",
        "log_to_console": true
    }
}
```

## Best Practices

### Code Organization

1. **Modular Architecture**: Keep components separate and focused on specific tasks
2. **Clear Interfaces**: Define clear interfaces between components
3. **Configuration Externalization**: Keep configuration in external files
4. **Error Handling**: Implement robust error handling throughout the codebase

### Performance Optimization

1. **Parallel Processing**: Use multi-threading for file scanning
2. **Memory Management**: Implement adaptive memory management
3. **Batch Processing**: Process files in batches to control resource usage
4. **File Prioritization**: Prioritize scanning of high-risk file types

### Cross-Platform Compatibility

1. **Path Handling**: Use `os.path.join()` for path construction
2. **Unicode Support**: Implement proper Unicode handling for filenames
3. **File I/O**: Use binary mode for file operations when appropriate
4. **Environment Detection**: Detect and adapt to the operating system

### Security

1. **Input Validation**: Validate all inputs, especially file paths
2. **Secure File Handling**: Implement secure file handling practices
3. **Error Suppression**: Avoid exposing sensitive information in error messages
4. **Logging**: Log security-relevant events

## Troubleshooting

Common issues and solutions:

1. **YARA Compilation Errors**:
   - Check YARA rule syntax
   - Ensure all string definitions end with semicolons
   - Verify rule metadata format

2. **Unicode Filename Issues**:
   - Use file-based communication instead of console output
   - Set appropriate environment variables for Unicode support
   - Use UTF-8 encoding for all file operations

3. **Memory Management Issues**:
   - Adjust batch size and thread count
   - Enable adaptive threading
   - Implement garbage collection triggers

4. **Performance Issues**:
   - Optimize file discovery process
   - Prioritize high-risk file types
   - Implement file size filtering

## Cross-Platform Compatibility

The pre-scanning phase is designed to run on both Windows and Linux platforms. Key considerations for cross-platform compatibility:

### Windows-Specific Considerations

1. **Path Handling**: Windows uses backslashes (`\`) in paths
2. **Unicode Support**: Windows console has limitations with Unicode
3. **File Locking**: Windows has stricter file locking behavior
4. **Command Execution**: Use appropriate command execution methods

### Linux-Specific Considerations

1. **Path Handling**: Linux uses forward slashes (`/`) in paths
2. **Case Sensitivity**: Linux filesystems are case-sensitive
3. **File Permissions**: Respect Linux file permissions
4. **Command Execution**: Use appropriate command execution methods

### Cross-Platform Solutions

1. **Path Handling**: Use `os.path.join()` for path construction
2. **Unicode Support**: Implement file-based communication
3. **File I/O**: Use binary mode for file operations
4. **Environment Detection**: Use `platform.system()` to detect the operating system
