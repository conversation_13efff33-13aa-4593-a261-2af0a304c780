"""
Tests for the configuration validator.
"""

import os
import sys
import json
import unittest
from typing import Dict, Any

# Add parent directory to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from utils.config_validator import validate_config, CONFIG_SCHEMA

class TestConfigValidator(unittest.TestCase):
    """Tests for the configuration validator."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create a valid configuration
        self.valid_config = {
            "project": {
                "name": "SBARDS",
                "version": "1.0.0",
                "description": "Security Behavior Analysis and Response Decision System"
            },
            "paths": {
                "output_dir": "output",
                "log_dir": "logs"
            },
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            },
            "monitoring": {
                "enabled": True,
                "monitor_whole_device": True,
                "interval_seconds": 2,
                "detailed_api_data": True,
                "continuous_monitoring": True,
                "use_mock_monitors": True
            },
            "prescanning": {
                "enabled": True,
                "max_file_size_mb": 100,
                "excluded_extensions": [".exe", ".dll"],
                "excluded_directories": ["node_modules", "venv"]
            }
        }
        
    def test_valid_config(self):
        """Test validation of a valid configuration."""
        is_valid, errors = validate_config(self.valid_config)
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
        
    def test_missing_required_section(self):
        """Test validation of a configuration with a missing required section."""
        # Remove required section
        invalid_config = self.valid_config.copy()
        del invalid_config["project"]
        
        is_valid, errors = validate_config(invalid_config)
        self.assertFalse(is_valid)
        self.assertIn("Missing required section: project", errors)
        
    def test_missing_required_field(self):
        """Test validation of a configuration with a missing required field."""
        # Remove required field
        invalid_config = self.valid_config.copy()
        del invalid_config["project"]["name"]
        
        is_valid, errors = validate_config(invalid_config)
        self.assertFalse(is_valid)
        self.assertIn("Missing required field: project.name", errors)
        
    def test_invalid_field_type(self):
        """Test validation of a configuration with an invalid field type."""
        # Set field to invalid type
        invalid_config = self.valid_config.copy()
        invalid_config["monitoring"]["enabled"] = "true"  # Should be boolean
        
        is_valid, errors = validate_config(invalid_config)
        self.assertFalse(is_valid)
        self.assertIn("Field monitoring.enabled should be of type bool", errors)
        
    def test_invalid_field_value(self):
        """Test validation of a configuration with an invalid field value."""
        # Set field to invalid value
        invalid_config = self.valid_config.copy()
        invalid_config["logging"]["level"] = "VERBOSE"  # Not a valid level
        
        is_valid, errors = validate_config(invalid_config)
        self.assertFalse(is_valid)
        self.assertIn("Field logging.level should be one of: DEBUG, INFO, WARNING, ERROR, CRITICAL", errors)
        
    def test_multiple_errors(self):
        """Test validation of a configuration with multiple errors."""
        # Create configuration with multiple errors
        invalid_config = self.valid_config.copy()
        del invalid_config["project"]["name"]
        invalid_config["monitoring"]["enabled"] = "true"
        invalid_config["logging"]["level"] = "VERBOSE"
        
        is_valid, errors = validate_config(invalid_config)
        self.assertFalse(is_valid)
        self.assertEqual(len(errors), 3)
        
if __name__ == '__main__':
    unittest.main()
