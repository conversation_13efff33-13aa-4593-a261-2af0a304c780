import os
import logging
import datetime
import json

class Logger:
    """
    Logger for the SBARDS Project.
    Sets up logging to both console and file.
    """
    
    def __init__(self, log_dir="logs", log_level="info"):
        """
        Initialize the logger.
        
        Args:
            log_dir (str): Directory to store log files
            log_level (str): Logging level (debug, info, warning, error, critical)
        """
        self.log_dir = os.path.abspath(log_dir)
        self.log_level = self._get_log_level(log_level)
        
        # Create log directory if it doesn't exist
        os.makedirs(self.log_dir, exist_ok=True)
        
        # Set up logging
        self._setup_logging()
    
    def _get_log_level(self, level_str):
        """
        Convert string log level to logging level.
        
        Args:
            level_str (str): String representation of log level
            
        Returns:
            int: Logging level
        """
        level_map = {
            "debug": logging.DEBUG,
            "info": logging.INFO,
            "warning": logging.WARNING,
            "error": logging.ERROR,
            "critical": logging.CRITICAL
        }
        
        return level_map.get(level_str.lower(), logging.INFO)
    
    def _setup_logging(self):
        """
        Set up logging configuration.
        """
        # Create a timestamp for the log file
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = os.path.join(self.log_dir, f"sbards_scan_{timestamp}.log")
        
        # Configure logging
        logging.basicConfig(
            level=self.log_level,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            handlers=[
                logging.FileHandler(log_file, encoding="utf-8"),
                logging.StreamHandler()
            ]
        )
        
        # Create a logger
        self.logger = logging.getLogger("SBARDS")
        self.logger.info(f"Logging initialized. Log file: {log_file}")
    
    def get_logger(self):
        """
        Get the logger.
        
        Returns:
            Logger: Configured logger
        """
        return self.logger
    
    def error(self, message, context: dict = None):
        """
        Log an error message with optional structured context.
        
        Args:
            message (str): The error message
            context (dict, optional): A dictionary with additional context information
        """
        log_entry = {"level": "ERROR", "message": message}
        if context:
            log_entry["context"] = context
        logging.error(json.dumps(log_entry))
