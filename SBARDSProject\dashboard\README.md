# SBARDS Monitoring Dashboard

This is a simple web-based dashboard for visualizing monitoring data from the SBARDS monitoring layer.

## Features

- Real-time monitoring status display
- Alert visualization
- Process monitoring
- File operation tracking
- Network connection monitoring
- Correlated event display
- Automatic response tracking
- Start/stop monitoring controls

## Installation

1. Install the required dependencies:

```bash
pip install -r requirements.txt
```

## Usage

1. Start the dashboard:

```bash
python dashboard.py
```

2. Open a web browser and navigate to:

```
http://localhost:5000
```

3. Use the dashboard to:
   - View monitoring status
   - Start/stop monitoring
   - View alerts and events
   - Track automatic responses

## Dashboard Sections

### System Status
Shows the current status of all monitoring components.

### Alerts
Displays recent alerts from the monitoring system.

### Correlated Events
Shows events that have been correlated across different monitoring components.

### Automatic Responses
Displays automatic responses taken by the system.

### File Operations
Shows recent file operations detected by the filesystem monitor.

### Network Connections
Displays current network connections detected by the network monitor.

## Integration with SBARDS

The dashboard integrates directly with the SBARDS monitoring layer through the Orchestrator class. It uses the monitoring layer's APIs to retrieve data and control the monitoring system.

## Requirements

- Python 3.6+
- Flask
- SBARDS monitoring layer
