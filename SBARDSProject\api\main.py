"""
Main API for SBARDS

This module provides the main FastAPI application for the SBARDS project.
It serves as the central entry point for all API endpoints and handles
routing, middleware, and static file serving.
"""

import os
import json
import logging
import time
from datetime import datetime
from fastapi import FastAPI, Depends, HTTPException, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi
from fastapi.middleware.gzip import GZipMiddleware
from starlette.middleware.base import BaseHTTPMiddleware
from typing import Dict, List, Any, Optional, Callable

# Import security module
from api.security import get_api_key, get_optional_api_key, api_key_validator

# Create FastAPI app
app = FastAPI(
    title="SBARDS API",
    description="""
    Security Behavior Analysis and Response Decision System API

    This API provides endpoints for:
    * Pre-scanning files and directories for potential threats
    * Continuous monitoring of system activity
    * Alerting on suspicious behavior
    * Retrieving detailed system information

    For more information, visit the project repository.
    """,
    version="1.0.0",
    docs_url=None,  # We'll create a custom docs endpoint
    redoc_url=None  # We'll create a custom redoc endpoint
)

# Request logging middleware
class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware for logging requests.

    This middleware logs information about each request, including
    the method, path, status code, and processing time.
    """

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process a request.

        Args:
            request (Request): FastAPI request object
            call_next (Callable): Next middleware in the chain

        Returns:
            Response: FastAPI response object
        """
        # Get start time
        start_time = time.time()

        # Process request
        response = await call_next(request)

        # Calculate processing time
        process_time = time.time() - start_time

        # Log request
        logging.info(
            f"{request.method} {request.url.path} {response.status_code} "
            f"({process_time:.4f}s) - {request.client.host if request.client else 'unknown'}"
        )

        return response

# Add middleware
app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(RequestLoggingMiddleware)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Load API keys
try:
    # Try to load config.json
    with open("config_new.json", "r") as f:
        config = json.load(f)

    # Load API keys from config
    api_key_validator.load_api_keys_from_config(config)

    # If no API keys are configured, generate one
    if not api_key_validator.api_keys:
        # Generate a new API key
        api_key = api_key_validator.generate_key()

        # Add to validator
        api_key_validator.load_api_keys([api_key])

        # Log the API key
        logging.info(f"Generated API key: {api_key}")

        # Update config
        if "backend" not in config:
            config["backend"] = {}
        if "api_keys" not in config["backend"]:
            config["backend"]["api_keys"] = {}
        config["backend"]["api_keys"]["api"] = [api_key]

        # Save config
        with open("config_new.json", "w") as f:
            json.dump(config, f, indent=4)
except Exception as e:
    logging.warning(f"Error loading API keys: {e}")
    logging.warning("API authentication is disabled")

# Import routers
from api.routers import prescanning, monitoring

# Add routers
app.include_router(prescanning.router)
app.include_router(monitoring.router)

# Mount static files
try:
    app.mount("/dashboard", StaticFiles(directory="ui/dashboard", html=True), name="dashboard")
except Exception as e:
    logging.warning(f"Could not mount dashboard: {e}")

# Custom OpenAPI schema
def custom_openapi():
    """
    Generate a custom OpenAPI schema.

    This function generates a custom OpenAPI schema with additional metadata
    and security schemes.

    Returns:
        Dict[str, Any]: OpenAPI schema
    """
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )

    # Add additional metadata
    openapi_schema["info"]["x-logo"] = {
        "url": "https://fastapi.tiangolo.com/img/logo-margin/logo-teal.png"
    }

    # Add contact information
    openapi_schema["info"]["contact"] = {
        "name": "SBARDS Support",
        "email": "<EMAIL>",
        "url": "https://sbards.example.com/support"
    }

    # Add license information
    openapi_schema["info"]["license"] = {
        "name": "MIT",
        "url": "https://opensource.org/licenses/MIT"
    }

    # Add security schemes
    openapi_schema["components"] = {
        "securitySchemes": {
            "APIKeyHeader": {
                "type": "apiKey",
                "in": "header",
                "name": "X-API-Key",
                "description": "API key for authentication. Add your API key with the `X-API-Key` header."
            }
        }
    }

    # Add global security requirement
    openapi_schema["security"] = [{"APIKeyHeader": []}]

    # Add tags metadata
    openapi_schema["tags"] = [
        {
            "name": "root",
            "description": "Root endpoints"
        },
        {
            "name": "monitoring",
            "description": "Monitoring endpoints for real-time system monitoring"
        },
        {
            "name": "prescanning",
            "description": "Pre-scanning endpoints for file and directory scanning"
        }
    ]

    app.openapi_schema = openapi_schema
    return app.openapi_schema

# Custom documentation endpoints
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    """
    Custom Swagger UI.

    Returns:
        HTMLResponse: Swagger UI HTML
    """
    return get_swagger_ui_html(
        openapi_url="/openapi.json",
        title=f"{app.title} - Swagger UI",
        oauth2_redirect_url="/docs/oauth2-redirect",
        swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js",
        swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css",
    )

@app.get("/openapi.json", include_in_schema=False)
async def get_openapi_endpoint():
    """
    Get OpenAPI schema.

    Returns:
        Dict[str, Any]: OpenAPI schema
    """
    return custom_openapi()

# Root endpoint
@app.get("/", tags=["root"])
async def root():
    """
    Root endpoint.

    This endpoint provides basic information about the API and available endpoints.

    Returns:
        Dict[str, Any]: API information including name, version, description, and available endpoints
    """
    return {
        "name": "SBARDS API",
        "version": "1.0.0",
        "description": "Security Behavior Analysis and Response Decision System API",
        "endpoints": {
            "prescanning": "/api/prescanning",
            "monitoring": "/api/monitoring",
            "dashboard": "/dashboard",
            "documentation": "/docs",
            "openapi_schema": "/openapi.json",
            "health": "/health"
        }
    }

# Health check endpoint
@app.get("/health", tags=["root"])
async def health_check():
    """
    Health check endpoint.

    This endpoint returns the health status of the API and its components.

    Returns:
        Dict[str, Any]: Health status information
    """
    # Check components
    components = {
        "api": {
            "status": "healthy",
            "version": "1.0.0"
        },
        "database": {
            "status": "healthy",
            "version": "1.0.0"
        },
        "monitoring": {
            "status": "healthy",
            "version": "1.0.0"
        },
        "prescanning": {
            "status": "healthy",
            "version": "1.0.0"
        }
    }

    # Calculate overall status
    overall_status = "healthy"
    for component, info in components.items():
        if info["status"] != "healthy":
            overall_status = "degraded"
            break

    return {
        "status": overall_status,
        "timestamp": datetime.now().isoformat(),
        "components": components,
        "uptime": "unknown"  # In a real implementation, this would be the actual uptime
    }
