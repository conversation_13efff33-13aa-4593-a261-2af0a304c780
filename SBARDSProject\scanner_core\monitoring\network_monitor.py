"""
Network Monitor for SBARDS

This module provides network monitoring capabilities for the SBARDS project.
"""

import os
import time
import socket
import logging
import threading
import platform
import subprocess
import re
from typing import Dict, List, Any, Optional, Set, Tuple
from collections import deque, defaultdict
from datetime import datetime

class NetworkMonitor:
    """
    Monitors network connections for suspicious activity.
    
    This class provides capabilities to monitor network connections and
    detect patterns that may indicate malicious activity.
    """
    
    def __init__(self, config: Dict[str, Any], alert_manager):
        """
        Initialize the network monitor.
        
        Args:
            config (Dict[str, Any]): Network monitoring configuration
            alert_manager: Alert manager instance for generating alerts
        """
        self.config = config
        self.alert_manager = alert_manager
        self.logger = logging.getLogger("SBARDS.NetworkMonitor")
        
        # Network monitoring configuration
        self.check_interval = config.get("check_interval_seconds", 30)
        self.suspicious_domains = config.get("suspicious_domains", [])
        self.log_connections = config.get("log_connections", True)
        
        # Connection tracking
        self.connection_history = deque(maxlen=1000)
        self.current_connections = set()
        self.previous_connections = set()
        
        # Platform-specific settings
        self.platform = platform.system().lower()
        
        self.logger.info(f"Network Monitor initialized on {platform.system()}")
        
    def start_monitoring(self, stop_event: threading.Event):
        """
        Start monitoring network connections.
        
        Args:
            stop_event (threading.Event): Event to signal stopping
        """
        self.logger.info("Starting network monitoring")
        
        # Initial connection scan
        self._scan_connections()
        
        # Monitoring loop
        while not stop_event.is_set():
            try:
                # Scan connections
                self._scan_connections()
                
                # Check for suspicious connections
                self._check_suspicious_connections()
                
                # Wait for next check interval
                stop_event.wait(self.check_interval)
                
            except Exception as e:
                self.logger.error(f"Error during network monitoring: {e}")
                # Wait a bit before retrying
                stop_event.wait(1.0)
                
        self.logger.info("Network monitoring stopped")
        
    def _scan_connections(self):
        """Scan current network connections and track changes."""
        # Update previous connections
        self.previous_connections = self.current_connections.copy()
        
        # Get current connections
        self.current_connections = self._get_network_connections()
        
        # Identify new and closed connections
        new_connections = self.current_connections - self.previous_connections
        closed_connections = self.previous_connections - self.current_connections
        
        # Log connection changes
        for conn_info in new_connections:
            self.connection_history.append({
                "timestamp": time.time(),
                "datetime": datetime.now().isoformat(),
                "event": "established",
                "connection": conn_info
            })
            if self.log_connections:
                self.logger.debug(f"New connection: {conn_info}")
                
        for conn_info in closed_connections:
            self.connection_history.append({
                "timestamp": time.time(),
                "datetime": datetime.now().isoformat(),
                "event": "closed",
                "connection": conn_info
            })
            if self.log_connections:
                self.logger.debug(f"Closed connection: {conn_info}")
                
    def _get_network_connections(self) -> Set[str]:
        """
        Get current network connections.
        
        Returns:
            Set[str]: Set of connection information strings
        """
        connections = set()
        
        try:
            if self.platform == "windows":
                # Use netstat on Windows
                output = subprocess.check_output(
                    ["netstat", "-ano"],
                    universal_newlines=True
                )
                
                # Parse output
                for line in output.strip().split("\n")[4:]:  # Skip header lines
                    parts = line.strip().split()
                    if len(parts) >= 5 and parts[0] in ["TCP", "UDP"]:
                        proto = parts[0]
                        local = parts[1]
                        remote = parts[2]
                        state = parts[3] if parts[0] == "TCP" else "N/A"
                        pid = parts[-1]
                        connections.add(f"{proto}:{local}:{remote}:{state}:{pid}")
                        
            else:
                # Use netstat on Linux/macOS
                output = subprocess.check_output(
                    ["netstat", "-tunapl"],
                    universal_newlines=True
                )
                
                # Parse output
                for line in output.strip().split("\n")[2:]:  # Skip header lines
                    parts = line.strip().split()
                    if len(parts) >= 7 and parts[0] in ["tcp", "udp", "tcp6", "udp6"]:
                        proto = parts[0]
                        local = parts[3]
                        remote = parts[4]
                        state = parts[5] if proto.startswith("tcp") else "N/A"
                        pid_program = parts[6]
                        connections.add(f"{proto}:{local}:{remote}:{state}:{pid_program}")
                        
        except Exception as e:
            self.logger.error(f"Error getting network connections: {e}")
            
        return connections
        
    def _check_suspicious_connections(self):
        """Check for suspicious network connections."""
        for conn_info in self.current_connections:
            # Extract connection details
            parts = conn_info.split(":")
            if len(parts) < 3:
                continue
                
            proto = parts[0]
            remote = parts[2]
            
            # Check for connections to suspicious domains
            for domain in self.suspicious_domains:
                if domain in remote:
                    self.alert_manager.add_alert(
                        source="NetworkMonitor",
                        alert_type="suspicious_connection",
                        message=f"Connection to suspicious domain detected: {remote}",
                        severity="warning",
                        details={
                            "connection": conn_info,
                            "protocol": proto,
                            "remote": remote,
                            "matched_domain": domain
                        }
                    )
                    
                    self.logger.warning(f"Suspicious connection detected: {conn_info} (matched domain: {domain})")
                    
    def get_connection_history(self, count: int = None) -> List[Dict[str, Any]]:
        """
        Get connection history.
        
        Args:
            count (int, optional): Number of history entries to retrieve
            
        Returns:
            List[Dict[str, Any]]: List of connection history entries
        """
        if count is None:
            return list(self.connection_history)
        else:
            return list(self.connection_history)[-count:]
            
    def get_current_connections(self) -> Set[str]:
        """
        Get current connections.
        
        Returns:
            Set[str]: Set of current connection information strings
        """
        return self.current_connections.copy()
        
    def resolve_hostname(self, ip_address: str) -> str:
        """
        Resolve IP address to hostname.
        
        Args:
            ip_address (str): IP address to resolve
            
        Returns:
            str: Hostname or original IP if resolution fails
        """
        try:
            hostname, _, _ = socket.gethostbyaddr(ip_address)
            return hostname
        except (socket.herror, socket.gaierror):
            return ip_address
