"""
Utility functions for the SBARDS backend.

This module provides utility functions for interacting with the SBARDS scanner
and processing scan results.
"""

import os
import json
import hashlib
import logging
import requests
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("backend_utils.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("SBARDS.Backend.Utils")

def calculate_file_hash(file_path: str) -> str:
    """Calculate SHA-256 hash of a file."""
    try:
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for byte_block in iter(lambda: f.read(4096), b""):
                sha256_hash.update(byte_block)
        return sha256_hash.hexdigest()
    except Exception as e:
        logger.error(f"Error calculating hash for {file_path}: {e}")
        return ""

def parse_html_report(report_path: str) -> Dict[str, Any]:
    """Parse an HTML scan report to extract structured data."""
    try:
        with open(report_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # Extract basic information from the HTML report
        # This is a simple implementation and might need to be adjusted based on the actual HTML structure
        import re
        
        # Extract scan path
        scan_path_match = re.search(r'<strong>Path:</strong>\s*([^<]+)', content)
        scan_path = scan_path_match.group(1).strip() if scan_path_match else ""
        
        # Extract files scanned
        files_scanned_match = re.search(r'<strong>Files Scanned:</strong>\s*(\d+)', content)
        files_scanned = int(files_scanned_match.group(1)) if files_scanned_match else 0
        
        # Extract threats found
        threats_found_match = re.search(r'<strong>Threats Found:</strong>\s*<span[^>]*>(\d+)</span>', content)
        threats_found = int(threats_found_match.group(1)) if threats_found_match else 0
        
        # Extract scan date
        scan_date_match = re.search(r'<strong>Scan Date:</strong>\s*([^<]+)', content)
        scan_date = scan_date_match.group(1).strip() if scan_date_match else ""
        
        return {
            "scan_path": scan_path,
            "files_scanned": files_scanned,
            "threats_found": threats_found,
            "scan_date": scan_date,
            "report_content": content
        }
    except Exception as e:
        logger.error(f"Error parsing HTML report {report_path}: {e}")
        return {
            "scan_path": "",
            "files_scanned": 0,
            "threats_found": 0,
            "scan_date": "",
            "report_content": ""
        }

def extract_file_results(log_file: str) -> List[Dict[str, Any]]:
    """Extract file results from a scanner log file."""
    file_results = []
    try:
        with open(log_file, "r", encoding="utf-8") as f:
            log_content = f.readlines()
        
        for line in log_content:
            if "[WARNING]" in line and "Potential threat found in" in line:
                # Extract file path
                file_path_match = line.split("Potential threat found in")[1].strip()
                if file_path_match:
                    file_path = file_path_match
                    file_hash = calculate_file_hash(file_path) if os.path.exists(file_path) else ""
                    
                    file_results.append({
                        "file_path": file_path,
                        "file_hash": file_hash,
                        "is_threat": True,
                        "threat_type": "Potential Threat"
                    })
    except Exception as e:
        logger.error(f"Error extracting file results from log {log_file}: {e}")
    
    return file_results

def send_report_to_backend(api_url: str, report_data: Dict[str, Any]) -> Dict[str, Any]:
    """Send a scan report to the backend API."""
    try:
        response = requests.post(f"{api_url}/api/reports/", json=report_data)
        if response.status_code == 201:
            logger.info(f"Successfully sent report to backend: {response.json()}")
            return response.json()
        else:
            logger.error(f"Error sending report to backend: {response.status_code} - {response.text}")
            return {"error": f"API error: {response.status_code}"}
    except Exception as e:
        logger.error(f"Exception sending report to backend: {e}")
        return {"error": str(e)}

def process_scan_report(report_path: str, log_file: str, api_url: str) -> Dict[str, Any]:
    """Process a scan report and send it to the backend."""
    try:
        # Parse the HTML report
        report_data = parse_html_report(report_path)
        
        # Extract file results from log
        file_results = extract_file_results(log_file)
        
        # Create the report data structure
        scan_id = f"scan_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        report_payload = {
            "scan_id": scan_id,
            "scan_path": report_data["scan_path"],
            "files_scanned": report_data["files_scanned"],
            "threats_found": report_data["threats_found"],
            "report_path": report_path,
            "report_content": report_data["report_content"],
            "file_results": file_results
        }
        
        # Send to backend
        result = send_report_to_backend(api_url, report_payload)
        return result
    except Exception as e:
        logger.error(f"Error processing scan report: {e}")
        return {"error": str(e)}
