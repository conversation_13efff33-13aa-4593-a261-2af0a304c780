"""
System Router for SBARDS API

This module provides the system router for the SBARDS API.
"""

import os
import platform
import logging
import psutil
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

# Create router
router = APIRouter()

# Models
class SystemInfo(BaseModel):
    """System information model."""
    platform: str
    platform_release: str
    platform_version: str
    architecture: str
    hostname: str
    processor: str
    cpu_count: int
    memory_total: float
    memory_available: float
    disk_usage: Dict[str, Any]

class ProcessInfo(BaseModel):
    """Process information model."""
    pid: int
    name: str
    username: str
    cpu_percent: float
    memory_percent: float
    status: str
    create_time: float

# Endpoints
@router.get("/info", response_model=SystemInfo)
async def get_system_info():
    """Get system information."""
    try:
        # Get disk usage for all drives
        disk_usage = {}
        for partition in psutil.disk_partitions():
            try:
                usage = psutil.disk_usage(partition.mountpoint)
                disk_usage[partition.mountpoint] = {
                    "total": usage.total / (1024 * 1024 * 1024),  # GB
                    "used": usage.used / (1024 * 1024 * 1024),  # GB
                    "free": usage.free / (1024 * 1024 * 1024),  # GB
                    "percent": usage.percent
                }
            except Exception as e:
                logging.warning(f"Error getting disk usage for {partition.mountpoint}: {e}")
        
        # Get system information
        return SystemInfo(
            platform=platform.system(),
            platform_release=platform.release(),
            platform_version=platform.version(),
            architecture=platform.machine(),
            hostname=platform.node(),
            processor=platform.processor(),
            cpu_count=psutil.cpu_count(),
            memory_total=psutil.virtual_memory().total / (1024 * 1024 * 1024),  # GB
            memory_available=psutil.virtual_memory().available / (1024 * 1024 * 1024),  # GB
            disk_usage=disk_usage
        )
    except Exception as e:
        logging.error(f"Error getting system information: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting system information: {str(e)}")

@router.get("/processes", response_model=List[ProcessInfo])
async def get_processes(limit: int = 20):
    """Get current processes."""
    try:
        # Get processes
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'username', 'cpu_percent', 'memory_percent', 'status', 'create_time']):
            try:
                processes.append(ProcessInfo(
                    pid=proc.info['pid'],
                    name=proc.info['name'],
                    username=proc.info['username'] or "",
                    cpu_percent=proc.info['cpu_percent'] or 0.0,
                    memory_percent=proc.info['memory_percent'] or 0.0,
                    status=proc.info['status'] or "",
                    create_time=proc.info['create_time'] or 0.0
                ))
            except Exception as e:
                logging.warning(f"Error getting process info for PID {proc.info.get('pid')}: {e}")
        
        # Sort by CPU usage and limit
        processes.sort(key=lambda p: p.cpu_percent, reverse=True)
        return processes[:limit]
    except Exception as e:
        logging.error(f"Error getting processes: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting processes: {str(e)}")

@router.get("/memory")
async def get_memory_info():
    """Get memory information."""
    try:
        # Get memory information
        memory = psutil.virtual_memory()
        return {
            "total": memory.total / (1024 * 1024 * 1024),  # GB
            "available": memory.available / (1024 * 1024 * 1024),  # GB
            "used": memory.used / (1024 * 1024 * 1024),  # GB
            "percent": memory.percent
        }
    except Exception as e:
        logging.error(f"Error getting memory information: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting memory information: {str(e)}")

@router.get("/cpu")
async def get_cpu_info():
    """Get CPU information."""
    try:
        # Get CPU information
        return {
            "count": psutil.cpu_count(),
            "percent": psutil.cpu_percent(interval=0.1, percpu=True),
            "average": psutil.cpu_percent(interval=0.1)
        }
    except Exception as e:
        logging.error(f"Error getting CPU information: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting CPU information: {str(e)}")

@router.get("/disk")
async def get_disk_info():
    """Get disk information."""
    try:
        # Get disk information
        disk_info = {}
        for partition in psutil.disk_partitions():
            try:
                usage = psutil.disk_usage(partition.mountpoint)
                disk_info[partition.mountpoint] = {
                    "device": partition.device,
                    "fstype": partition.fstype,
                    "total": usage.total / (1024 * 1024 * 1024),  # GB
                    "used": usage.used / (1024 * 1024 * 1024),  # GB
                    "free": usage.free / (1024 * 1024 * 1024),  # GB
                    "percent": usage.percent
                }
            except Exception as e:
                logging.warning(f"Error getting disk usage for {partition.mountpoint}: {e}")
        
        return disk_info
    except Exception as e:
        logging.error(f"Error getting disk information: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting disk information: {str(e)}")
