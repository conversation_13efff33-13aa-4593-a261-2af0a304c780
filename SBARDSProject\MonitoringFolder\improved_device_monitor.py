"""
Improved Device Monitor for SBARDS

This script monitors all connected devices and storage volumes with improved filtering.
"""

import os
import sys
import json
import time
import shutil
import psutil
import datetime
import threading
import logging
import string
import win32api
import win32file
import win32con
from typing import Dict, List, Any, Set

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/device_monitor.log', mode='a')
    ]
)

logger = logging.getLogger("SBARDS.DeviceMonitor")

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

# Load configuration
try:
    config_path = 'SBARDSProject/config.json'
    if not os.path.exists(config_path):
        config_path = 'config.json'
        if not os.path.exists(config_path):
            logger.error("Configuration file not found")
            sys.exit(1)
            
    with open(config_path, 'r') as f:
        config = json.load(f)
    logger.info("Configuration loaded successfully")
except Exception as e:
    logger.error(f"Error loading configuration: {e}")
    sys.exit(1)

class ImprovedDeviceMonitor:
    """Improved monitor for all connected devices."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the device monitor."""
        self.config = config
        self.logger = logging.getLogger("SBARDS.DeviceMonitor")
        
        # Device tracking
        self.devices = {}
        self.suspicious_files = []
        
        # Get suspicious extensions from config
        self.suspicious_extensions = config.get("monitoring", {}).get("filesystem_monitoring", {}).get(
            "suspicious_extensions", [
                ".encrypted", ".locked", ".crypted", ".crypt", ".crypto", ".enc", ".ransomware",
                ".wcry", ".wncry", ".locky", ".zepto", ".cerber", ".coverton", ".enigma", ".pays"
            ]
        )
        
        # Directories to skip
        self.skip_dirs = [
            "System Volume Information",
            "$RECYCLE.BIN",
            "Windows",
            "Program Files",
            "Program Files (x86)",
            "ProgramData"
        ]
        
        # Paths to skip (partial matches)
        self.skip_paths = [
            "encoding",
            "Encodings",
            "tcl8",
            "perl5",
            "mingw",
            "git"
        ]
        
        # Monitoring thread
        self.monitoring_thread = None
        self.stop_event = threading.Event()
        self.is_running = False
        
        # Initial scan
        self.detect_devices()
        
    def detect_devices(self) -> None:
        """Detect all connected devices."""
        self.logger.info("Detecting connected devices...")
        
        try:
            # Get all drives
            drives = []
            
            # Get all drive letters
            for letter in string.ascii_uppercase:
                drive = f"{letter}:\\"
                if os.path.exists(drive):
                    try:
                        drive_type = win32file.GetDriveType(drive)
                        drive_info = {
                            "path": drive,
                            "type": self._get_drive_type_name(drive_type),
                            "label": self._get_drive_label(drive)
                        }
                        
                        # Get disk usage if available
                        try:
                            usage = shutil.disk_usage(drive)
                            drive_info["total"] = usage.total
                            drive_info["used"] = usage.used
                            drive_info["free"] = usage.free
                            drive_info["percent_used"] = (usage.used / usage.total) * 100 if usage.total > 0 else 0
                        except Exception as e:
                            self.logger.error(f"Error getting disk usage for {drive}: {e}")
                            
                        drives.append(drive_info)
                        self.logger.info(f"Detected drive: {drive} ({drive_info['type']}, {drive_info.get('label', 'No Label')})")
                    except Exception as e:
                        self.logger.error(f"Error detecting drive {drive}: {e}")
            
            # Update devices
            self.devices = {drive["path"]: drive for drive in drives}
            
            # Log summary
            self.logger.info(f"Detected {len(drives)} drives")
            for drive in drives:
                if "total" in drive:
                    self.logger.info(f"  {drive['path']} ({drive['type']}): {drive.get('label', 'No Label')}, "
                                    f"{drive['total'] / (1024**3):.2f} GB total, "
                                    f"{drive['free'] / (1024**3):.2f} GB free, "
                                    f"{drive['percent_used']:.2f}% used")
                else:
                    self.logger.info(f"  {drive['path']} ({drive['type']}): {drive.get('label', 'No Label')}")
                    
        except Exception as e:
            self.logger.error(f"Error detecting devices: {e}")
            
    def _get_drive_type_name(self, drive_type: int) -> str:
        """Get drive type name."""
        drive_types = {
            win32con.DRIVE_UNKNOWN: "Unknown",
            win32con.DRIVE_NO_ROOT_DIR: "No Root Directory",
            win32con.DRIVE_REMOVABLE: "Removable",
            win32con.DRIVE_FIXED: "Fixed",
            win32con.DRIVE_REMOTE: "Network",
            win32con.DRIVE_CDROM: "CD-ROM",
            win32con.DRIVE_RAMDISK: "RAM Disk"
        }
        return drive_types.get(drive_type, "Unknown")
        
    def _get_drive_label(self, drive: str) -> str:
        """Get drive label."""
        try:
            volume_name = win32api.GetVolumeInformation(drive)[0]
            return volume_name
        except Exception:
            return ""
            
    def scan_device(self, device_path: str) -> None:
        """Scan a specific device."""
        self.logger.info(f"Scanning device: {device_path}")
        
        try:
            # Check if device exists
            if not os.path.exists(device_path):
                self.logger.error(f"Device {device_path} not found")
                return
                
            # Get disk usage
            try:
                disk_usage = shutil.disk_usage(device_path)
                self.logger.info(f"Total space: {disk_usage.total / (1024**3):.2f} GB")
                self.logger.info(f"Used space: {disk_usage.used / (1024**3):.2f} GB")
                self.logger.info(f"Free space: {disk_usage.free / (1024**3):.2f} GB")
                self.logger.info(f"Usage percentage: {disk_usage.used / disk_usage.total * 100:.2f}%")
            except Exception as e:
                self.logger.error(f"Error getting disk usage for {device_path}: {e}")
                
            # List top-level directories
            try:
                self.logger.info(f"Top-level directories in {device_path}:")
                for item in os.listdir(device_path):
                    item_path = os.path.join(device_path, item)
                    if os.path.isdir(item_path):
                        self.logger.info(f"  Directory: {item}")
                    else:
                        self.logger.info(f"  File: {item}")
            except Exception as e:
                self.logger.error(f"Error listing top-level directories for {device_path}: {e}")
                
            # Scan for suspicious files
            self.scan_for_suspicious_files(device_path)
            
        except Exception as e:
            self.logger.error(f"Error scanning device {device_path}: {e}")
            
    def scan_for_suspicious_files(self, device_path: str) -> None:
        """Scan for suspicious files on a device."""
        self.logger.info(f"Scanning for suspicious files on {device_path}...")
        
        try:
            # Check if device exists
            if not os.path.exists(device_path):
                self.logger.error(f"Device {device_path} not found")
                return
                
            # Reset suspicious files for this device
            device_suspicious_files = []
            
            # Walk through the device
            for root, dirs, files in os.walk(device_path):
                # Skip directories
                skip = False
                for skip_dir in self.skip_dirs:
                    if skip_dir in root:
                        skip = True
                        break
                        
                for skip_path in self.skip_paths:
                    if skip_path.lower() in root.lower():
                        skip = True
                        break
                        
                if skip:
                    # Skip this directory
                    dirs[:] = []  # Don't recurse into subdirectories
                    continue
                    
                for file in files:
                    file_path = os.path.join(root, file)
                    
                    try:
                        # Check file extension
                        _, ext = os.path.splitext(file_path)
                        if ext.lower() in self.suspicious_extensions:
                            # Skip encoding files
                            if ext.lower() == ".enc" and ("encoding" in file_path.lower() or "encodings" in file_path.lower()):
                                continue
                                
                            # Get file size
                            try:
                                file_size = os.path.getsize(file_path)
                            except:
                                file_size = 0
                                
                            # Get file modification time
                            try:
                                file_modified = datetime.datetime.fromtimestamp(os.path.getmtime(file_path)).isoformat()
                            except:
                                file_modified = "unknown"
                                
                            file_info = {
                                "path": file_path,
                                "extension": ext,
                                "size": file_size,
                                "modified": file_modified
                            }
                            device_suspicious_files.append(file_info)
                            self.logger.warning(f"Suspicious file found: {file_path}")
                            
                    except Exception as e:
                        self.logger.error(f"Error processing file {file_path}: {e}")
                        
            # Update suspicious files
            self.suspicious_files.extend(device_suspicious_files)
            
            self.logger.info(f"Found {len(device_suspicious_files)} suspicious files on {device_path}")
            
        except Exception as e:
            self.logger.error(f"Error scanning for suspicious files on {device_path}: {e}")
            
    def start_monitoring(self) -> bool:
        """Start monitoring."""
        if self.is_running:
            self.logger.warning("Monitoring is already running")
            return True
            
        self.logger.info("Starting device monitoring")
        self.stop_event.clear()
        
        # Start monitoring thread
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self.monitoring_thread.start()
        
        self.is_running = True
        return True
        
    def stop_monitoring(self) -> bool:
        """Stop monitoring."""
        if not self.is_running:
            return True
            
        self.logger.info("Stopping device monitoring")
        self.stop_event.set()
        
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=10.0)
            
        self.is_running = False
        return True
        
    def _monitoring_loop(self) -> None:
        """Monitoring loop."""
        device_scan_interval = 30  # Scan devices every 30 seconds
        full_scan_interval = 300  # Full scan every 5 minutes
        
        last_full_scan = 0
        
        while not self.stop_event.is_set():
            try:
                # Detect devices
                self.detect_devices()
                
                # Check if it's time for a full scan
                current_time = time.time()
                if current_time - last_full_scan >= full_scan_interval:
                    self.logger.info("Performing full scan of all devices")
                    
                    # Scan all devices
                    for device_path in self.devices:
                        if self.stop_event.is_set():
                            break
                        self.scan_device(device_path)
                        
                    last_full_scan = current_time
                    
                # Wait for next scan
                for _ in range(device_scan_interval):
                    if self.stop_event.is_set():
                        break
                    time.sleep(1)
                    
            except Exception as e:
                self.logger.error(f"Error during monitoring: {e}")
                # Wait a bit before retrying
                self.stop_event.wait(1.0)
                
        self.logger.info("Device monitoring stopped")
        
    def get_devices(self) -> Dict[str, Dict[str, Any]]:
        """Get detected devices."""
        return self.devices
        
    def get_suspicious_files(self) -> List[Dict[str, Any]]:
        """Get suspicious files."""
        return self.suspicious_files

def main():
    """Main entry point."""
    logger.info("Starting Improved Device Monitor")
    
    # Create monitor
    monitor = ImprovedDeviceMonitor(config)
    
    # Start monitoring
    monitor.start_monitoring()
    
    try:
        # Keep running until interrupted
        logger.info("Press Ctrl+C to stop monitoring")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Stopping monitoring")
    finally:
        # Stop monitoring
        monitor.stop_monitoring()
        logger.info("Monitoring stopped")
        
        # Print summary
        devices = monitor.get_devices()
        logger.info(f"Monitored {len(devices)} devices:")
        for path, device in devices.items():
            if "total" in device:
                logger.info(f"  {path} ({device['type']}): {device.get('label', 'No Label')}, "
                           f"{device['total'] / (1024**3):.2f} GB total, "
                           f"{device['free'] / (1024**3):.2f} GB free, "
                           f"{device['percent_used']:.2f}% used")
            else:
                logger.info(f"  {path} ({device['type']}): {device.get('label', 'No Label')}")
                
        suspicious_files = monitor.get_suspicious_files()
        logger.info(f"Found {len(suspicious_files)} suspicious files:")
        for file in suspicious_files:
            logger.info(f"  {file['path']} ({file['extension']}, {file['size']} bytes, modified: {file['modified']})")

if __name__ == "__main__":
    main()
