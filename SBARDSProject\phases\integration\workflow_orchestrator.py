"""
SBARDS Comprehensive Workflow Orchestrator

This module implements the complete SBARDS workflow as specified:
1. Capture Layer (طبقة الالتقاط)
2. Pre-Scanning Quick Check (فحص أولي سريع)
3. Static Analysis Layer (طبقة التحليل الثابت)
4. Dynamic Analysis Layer (طبقة التحليل الديناميكي)
5. Response Layer (طبقة الاستجابة)
6. External Integration Layer (طبقة التكامل الخارجي)
7. Memory Protection Layer (طبقة حماية الذاكرة)
8. Continuous Monitoring Layer (طبقة المراقبة المستمرة)
"""

import os
import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional, Union, BinaryIO
from pathlib import Path

# Import all phase modules
from phases.capture.capture import CaptureLayer
from phases.prescanning.orchestrator import Orchestrator as PreScanningOrchestrator
from phases.static_analysis.static_analyzer import StaticAnalyzer
from phases.dynamic_analysis.dynamic_analyzer import DynamicAnalyzer
from phases.response.response import ResponseLayer
from phases.integration.external_integration import ExternalIntegrationLayer
from phases.memory_protection.memory_protection import MemoryProtectionLayer
from phases.monitoring.monitor_manager import MonitorManager


class WorkflowOrchestrator:
    """
    Comprehensive SBARDS Workflow Orchestrator

    Coordinates all analysis phases according to the specified workflow.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the Workflow Orchestrator.

        Args:
            config (Dict[str, Any]): Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger("SBARDS.WorkflowOrchestrator")

        # Initialize all layers
        self._init_layers()

        # Workflow state
        self.current_workflow = None

    def _init_layers(self):
        """Initialize all analysis layers."""
        try:
            self.capture_layer = CaptureLayer(self.config)
            self.prescanning_orchestrator = PreScanningOrchestrator(self.config)
            self.static_analyzer = StaticAnalyzer(self.config)
            self.dynamic_analyzer = DynamicAnalyzer(self.config)
            self.response_layer = ResponseLayer(self.config)
            self.external_integration = ExternalIntegrationLayer(self.config)
            self.memory_protection = MemoryProtectionLayer(self.config)
            self.monitor_manager = MonitorManager(self.config)

            self.logger.info("All workflow layers initialized successfully")

        except Exception as e:
            self.logger.error(f"Error initializing workflow layers: {e}")
            raise

    def process_file(self, file_input: Union[str, BinaryIO, bytes],
                    filename: Optional[str] = None,
                    metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a file through the complete SBARDS workflow.

        Args:
            file_input: File to process (path, file-like object, or bytes)
            filename: Optional filename
            metadata: Optional metadata

        Returns:
            Dict[str, Any]: Complete workflow results
        """
        workflow_id = self._generate_workflow_id()

        try:
            self.logger.info(f"Starting SBARDS workflow {workflow_id}")

            # Initialize workflow state
            workflow_results = {
                "workflow_id": workflow_id,
                "timestamp": datetime.now().isoformat(),
                "phases": {},
                "final_decision": {},
                "blockchain_hash": None
            }

            self.current_workflow = workflow_results

            # Phase 1: Capture Layer (طبقة الالتقاط)
            self.logger.info("Phase 1: File Capture")

            # Check if input is a directory or file
            if isinstance(file_input, str) and os.path.isdir(file_input):
                capture_result = self.capture_layer.capture_directory(file_input, metadata=metadata)
            else:
                capture_result = self.capture_layer.capture_file(file_input, filename, metadata)

            workflow_results["phases"]["capture"] = capture_result

            if not capture_result.get("success", False):
                return self._finalize_workflow(workflow_results, "REJECTED", "File capture failed")

            # Handle directory vs single file results
            if "scan_id" in capture_result:
                # Directory scan result
                if capture_result.get("files_captured", 0) == 0:
                    return self._finalize_workflow(workflow_results, "REJECTED", "No files captured from directory")

                # For directory scans, we'll process the first captured file as representative
                # In a full implementation, you might want to process all files
                first_file = capture_result["captured_files"][0]
                file_path = first_file["file_path"]
                file_info = first_file["file_info"]
            else:
                # Single file result
                file_path = capture_result["file_path"]
                file_info = capture_result["file_info"]

            # Phase 2: Pre-Scanning Quick Check (فحص أولي سريع)
            self.logger.info("Phase 2: Pre-Scanning Quick Check")
            prescan_result = self.prescanning_orchestrator.scan_file(file_path)
            workflow_results["phases"]["prescanning"] = prescan_result

            # Decision point: If clean, store hash in blockchain and allow
            if self._is_file_clean(prescan_result):
                blockchain_hash = self._store_in_blockchain(file_info)
                workflow_results["blockchain_hash"] = blockchain_hash
                return self._finalize_workflow(workflow_results, "ALLOWED", "Pre-scan clean")

            # Phase 3: Static Analysis Layer (طبقة التحليل الثابت)
            self.logger.info("Phase 3: Static Analysis")
            static_result = self.static_analyzer.analyze_file(file_path, file_info)
            workflow_results["phases"]["static_analysis"] = static_result

            # Decision point based on static analysis
            static_decision = self._evaluate_static_results(static_result)

            if static_decision == "MALICIOUS":
                # Immediate quarantine and alert
                self._quarantine_file(file_path, workflow_results)
                return self._finalize_workflow(workflow_results, "QUARANTINED", "Static analysis detected malware")

            elif static_decision == "CLEAN":
                # Store hash and allow
                blockchain_hash = self._store_in_blockchain(file_info)
                workflow_results["blockchain_hash"] = blockchain_hash
                return self._finalize_workflow(workflow_results, "ALLOWED", "Static analysis clean")

            # Phase 4: Dynamic Analysis Layer (طبقة التحليل الديناميكي)
            self.logger.info("Phase 4: Dynamic Analysis")
            dynamic_result = self.dynamic_analyzer.analyze_file(file_path, static_result)
            workflow_results["phases"]["dynamic_analysis"] = dynamic_result

            # Decision point based on dynamic analysis
            dynamic_decision = self._evaluate_dynamic_results(dynamic_result)

            if dynamic_decision == "MALICIOUS":
                # Quarantine and update threat databases
                self._quarantine_file(file_path, workflow_results)
                self._update_threat_databases(workflow_results)
                return self._finalize_workflow(workflow_results, "QUARANTINED", "Dynamic analysis detected malware")

            elif dynamic_decision == "SUSPICIOUS":
                # Isolate in honeypot
                self._isolate_in_honeypot(file_path, workflow_results)
                return self._finalize_workflow(workflow_results, "ISOLATED", "Suspicious behavior detected")

            # If we reach here, file is considered clean
            # Update databases and allow access
            self._update_databases(workflow_results)
            blockchain_hash = self._store_in_blockchain(file_info)
            workflow_results["blockchain_hash"] = blockchain_hash

            return self._finalize_workflow(workflow_results, "ALLOWED", "All phases passed")

        except Exception as e:
            self.logger.error(f"Error in workflow {workflow_id}: {e}")
            return self._finalize_workflow(workflow_results, "ERROR", str(e))

    def _generate_workflow_id(self) -> str:
        """Generate unique workflow ID."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        return f"workflow_{timestamp}"

    def _is_file_clean(self, prescan_result: Dict[str, Any]) -> bool:
        """Determine if file is clean based on pre-scan results."""
        # Check if any matches were found
        matches = prescan_result.get("matches", [])
        return len(matches) == 0

    def _evaluate_static_results(self, static_result: Dict[str, Any]) -> str:
        """Evaluate static analysis results."""
        if "error" in static_result:
            return "SUSPICIOUS"

        risk_assessment = static_result.get("risk_assessment", {})
        risk_level = risk_assessment.get("risk_level", "MINIMAL")

        if risk_level == "HIGH":
            return "MALICIOUS"
        elif risk_level in ["MINIMAL", "LOW"]:
            return "CLEAN"
        else:
            return "SUSPICIOUS"

    def _evaluate_dynamic_results(self, dynamic_result: Dict[str, Any]) -> str:
        """Evaluate dynamic analysis results."""
        if "error" in dynamic_result:
            return "SUSPICIOUS"

        risk_assessment = dynamic_result.get("risk_assessment", {})
        risk_level = risk_assessment.get("risk_level", "MINIMAL")

        if risk_level == "HIGH":
            return "MALICIOUS"
        elif risk_level in ["MINIMAL", "LOW"]:
            return "CLEAN"
        else:
            return "SUSPICIOUS"

    def _quarantine_file(self, file_path: str, workflow_results: Dict[str, Any]):
        """Quarantine malicious file."""
        try:
            self.response_layer.quarantine_file(file_path, workflow_results)
            self.logger.info(f"File quarantined: {file_path}")
        except Exception as e:
            self.logger.error(f"Error quarantining file: {e}")

    def _isolate_in_honeypot(self, file_path: str, workflow_results: Dict[str, Any]):
        """Isolate suspicious file in honeypot."""
        try:
            self.response_layer.isolate_in_honeypot(file_path, workflow_results)
            self.logger.info(f"File isolated in honeypot: {file_path}")
        except Exception as e:
            self.logger.error(f"Error isolating file: {e}")

    def _store_in_blockchain(self, file_info: Dict[str, Any]) -> Optional[str]:
        """Store file hash in blockchain."""
        try:
            return self.external_integration.store_hash_in_blockchain(file_info)
        except Exception as e:
            self.logger.error(f"Error storing in blockchain: {e}")
            return None

    def _update_threat_databases(self, workflow_results: Dict[str, Any]):
        """Update threat intelligence databases."""
        try:
            self.external_integration.update_threat_databases(workflow_results)
        except Exception as e:
            self.logger.error(f"Error updating threat databases: {e}")

    def _update_databases(self, workflow_results: Dict[str, Any]):
        """Update known good databases."""
        try:
            self.external_integration.update_good_databases(workflow_results)
        except Exception as e:
            self.logger.error(f"Error updating databases: {e}")

    def _finalize_workflow(self, workflow_results: Dict[str, Any],
                          decision: str, reason: str) -> Dict[str, Any]:
        """Finalize workflow with decision."""
        workflow_results["final_decision"] = {
            "decision": decision,
            "reason": reason,
            "timestamp": datetime.now().isoformat()
        }

        # Save workflow results
        self._save_workflow_results(workflow_results)

        # Trigger response actions
        self._trigger_response_actions(workflow_results)

        # Update continuous monitoring
        self._update_monitoring(workflow_results)

        self.logger.info(f"Workflow {workflow_results['workflow_id']} completed: {decision}")

        return workflow_results

    def _save_workflow_results(self, workflow_results: Dict[str, Any]):
        """Save workflow results to file."""
        try:
            results_dir = Path("workflow_results")
            results_dir.mkdir(exist_ok=True)

            result_file = results_dir / f"{workflow_results['workflow_id']}.json"

            with open(result_file, 'w') as f:
                json.dump(workflow_results, f, indent=2, default=str)

        except Exception as e:
            self.logger.error(f"Error saving workflow results: {e}")

    def _trigger_response_actions(self, workflow_results: Dict[str, Any]):
        """Trigger appropriate response actions."""
        try:
            decision = workflow_results["final_decision"]["decision"]

            if decision in ["QUARANTINED", "ISOLATED"]:
                self.response_layer.send_alerts(workflow_results)
                self.response_layer.update_permissions(workflow_results)

        except Exception as e:
            self.logger.error(f"Error triggering response actions: {e}")

    def _update_monitoring(self, workflow_results: Dict[str, Any]):
        """Update continuous monitoring with workflow results."""
        try:
            self.monitor_manager.update_with_workflow_results(workflow_results)
        except Exception as e:
            self.logger.error(f"Error updating monitoring: {e}")
