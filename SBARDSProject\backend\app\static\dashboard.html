<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SBARDS Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .stats-container {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }
        .stat-card {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            padding: 20px;
            flex: 1;
            margin: 0 10px;
            text-align: center;
        }
        .stat-card h2 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        .stat-card p {
            font-size: 36px;
            font-weight: bold;
            margin: 0;
        }
        .reports-container {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .threat {
            color: #e74c3c;
            font-weight: bold;
        }
        .safe {
            color: #27ae60;
        }
        .loading {
            text-align: center;
            padding: 20px;
            font-style: italic;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <header>
        <h1>SBARDS Dashboard</h1>
        <p>Security-Based Automated Ransomware Detection System</p>
    </header>

    <div class="container">
        <div class="stats-container">
            <div class="stat-card">
                <h2>Total Scans</h2>
                <p id="total-scans">-</p>
            </div>
            <div class="stat-card">
                <h2>Files Scanned</h2>
                <p id="files-scanned">-</p>
            </div>
            <div class="stat-card">
                <h2>Threats Found</h2>
                <p id="threats-found">-</p>
            </div>
        </div>

        <div class="reports-container">
            <h2>Recent Scan Reports</h2>
            <div id="reports-table">
                <p class="loading">Loading reports...</p>
            </div>
        </div>
    </div>

    <script>
        // API URL
        const API_URL = window.location.origin;

        // Fetch statistics
        async function fetchStats() {
            try {
                const response = await fetch(`${API_URL}/api/stats/`);
                const data = await response.json();
                
                document.getElementById('total-scans').textContent = data.total_scans;
                document.getElementById('files-scanned').textContent = data.total_files_scanned;
                document.getElementById('threats-found').textContent = data.total_threats;
            } catch (error) {
                console.error('Error fetching stats:', error);
            }
        }

        // Fetch reports
        async function fetchReports() {
            try {
                const response = await fetch(`${API_URL}/api/reports/`);
                const reports = await response.json();
                
                const reportsTable = document.getElementById('reports-table');
                
                if (reports.length === 0) {
                    reportsTable.innerHTML = '<p>No reports found.</p>';
                    return;
                }
                
                let tableHTML = `
                    <table>
                        <thead>
                            <tr>
                                <th>Scan ID</th>
                                <th>Path</th>
                                <th>Files Scanned</th>
                                <th>Threats</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                
                reports.forEach(report => {
                    const date = new Date(report.timestamp).toLocaleString();
                    const threatClass = report.threats_found > 0 ? 'threat' : 'safe';
                    
                    tableHTML += `
                        <tr>
                            <td>${report.scan_id}</td>
                            <td>${report.scan_path}</td>
                            <td>${report.files_scanned}</td>
                            <td class="${threatClass}">${report.threats_found}</td>
                            <td>${date}</td>
                        </tr>
                    `;
                });
                
                tableHTML += `
                        </tbody>
                    </table>
                `;
                
                reportsTable.innerHTML = tableHTML;
            } catch (error) {
                console.error('Error fetching reports:', error);
                document.getElementById('reports-table').innerHTML = '<p>Error loading reports.</p>';
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            fetchStats();
            fetchReports();
            
            // Refresh data every 30 seconds
            setInterval(() => {
                fetchStats();
                fetchReports();
            }, 30000);
        });
    </script>
</body>
</html>
