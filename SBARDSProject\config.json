{"capture": {"directory": "captured_files", "temp_directory": "temp", "quarantine_directory": "quarantine", "max_file_size_mb": 100, "min_file_size_bytes": 1, "allowed_extensions": [], "blocked_extensions": []}, "scanner": {"target_directory": "samples", "recursive": true, "max_depth": 5, "exclude_dirs": [], "exclude_extensions": [], "max_file_size_mb": 100}, "rules": {"rule_files": ["rules/custom_rules.yar"], "enable_categories": ["all"]}, "output": {"log_directory": "logs", "output_directory": "output", "json_output": true, "csv_output": false, "html_report": false, "log_level": "info"}, "performance": {"threads": 1, "batch_size": 10, "timeout_seconds": 30}, "features": {"monitor_downloads": true, "hash_optimization": true}, "static_analysis": {"entropy_threshold": 7.5, "virustotal": {"enabled": false, "api_key": ""}, "yara": {"rules_directory": "rules", "timeout_seconds": 30}}, "dynamic_analysis": {"analysis_timeout_seconds": 300, "sandbox_type": "docker", "docker": {"image": "ubuntu:latest", "memory_limit": "512m", "cpu_limit": "1.0"}}, "response": {"auto_quarantine": false, "notification_methods": ["log", "email"], "email": {"smtp_server": "", "smtp_port": 587, "username": "", "password": "", "recipients": []}}, "monitoring": {"enabled": true, "check_interval_seconds": 10, "monitors": ["osquery", "sysmon", "etw"]}}