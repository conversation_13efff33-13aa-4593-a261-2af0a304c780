"""
Monitoring API Models for SBARDS

This module provides API models for the Monitoring phase of the SBARDS project.
These models define the structure of data exchanged through the API endpoints.
"""

from pydantic import BaseModel, Field, validator
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from enum import Enum

class AlertSeverity(str, Enum):
    """Alert severity levels."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    INFO = "info"
    LOW = "low"

class AlertType(str, Enum):
    """Common alert types."""
    PROCESS_CREATE = "process_create"
    PROCESS_TERMINATE = "process_terminate"
    FILE_CREATE = "file_create"
    FILE_CHANGE = "file_change"
    FILE_DELETE = "file_delete"
    NETWORK_CONNECTION = "network_connection"
    REGISTRY_CHANGE = "registry_change"
    SECURITY_EVENT = "security_event"
    SYSTEM_EVENT = "system_event"
    WMI_ACTIVITY = "wmi_activity"
    POWERSHELL_EXECUTION = "powershell_execution"
    CUSTOM = "custom"

class MonitoringStatus(BaseModel):
    """
    Monitoring status model.

    This model represents the current status of the monitoring system.
    """
    status: str = Field(..., description="Monitoring status (e.g., 'running', 'stopped', 'starting', 'stopping')")
    message: str = Field(..., description="Monitoring status message")

    class Config:
        schema_extra = {
            "example": {
                "status": "running",
                "message": "Monitoring is running"
            }
        }

class AlertResponse(BaseModel):
    """
    Alert response model.

    This model represents an alert generated by the monitoring system.
    """
    id: int = Field(..., description="Alert ID")
    source: str = Field(..., description="Alert source (e.g., 'SysmonMonitor', 'ETWMonitor')")
    type: str = Field(..., description="Alert type")
    message: str = Field(..., description="Alert message")
    severity: str = Field(..., description="Alert severity (critical, high, medium, info, low)")
    time: str = Field(..., description="Alert time (ISO format)")
    details: Dict[str, Any] = Field({}, description="Alert details")
    processed: bool = Field(False, description="Alert processed status")

    @validator('severity')
    def validate_severity(cls, v):
        """Validate severity value."""
        if v not in [s.value for s in AlertSeverity]:
            raise ValueError(f"Invalid severity: {v}")
        return v

    class Config:
        schema_extra = {
            "example": {
                "id": 1,
                "source": "SysmonMonitor",
                "type": "process_create",
                "message": "Process created: notepad.exe",
                "severity": "info",
                "time": "2023-05-21T12:34:56.789012",
                "details": {
                    "process_name": "notepad.exe",
                    "process_id": 1234,
                    "command_line": "notepad.exe",
                    "user": "SYSTEM"
                },
                "processed": False
            }
        }

class FileChangeResponse(BaseModel):
    """
    File change response model.

    This model represents a file change detected by the monitoring system.
    """
    file_path: str = Field(..., description="File path")
    size: int = Field(..., description="File size in bytes")
    mtime: float = Field(..., description="Last modified time (Unix timestamp)")
    first_seen: float = Field(..., description="First seen time (Unix timestamp)")
    last_change: float = Field(..., description="Last change time (Unix timestamp)")

    class Config:
        schema_extra = {
            "example": {
                "file_path": "C:\\Users\\<USER>\\Documents\\test.txt",
                "size": 1024,
                "mtime": 1621234567.89,
                "first_seen": 1621234567.89,
                "last_change": 1621234567.89
            }
        }

class ProcessInfo(BaseModel):
    """
    Process information model.

    This model represents information about a process.
    """
    name: str = Field(..., description="Process name")
    pid: int = Field(..., description="Process ID")
    path: str = Field(..., description="Process path")
    command_line: Optional[str] = Field(None, description="Command line")
    user: Optional[str] = Field(None, description="User")
    start_time: Optional[float] = Field(None, description="Start time (Unix timestamp)")

    class Config:
        schema_extra = {
            "example": {
                "name": "notepad.exe",
                "pid": 1234,
                "path": "C:\\Windows\\notepad.exe",
                "command_line": "notepad.exe",
                "user": "SYSTEM",
                "start_time": 1621234567.89
            }
        }

class NetworkConnectionInfo(BaseModel):
    """
    Network connection information model.

    This model represents information about a network connection.
    """
    source_ip: str = Field(..., description="Source IP address")
    source_port: int = Field(..., description="Source port")
    dest_ip: str = Field(..., description="Destination IP address")
    dest_port: int = Field(..., description="Destination port")
    protocol: str = Field(..., description="Protocol")
    process_name: Optional[str] = Field(None, description="Process name")
    process_id: Optional[int] = Field(None, description="Process ID")

    class Config:
        schema_extra = {
            "example": {
                "source_ip": "*************",
                "source_port": 12345,
                "dest_ip": "*******",
                "dest_port": 443,
                "protocol": "TCP",
                "process_name": "chrome.exe",
                "process_id": 1234
            }
        }

class MonitorDetailedStatus(BaseModel):
    """
    Detailed monitoring status model.

    This model represents detailed status information about the monitoring system.
    """
    is_running: bool = Field(..., description="Whether monitoring is running")
    platform: str = Field(..., description="Platform (e.g., 'windows', 'linux', 'macos')")
    monitors: Dict[str, Dict[str, Any]] = Field(..., description="Monitor status")
    monitor_whole_device: bool = Field(..., description="Whether monitoring the whole device")
    monitoring_interval: float = Field(..., description="Monitoring interval in seconds")
    alert_manager: Dict[str, Any] = Field(..., description="Alert manager status")
    file_changes: Dict[str, Any] = Field(..., description="File change tracking status")

    class Config:
        schema_extra = {
            "example": {
                "is_running": True,
                "platform": "windows",
                "monitors": {
                    "osquery": {
                        "is_running": True,
                        "name": "OSQueryMonitor",
                        "monitoring_interval": 2
                    },
                    "sysmon": {
                        "is_running": True,
                        "name": "SysmonMonitor",
                        "monitoring_interval": 2
                    }
                },
                "monitor_whole_device": True,
                "monitoring_interval": 2,
                "alert_manager": {
                    "is_running": True,
                    "alert_count": 10
                },
                "file_changes": {
                    "total_tracked_files": 5,
                    "recent_changes": 2
                }
            }
        }
