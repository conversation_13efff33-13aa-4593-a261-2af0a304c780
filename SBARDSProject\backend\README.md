# SBARDS Backend API

This is the backend API for the SBARDS (Security-Based Automated Ransomware Detection System) project. It provides a centralized service for collecting scan reports, integrating with VirusTotal, and providing a REST API for UIs, dashboards, and further integration.

## Features

- Collection of scan reports from SBARDS scanners
- Integration with VirusTotal for file hash verification
- Centralized logging and reporting
- REST API for UIs, dashboards, and further integration
- SQLite database for storing scan results (with support for other databases)
- Interactive dashboard for visualizing scan results
- Comprehensive API documentation with Swagger UI
- Robust error handling and validation
- Rate limiting to prevent abuse

## Installation

### Prerequisites

- Python 3.9 or higher
- pip (Python package manager)
- VirusTotal API key (for VirusTotal integration)
- Docker and Docker Compose (optional, for containerized deployment)

### Local Installation

1. Clone the repository:
   ```
   git clone https://github.com/SBARDSOrganization/SBARDSProject.git
   cd SBARDSProject/backend
   ```

2. Create a virtual environment (recommended):
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

4. Set up environment variables:
   ```
   cp .env.example .env
   # Edit .env file with your configuration
   ```

5. Run the backend server:
   ```
   python run_backend.py
   ```

6. Access the API documentation:
   ```
   http://localhost:8000/api/docs
   ```

7. Access the dashboard:
   ```
   http://localhost:8000/dashboard
   ```

### Docker Installation

1. Clone the repository:
   ```
   git clone https://github.com/SBARDSOrganization/SBARDSProject.git
   cd SBARDSProject/backend
   ```

2. Set up environment variables:
   ```
   cp .env.example .env
   # Edit .env file with your configuration
   ```

3. Build and run with Docker Compose:
   ```
   docker-compose up -d
   ```

4. Access the API documentation:
   ```
   http://localhost:8000/api/docs
   ```

5. Access the dashboard:
   ```
   http://localhost:8000/dashboard
   ```

## API Endpoints

### Dashboard
- `GET /`: Redirects to the dashboard
- `GET /dashboard`: Interactive dashboard for visualizing scan results

### API
- `GET /api`: API root endpoint with information about available endpoints
- `GET /api/docs`: Swagger UI documentation
- `GET /api/redoc`: ReDoc documentation
- `GET /api/health`: Health check endpoint

### Reports
- `POST /api/reports/`: Create a new scan report
- `GET /api/reports/`: Get all scan reports with pagination
- `GET /api/reports/{scan_id}`: Get a specific scan report by ID

### Files
- `POST /api/files/check`: Check a file against VirusTotal
- `POST /api/files/check-hash`: Check a file hash against VirusTotal

### Statistics
- `GET /api/stats/`: Get statistics about scans and threats

## Integration with SBARDS Scanner

The SBARDS scanner can be configured to send scan reports to the backend API using the `--send-to-backend` flag:

```
python run_scanner.py scan . --send-to-backend --backend-url=http://localhost:8000
```

You can also use the backend command to interact with the backend API:

```
python run_scanner.py backend --stats
python run_scanner.py backend --list-reports
python run_scanner.py backend --check-file path/to/file
```

## VirusTotal Integration

To enable VirusTotal integration, you need to set the `VIRUSTOTAL_API_KEY` environment variable:

```
export VIRUSTOTAL_API_KEY=your_api_key_here
```

You can get a free API key by signing up at [VirusTotal](https://www.virustotal.com/).

The backend will automatically check file hashes against VirusTotal when:
1. A scan report is submitted with files marked as threats
2. A file is uploaded for checking via the API
3. A file hash is submitted for checking via the API

## Database

The backend uses SQLite by default, with the database file stored in the `data` directory. You can change the database URL by setting the `DATABASE_URL` environment variable.

Supported databases:
- SQLite (default): `sqlite:///./sbards.db`
- PostgreSQL: `postgresql://user:password@localhost/sbards`
- MySQL: `mysql://user:password@localhost/sbards`

## Configuration

The backend can be configured using environment variables or command-line arguments:

```
python run_backend.py --help
```

Available options:
- `--host`: Host to bind the server to (default: 0.0.0.0)
- `--port`: Port to bind the server to (default: 8000)
- `--reload`: Enable auto-reload for development
- `--log-level`: Logging level (default: info)
- `--env-file`: Path to .env file (default: .env)
- `--database-url`: Database URL (overrides environment variable)
- `--virustotal-api-key`: VirusTotal API key (overrides environment variable)

## License

This project is licensed under the MIT License - see the LICENSE file for details.
