"""
Network Monitor for SBARDS

This module provides network monitoring for the monitoring phase of the SBARDS project.
"""

import os
import time
import logging
import threading
from typing import Dict, List, Any, Optional, Set

class NetworkMonitor:
    """
    Network monitor for the monitoring phase.
    
    This class provides mechanisms for:
    1. Monitoring network connections
    2. Detecting suspicious connections
    3. Tracking connection changes
    4. Monitoring network traffic
    """
    
    def __init__(self, config: Dict[str, Any], alert_manager):
        """
        Initialize network monitor.
        
        Args:
            config (Dict[str, Any]): Network monitoring configuration
            alert_manager: Alert manager
        """
        self.config = config
        self.enabled = config.get("enabled", True)
        self.check_interval_seconds = config.get("check_interval_seconds", 5)
        self.suspicious_ports = config.get("suspicious_ports", [])
        self.suspicious_addresses = config.get("suspicious_addresses", [])
        self.detect_new_connections = config.get("detect_new_connections", True)
        self.detect_connection_spikes = config.get("detect_connection_spikes", True)
        self.connection_spike_threshold = config.get("connection_spike_threshold", 10)
        self.connection_spike_time_window_seconds = config.get("connection_spike_time_window_seconds", 10)
        
        # Set up logging
        self.logger = logging.getLogger("NetworkMonitor")
        
        # Set alert manager
        self.alert_manager = alert_manager
        
        # Initialize connection storage
        self.connections = {}
        self.connection_history = []
        self.monitored_connections = set()
        self.connection_count = 0
        
        # Initialize monitoring state
        self.is_running_flag = False
        self.stop_event = threading.Event()
        self.monitoring_thread = None
    
    def start(self) -> bool:
        """
        Start network monitoring.
        
        Returns:
            bool: True if started successfully, False otherwise
        """
        if not self.enabled:
            self.logger.warning("Network monitoring is not enabled in configuration")
            return False
        
        if self.is_running_flag:
            self.logger.warning("Network monitoring is already running")
            return True
        
        self.logger.info("Starting network monitoring")
        
        try:
            # Reset stop event
            self.stop_event.clear()
            
            # Start monitoring thread
            self.monitoring_thread = threading.Thread(
                target=self._monitoring_loop,
                daemon=True
            )
            self.monitoring_thread.start()
            
            self.is_running_flag = True
            self.logger.info("Network monitoring started")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error starting network monitoring: {e}")
            return False
    
    def stop(self) -> bool:
        """
        Stop network monitoring.
        
        Returns:
            bool: True if stopped successfully, False otherwise
        """
        if not self.is_running_flag:
            self.logger.warning("Network monitoring is not running")
            return True
        
        self.logger.info("Stopping network monitoring")
        
        try:
            # Set stop event
            self.stop_event.set()
            
            # Wait for monitoring thread to stop
            if self.monitoring_thread:
                self.monitoring_thread.join(timeout=5.0)
            
            self.is_running_flag = False
            self.logger.info("Network monitoring stopped")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error stopping network monitoring: {e}")
            return False
    
    def is_running(self) -> bool:
        """
        Check if network monitoring is running.
        
        Returns:
            bool: True if running, False otherwise
        """
        return self.is_running_flag
    
    def _monitoring_loop(self) -> None:
        """Network monitoring loop."""
        while not self.stop_event.is_set():
            try:
                # Get current connections
                self._update_connections()
                
                # Check for suspicious connections
                self._check_suspicious_connections()
                
                # Check for connection spikes
                if self.detect_connection_spikes:
                    self._check_connection_spikes()
                
                # Wait for next check
                self.stop_event.wait(self.check_interval_seconds)
                
            except Exception as e:
                self.logger.error(f"Error in network monitoring loop: {e}")
                # Wait a bit before retrying
                self.stop_event.wait(1.0)
    
    def _update_connections(self) -> None:
        """Update connection list."""
        try:
            import psutil
            
            # Get current connections
            current_connections = {}
            
            for conn in psutil.net_connections(kind='inet'):
                try:
                    # Get connection info
                    if conn.laddr and conn.raddr:
                        # Create connection key
                        key = f"{conn.laddr.ip}:{conn.laddr.port}-{conn.raddr.ip}:{conn.raddr.port}"
                        
                        # Add to current connections
                        current_connections[key] = {
                            "id": key,
                            "local_address": conn.laddr.ip,
                            "local_port": conn.laddr.port,
                            "remote_address": conn.raddr.ip,
                            "remote_port": conn.raddr.port,
                            "status": conn.status,
                            "pid": conn.pid,
                            "timestamp": time.time()
                        }
                        
                        # Check if connection is new
                        if key not in self.connections:
                            # Add to connection history
                            self.connection_history.append({
                                "event": "created",
                                "id": key,
                                "local_address": conn.laddr.ip,
                                "local_port": conn.laddr.port,
                                "remote_address": conn.raddr.ip,
                                "remote_port": conn.raddr.port,
                                "timestamp": time.time()
                            })
                            
                            # Keep history size limited
                            if len(self.connection_history) > 1000:
                                self.connection_history.pop(0)
                            
                            # Check for new connections
                            if self.detect_new_connections:
                                self._check_new_connection(key, current_connections[key])
                except Exception as e:
                    self.logger.debug(f"Error processing connection: {e}")
            
            # Check for closed connections
            for key in list(self.connections.keys()):
                if key not in current_connections:
                    # Add to connection history
                    self.connection_history.append({
                        "event": "closed",
                        "id": key,
                        "local_address": self.connections[key]["local_address"],
                        "local_port": self.connections[key]["local_port"],
                        "remote_address": self.connections[key]["remote_address"],
                        "remote_port": self.connections[key]["remote_port"],
                        "timestamp": time.time()
                    })
                    
                    # Keep history size limited
                    if len(self.connection_history) > 1000:
                        self.connection_history.pop(0)
                    
                    # Remove from connections
                    del self.connections[key]
                    
                    # Remove from monitored connections
                    self.monitored_connections.discard(key)
            
            # Update connections
            self.connections = current_connections
            self.connection_count = len(self.connections)
            
        except ImportError:
            self.logger.error("psutil module not found. Network monitoring requires psutil.")
            self.stop()
        except Exception as e:
            self.logger.error(f"Error updating connections: {e}")
    
    def _check_new_connection(self, key: str, connection: Dict[str, Any]) -> None:
        """
        Check a new connection.
        
        Args:
            key (str): Connection key
            connection (Dict[str, Any]): Connection info
        """
        # Check for suspicious ports
        if connection["remote_port"] in self.suspicious_ports:
            # Create alert
            self.alert_manager.add_alert(
                level="warning",
                source="network_monitor",
                message=f"Connection to suspicious port: {connection['remote_address']}:{connection['remote_port']}",
                details={
                    "local_address": connection["local_address"],
                    "local_port": connection["local_port"],
                    "remote_address": connection["remote_address"],
                    "remote_port": connection["remote_port"],
                    "status": connection["status"],
                    "pid": connection["pid"]
                }
            )
        
        # Check for suspicious addresses
        for address in self.suspicious_addresses:
            if address in connection["remote_address"]:
                # Create alert
                self.alert_manager.add_alert(
                    level="warning",
                    source="network_monitor",
                    message=f"Connection to suspicious address: {connection['remote_address']}",
                    details={
                        "local_address": connection["local_address"],
                        "local_port": connection["local_port"],
                        "remote_address": connection["remote_address"],
                        "remote_port": connection["remote_port"],
                        "status": connection["status"],
                        "pid": connection["pid"]
                    }
                )
    
    def _check_suspicious_connections(self) -> None:
        """Check for suspicious connections."""
        for key, connection in self.connections.items():
            # Check monitored connections
            if key in self.monitored_connections:
                # Create alert
                self.alert_manager.add_alert(
                    level="info",
                    source="network_monitor",
                    message=f"Monitored connection active: {connection['local_address']}:{connection['local_port']} -> {connection['remote_address']}:{connection['remote_port']}",
                    details=connection
                )
    
    def _check_connection_spikes(self) -> None:
        """Check for connection spikes."""
        # Get recent connections
        now = time.time()
        window_start = now - self.connection_spike_time_window_seconds
        
        # Count new connections in the time window
        recent_connections = [
            conn for conn in self.connection_history
            if conn["event"] == "created" and conn["timestamp"] >= window_start
        ]
        
        # Check if threshold is exceeded
        if len(recent_connections) >= self.connection_spike_threshold:
            # Create alert
            self.alert_manager.add_alert(
                level="warning",
                source="network_monitor",
                message=f"Connection spike detected: {len(recent_connections)} new connections in {self.connection_spike_time_window_seconds} seconds",
                details={
                    "count": len(recent_connections),
                    "time_window_seconds": self.connection_spike_time_window_seconds,
                    "recent_connections": [
                        f"{conn['local_address']}:{conn['local_port']} -> {conn['remote_address']}:{conn['remote_port']}"
                        for conn in recent_connections[:10]
                    ]
                }
            )
    
    def get_current_connections(self) -> List[Dict[str, Any]]:
        """
        Get current connections.
        
        Returns:
            List[Dict[str, Any]]: Current connections
        """
        return list(self.connections.values())
    
    def get_connection_count(self) -> int:
        """
        Get connection count.
        
        Returns:
            int: Connection count
        """
        return self.connection_count
    
    def monitor_connection(self, local_address: str, local_port: int, remote_address: str, remote_port: int) -> None:
        """
        Monitor a specific connection.
        
        Args:
            local_address (str): Local address
            local_port (int): Local port
            remote_address (str): Remote address
            remote_port (int): Remote port
        """
        if not self.is_running_flag:
            self.logger.warning("Network monitoring is not running")
            return
        
        # Create connection key
        key = f"{local_address}:{local_port}-{remote_address}:{remote_port}"
        
        # Add to monitored connections
        self.monitored_connections.add(key)
        
        self.logger.info(f"Monitoring connection: {local_address}:{local_port} -> {remote_address}:{remote_port}")
        
        # Check if connection exists
        if key in self.connections:
            self.logger.info(f"Connection {key} is being monitored")
        else:
            self.logger.warning(f"Connection not found: {key}")
