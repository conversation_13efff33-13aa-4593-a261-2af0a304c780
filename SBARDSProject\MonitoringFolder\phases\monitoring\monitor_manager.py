"""
Monitor Manager for SBARDS

This module provides the main monitoring manager for the SBARDS project.
"""

import os
import time
import logging
import threading
import platform
from typing import Dict, List, Any, Optional, Set

class MonitorManager:
    """
    Monitor Manager for SBARDS.
    
    This class manages all monitoring components and coordinates their activities.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the monitor manager.
        
        Args:
            config (Dict[str, Any]): Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger("SBARDS.MonitorManager")
        
        # Platform detection
        self.platform = platform.system().lower()
        self.logger.info(f"Detected platform: {self.platform}")
        
        # Alert manager
        from phases.monitoring.alerts.alert_manager import AlertManager
        self.alert_manager = AlertManager(config)
        
        # Monitoring components
        self.monitors = {}
        
        # Initialize platform-specific monitors
        self._init_platform_monitors()
        
        # Monitoring thread
        self.monitoring_thread = None
        self.stop_event = threading.Event()
        self.is_running = False
        
        # Monitoring interval
        self.monitoring_interval = config.get("monitoring", {}).get("interval_seconds", 5)
        
        self.logger.info("Monitor Manager initialized")
        
    def _init_platform_monitors(self) -> None:
        """Initialize platform-specific monitors."""
        if self.platform == "windows":
            self._init_windows_monitors()
        elif self.platform == "linux":
            self._init_linux_monitors()
        else:
            self.logger.warning(f"Unsupported platform: {self.platform}")
            
    def _init_windows_monitors(self) -> None:
        """Initialize Windows-specific monitors."""
        # Import Windows-specific monitors
        from phases.monitoring.platforms.windows.osquery_monitor import OSQueryMonitor
        from phases.monitoring.platforms.windows.sysmon_monitor import SysmonMonitor
        from phases.monitoring.platforms.windows.etw_monitor import ETWMonitor
        
        # Initialize OSQuery monitor
        if self.config.get("monitoring", {}).get("enable_osquery", True):
            self.monitors["osquery"] = OSQueryMonitor(self.config, self.alert_manager)
            
        # Initialize Sysmon monitor
        if self.config.get("monitoring", {}).get("enable_sysmon", True):
            self.monitors["sysmon"] = SysmonMonitor(self.config, self.alert_manager)
            
        # Initialize ETW monitor
        if self.config.get("monitoring", {}).get("enable_etw", True):
            self.monitors["etw"] = ETWMonitor(self.config, self.alert_manager)
            
    def _init_linux_monitors(self) -> None:
        """Initialize Linux-specific monitors."""
        # Import Linux-specific monitors
        try:
            from phases.monitoring.platforms.linux.osquery_monitor import OSQueryMonitor
            from phases.monitoring.platforms.linux.auditd_monitor import AuditDMonitor
            from phases.monitoring.platforms.linux.syslog_monitor import SyslogMonitor
            from phases.monitoring.platforms.linux.zeek_monitor import ZeekMonitor
            
            # Initialize OSQuery monitor
            if self.config.get("monitoring", {}).get("enable_osquery", True):
                self.monitors["osquery"] = OSQueryMonitor(self.config, self.alert_manager)
                
            # Initialize AuditD monitor
            if self.config.get("monitoring", {}).get("enable_auditd", True):
                self.monitors["auditd"] = AuditDMonitor(self.config, self.alert_manager)
                
            # Initialize Syslog monitor
            if self.config.get("monitoring", {}).get("enable_syslog", True):
                self.monitors["syslog"] = SyslogMonitor(self.config, self.alert_manager)
                
            # Initialize Zeek monitor
            if self.config.get("monitoring", {}).get("enable_zeek", True):
                self.monitors["zeek"] = ZeekMonitor(self.config, self.alert_manager)
                
        except ImportError as e:
            self.logger.warning(f"Error importing Linux monitors: {e}")
            
    def start_monitoring(self) -> bool:
        """
        Start monitoring.
        
        Returns:
            bool: True if successful, False otherwise
        """
        if self.is_running:
            self.logger.warning("Monitoring is already running")
            return True
            
        self.logger.info("Starting monitoring")
        self.stop_event.clear()
        
        # Start alert manager
        self.alert_manager.start()
        
        # Start all monitors
        for name, monitor in self.monitors.items():
            try:
                self.logger.info(f"Starting monitor: {name}")
                monitor.start_monitoring(self.stop_event)
            except Exception as e:
                self.logger.error(f"Error starting monitor {name}: {e}")
        
        # Start monitoring thread
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self.monitoring_thread.start()
        
        self.is_running = True
        return True
        
    def stop_monitoring(self) -> bool:
        """
        Stop monitoring.
        
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.is_running:
            return True
            
        self.logger.info("Stopping monitoring")
        self.stop_event.set()
        
        # Stop all monitors
        for name, monitor in self.monitors.items():
            try:
                self.logger.info(f"Stopping monitor: {name}")
                monitor.stop_monitoring()
            except Exception as e:
                self.logger.error(f"Error stopping monitor {name}: {e}")
        
        # Stop alert manager
        self.alert_manager.stop()
        
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=10.0)
            
        self.is_running = False
        return True
        
    def _monitoring_loop(self) -> None:
        """Monitoring loop."""
        while not self.stop_event.is_set():
            try:
                # Check monitor status
                for name, monitor in self.monitors.items():
                    if hasattr(monitor, "is_running") and not monitor.is_running:
                        self.logger.warning(f"Monitor {name} is not running, attempting to restart")
                        try:
                            monitor.start_monitoring(self.stop_event)
                        except Exception as e:
                            self.logger.error(f"Error restarting monitor {name}: {e}")
                
                # Wait for next monitoring cycle
                self.stop_event.wait(self.monitoring_interval)
                
            except Exception as e:
                self.logger.error(f"Error during monitoring: {e}")
                # Wait a bit before retrying
                self.stop_event.wait(1.0)
                
        self.logger.info("Monitoring stopped")
        
    def get_status(self) -> Dict[str, Any]:
        """
        Get monitoring status.
        
        Returns:
            Dict[str, Any]: Monitoring status
        """
        status = {
            "is_running": self.is_running,
            "platform": self.platform,
            "monitors": {}
        }
        
        # Get status of all monitors
        for name, monitor in self.monitors.items():
            status["monitors"][name] = {
                "is_running": getattr(monitor, "is_running", False)
            }
            
        # Get alert manager status
        status["alert_manager"] = {
            "is_running": self.alert_manager.is_running,
            "alert_count": len(self.alert_manager.alerts)
        }
        
        return status
        
    def get_alerts(self, limit: int = 100, severity: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get alerts.
        
        Args:
            limit (int): Maximum number of alerts to return
            severity (Optional[str]): Filter alerts by severity
            
        Returns:
            List[Dict[str, Any]]: List of alerts
        """
        return self.alert_manager.get_alerts(limit, severity)
        
    def add_threats(self, threats: List[Dict[str, Any]]) -> None:
        """
        Add threats detected by the pre-scanning phase.
        
        Args:
            threats (List[Dict[str, Any]]): List of threats
        """
        for threat in threats:
            self.alert_manager.add_alert(
                source="PreScanning",
                alert_type="yara_match",
                message=f"YARA match: {threat.get('rule', 'unknown')} in {threat.get('file_path', 'unknown')}",
                severity=threat.get('severity', 'warning'),
                details=threat
            )
