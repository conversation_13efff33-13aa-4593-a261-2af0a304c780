"""
Tests for the SBARDS Backend API.
"""

import os
import sys
import pytest
from fastapi.testclient import TestClient

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.main import app

client = TestClient(app)

def test_root():
    """Test the root endpoint."""
    response = client.get("/")
    assert response.status_code == 200
    assert response.json() == {"message": "SBARDS Backend API"}

def test_stats():
    """Test the stats endpoint."""
    response = client.get("/api/stats/")
    assert response.status_code == 200
    assert "total_scans" in response.json()
    assert "total_files_scanned" in response.json()
    assert "total_threats" in response.json()

def test_reports_list():
    """Test the reports list endpoint."""
    response = client.get("/api/reports/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)

def test_create_report():
    """Test creating a report."""
    report_data = {
        "scan_id": "test_scan_123",
        "scan_path": "/test/path",
        "files_scanned": 10,
        "threats_found": 2,
        "report_path": "/test/path/report.html",
        "report_content": "<html><body>Test Report</body></html>",
        "file_results": [
            {
                "file_path": "/test/path/file1.txt",
                "file_hash": "abcdef1234567890",
                "is_threat": True,
                "threat_type": "Test Threat"
            }
        ]
    }
    
    response = client.post("/api/reports/", json=report_data)
    assert response.status_code == 201
    assert response.json()["scan_id"] == "test_scan_123"
    assert response.json()["files_scanned"] == 10
    assert response.json()["threats_found"] == 2

if __name__ == "__main__":
    pytest.main(["-xvs", __file__])
