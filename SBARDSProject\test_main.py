#!/usr/bin/env python3
"""
Test script for the new main.py functionality

This script tests the consolidated main.py interface to ensure all phases work correctly.
"""

import sys
import os
import tempfile
import subprocess
from pathlib import Path

def create_test_file():
    """Create a test file for analysis."""
    test_content = b"""
This is a test file for SBARDS analysis.
It contains some text that should be analyzed by the system.
The file is harmless and used only for testing purposes.
"""
    
    # Create temporary test file
    with tempfile.NamedTemporaryFile(mode='wb', delete=False, suffix='.txt') as f:
        f.write(test_content)
        return f.name

def run_command(cmd):
    """Run a command and return the result."""
    try:
        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Command timed out"
    except Exception as e:
        return -1, "", str(e)

def test_help():
    """Test help functionality."""
    print("\n=== Testing Help ===")
    
    # Test main help
    returncode, stdout, stderr = run_command([sys.executable, "main.py", "--help"])
    
    if returncode == 0:
        print("✓ Help command works")
        if "SBARDS" in stdout and "phases" in stdout.lower():
            print("✓ Help content looks correct")
        else:
            print("⚠ Help content may be incomplete")
    else:
        print(f"✗ Help command failed: {stderr}")
    
    return returncode == 0

def test_phase_validation():
    """Test phase validation."""
    print("\n=== Testing Phase Validation ===")
    
    # Test invalid phase
    returncode, stdout, stderr = run_command([sys.executable, "main.py", "--phase", "invalid"])
    
    if returncode != 0:
        print("✓ Invalid phase correctly rejected")
    else:
        print("⚠ Invalid phase was accepted (should be rejected)")
    
    return True

def test_file_requirement():
    """Test file requirement for analysis phases."""
    print("\n=== Testing File Requirement ===")
    
    # Test static phase without file
    returncode, stdout, stderr = run_command([sys.executable, "main.py", "--phase", "static"])
    
    if returncode != 0 and ("file" in stderr.lower() or "file" in stdout.lower()):
        print("✓ File requirement correctly enforced")
        return True
    else:
        print("⚠ File requirement not properly enforced")
        return False

def test_capture_phase():
    """Test capture phase."""
    print("\n=== Testing Capture Phase ===")
    
    test_file = create_test_file()
    
    try:
        returncode, stdout, stderr = run_command([
            sys.executable, "main.py", 
            "--phase", "capture", 
            "--file", test_file,
            "--log-level", "INFO"
        ])
        
        if returncode == 0:
            print("✓ Capture phase completed successfully")
            if "captured successfully" in stdout.lower():
                print("✓ Capture phase output looks correct")
                return True
            else:
                print("⚠ Capture phase output may be incomplete")
                return False
        else:
            print(f"✗ Capture phase failed: {stderr}")
            return False
            
    finally:
        # Cleanup test file
        try:
            os.unlink(test_file)
        except:
            pass

def test_prescanning_phase():
    """Test prescanning phase."""
    print("\n=== Testing Prescanning Phase ===")
    
    test_file = create_test_file()
    
    try:
        returncode, stdout, stderr = run_command([
            sys.executable, "main.py", 
            "--phase", "prescanning", 
            "--file", test_file,
            "--log-level", "INFO"
        ])
        
        if returncode == 0:
            print("✓ Prescanning phase completed successfully")
            if "pre-scanning completed" in stdout.lower():
                print("✓ Prescanning phase output looks correct")
                return True
            else:
                print("⚠ Prescanning phase output may be incomplete")
                return False
        else:
            print(f"✗ Prescanning phase failed: {stderr}")
            return False
            
    finally:
        # Cleanup test file
        try:
            os.unlink(test_file)
        except:
            pass

def test_static_phase():
    """Test static analysis phase."""
    print("\n=== Testing Static Analysis Phase ===")
    
    test_file = create_test_file()
    
    try:
        returncode, stdout, stderr = run_command([
            sys.executable, "main.py", 
            "--phase", "static", 
            "--file", test_file,
            "--log-level", "INFO"
        ])
        
        if returncode == 0:
            print("✓ Static analysis phase completed successfully")
            if "static analysis completed" in stdout.lower():
                print("✓ Static analysis phase output looks correct")
                return True
            else:
                print("⚠ Static analysis phase output may be incomplete")
                return False
        else:
            print(f"✗ Static analysis phase failed: {stderr}")
            return False
            
    finally:
        # Cleanup test file
        try:
            os.unlink(test_file)
        except:
            pass

def test_monitoring_phase():
    """Test monitoring phase (short duration)."""
    print("\n=== Testing Monitoring Phase ===")
    
    returncode, stdout, stderr = run_command([
        sys.executable, "main.py", 
        "--phase", "monitoring", 
        "--duration", "1",  # 1 minute only
        "--log-level", "INFO"
    ])
    
    if returncode == 0:
        print("✓ Monitoring phase completed successfully")
        if "monitoring completed" in stdout.lower():
            print("✓ Monitoring phase output looks correct")
            return True
        else:
            print("⚠ Monitoring phase output may be incomplete")
            return False
    else:
        print(f"✗ Monitoring phase failed: {stderr}")
        return False

def test_workflow_phase():
    """Test complete workflow phase."""
    print("\n=== Testing Complete Workflow Phase ===")
    
    test_file = create_test_file()
    
    try:
        returncode, stdout, stderr = run_command([
            sys.executable, "main.py", 
            "--phase", "workflow", 
            "--file", test_file,
            "--log-level", "INFO"
        ])
        
        if returncode == 0:
            print("✓ Complete workflow completed successfully")
            if "workflow finished" in stdout.lower():
                print("✓ Workflow phase output looks correct")
                return True
            else:
                print("⚠ Workflow phase output may be incomplete")
                return False
        else:
            print(f"✗ Complete workflow failed: {stderr}")
            return False
            
    finally:
        # Cleanup test file
        try:
            os.unlink(test_file)
        except:
            pass

def main():
    """Run all tests."""
    print("SBARDS Main.py Test Suite")
    print("=" * 50)
    
    # Check if main.py exists
    if not Path("main.py").exists():
        print("✗ main.py not found in current directory")
        return 1
    
    tests = [
        ("Help Functionality", test_help),
        ("Phase Validation", test_phase_validation),
        ("File Requirement", test_file_requirement),
        ("Capture Phase", test_capture_phase),
        ("Prescanning Phase", test_prescanning_phase),
        ("Static Analysis Phase", test_static_phase),
        ("Monitoring Phase", test_monitoring_phase),
        ("Complete Workflow", test_workflow_phase),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            if test_func():
                passed += 1
                print(f"✓ {test_name} PASSED")
            else:
                print(f"✗ {test_name} FAILED")
        except Exception as e:
            print(f"✗ {test_name} ERROR: {e}")
    
    print(f"\n{'='*50}")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! main.py is working correctly.")
        return 0
    else:
        print("⚠ Some tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
