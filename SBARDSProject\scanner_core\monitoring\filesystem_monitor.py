"""
Filesystem Monitor for SBARDS

This module provides filesystem monitoring capabilities for the SBARDS project.
"""

import os
import time
import math
import logging
import threading
import watchdog.observers
import watchdog.events
from typing import Dict, List, Any, Optional
from collections import deque, defaultdict
from datetime import datetime

class FilesystemMonitor:
    """
    Monitors filesystem for suspicious activity.
    
    This class provides capabilities to monitor file creation, modification,
    deletion, and other operations that may indicate malicious activity.
    """
    
    def __init__(self, config: Dict[str, Any], alert_manager):
        """
        Initialize the filesystem monitor.
        
        Args:
            config (Dict[str, Any]): Filesystem monitoring configuration
            alert_manager: Alert manager instance for generating alerts
        """
        self.config = config
        self.alert_manager = alert_manager
        self.logger = logging.getLogger("SBARDS.FilesystemMonitor")
        
        # Filesystem monitoring configuration
        self.watch_directories = config.get("watch_directories", ["samples"])
        self.detect_mass_operations = config.get("detect_mass_operations", True)
        self.mass_operation_threshold = config.get("mass_operation_threshold", 10)
        self.entropy_check = config.get("entropy_check", True)
        self.entropy_threshold = config.get("entropy_threshold", 7.8)  # High entropy threshold
        
        # File operation tracking
        self.file_operations = deque(maxlen=1000)
        self.recent_operations = defaultdict(int)  # Count operations by type
        self.operation_timestamps = defaultdict(list)  # Track timestamps for mass operation detection
        
        # Observers
        self.observers = []
        
        self.logger.info("Filesystem Monitor initialized")
        
    def start_monitoring(self, stop_event: threading.Event):
        """
        Start monitoring filesystem.
        
        Args:
            stop_event (threading.Event): Event to signal stopping
        """
        self.logger.info("Starting filesystem monitoring")
        
        # Create event handler
        event_handler = self._create_event_handler()
        
        # Start observers for each directory
        for directory in self.watch_directories:
            if os.path.exists(directory):
                observer = watchdog.observers.Observer()
                observer.schedule(event_handler, directory, recursive=True)
                observer.start()
                self.observers.append(observer)
                self.logger.info(f"Started monitoring directory: {directory}")
            else:
                self.logger.warning(f"Directory does not exist, skipping: {directory}")
                
        # Wait for stop event
        while not stop_event.is_set():
            # Check for mass operations periodically
            if self.detect_mass_operations:
                self._check_mass_operations()
                
            # Wait a bit before checking again
            stop_event.wait(1.0)
            
        # Stop all observers
        for observer in self.observers:
            observer.stop()
            
        for observer in self.observers:
            observer.join()
            
        self.logger.info("Filesystem monitoring stopped")
        
    def _create_event_handler(self):
        """Create filesystem event handler."""
        
        parent = self  # Reference to parent for inner class
        
        class FileSystemEventHandler(watchdog.events.FileSystemEventHandler):
            def on_created(self, event):
                if not event.is_directory:
                    parent._handle_file_event("created", event.src_path)
                    
            def on_modified(self, event):
                if not event.is_directory:
                    parent._handle_file_event("modified", event.src_path)
                    
            def on_deleted(self, event):
                if not event.is_directory:
                    parent._handle_file_event("deleted", event.src_path)
                    
            def on_moved(self, event):
                if not event.is_directory:
                    parent._handle_file_event("moved", event.dest_path, src_path=event.src_path)
                    
        return FileSystemEventHandler()
        
    def _handle_file_event(self, event_type: str, file_path: str, src_path: Optional[str] = None):
        """
        Handle a file event.
        
        Args:
            event_type (str): Type of event (created, modified, deleted, moved)
            file_path (str): Path to the file
            src_path (Optional[str]): Source path for move events
        """
        # Record the operation
        operation = {
            "timestamp": time.time(),
            "datetime": datetime.now().isoformat(),
            "type": event_type,
            "path": file_path
        }
        
        if src_path:
            operation["src_path"] = src_path
            
        self.file_operations.append(operation)
        
        # Update recent operations count
        self.recent_operations[event_type] += 1
        self.operation_timestamps[event_type].append(time.time())
        
        # Keep only recent timestamps (last minute)
        current_time = time.time()
        self.operation_timestamps[event_type] = [
            ts for ts in self.operation_timestamps[event_type]
            if current_time - ts < 60
        ]
        
        # Check file entropy if applicable
        if self.entropy_check and event_type in ["created", "modified"]:
            try:
                entropy = self._calculate_file_entropy(file_path)
                if entropy > self.entropy_threshold:
                    self.alert_manager.add_alert(
                        source="FilesystemMonitor",
                        alert_type="high_entropy_file",
                        message=f"High entropy file detected: {file_path}",
                        severity="warning",
                        details={
                            "file_path": file_path,
                            "entropy": entropy,
                            "threshold": self.entropy_threshold,
                            "event_type": event_type
                        }
                    )
                    self.logger.warning(f"High entropy file detected: {file_path} (entropy: {entropy:.2f})")
            except Exception as e:
                self.logger.debug(f"Error calculating entropy for {file_path}: {e}")
                
        self.logger.debug(f"File {event_type}: {file_path}")
        
    def _check_mass_operations(self):
        """Check for mass file operations (potential ransomware indicator)."""
        for operation_type, timestamps in self.operation_timestamps.items():
            # Check if we have enough operations to consider it "mass"
            if len(timestamps) >= self.mass_operation_threshold:
                # Check if they occurred within a short time window (10 seconds)
                current_time = time.time()
                recent_timestamps = [ts for ts in timestamps if current_time - ts < 10]
                
                if len(recent_timestamps) >= self.mass_operation_threshold:
                    self.alert_manager.add_alert(
                        source="FilesystemMonitor",
                        alert_type="mass_file_operations",
                        message=f"Mass file {operation_type} operations detected",
                        severity="critical",
                        details={
                            "operation_type": operation_type,
                            "count": len(recent_timestamps),
                            "threshold": self.mass_operation_threshold,
                            "time_window": "10 seconds"
                        }
                    )
                    self.logger.critical(
                        f"Mass file {operation_type} operations detected: "
                        f"{len(recent_timestamps)} in 10 seconds"
                    )
                    
                    # Clear the timestamps to prevent repeated alerts
                    self.operation_timestamps[operation_type] = []
                    
    def _calculate_file_entropy(self, file_path: str) -> float:
        """
        Calculate Shannon entropy of a file.
        
        Args:
            file_path (str): Path to the file
            
        Returns:
            float: Shannon entropy value (0-8)
        """
        # Check if file exists and is accessible
        if not os.path.exists(file_path) or not os.access(file_path, os.R_OK):
            return 0.0
            
        # Check file size
        try:
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                return 0.0
                
            # Read file in binary mode
            with open(file_path, 'rb') as f:
                data = f.read(min(file_size, 1024 * 1024))  # Read at most 1MB
                
            # Count byte frequencies
            byte_count = defaultdict(int)
            for byte in data:
                byte_count[byte] += 1
                
            # Calculate entropy
            entropy = 0.0
            for count in byte_count.values():
                probability = count / len(data)
                entropy -= probability * math.log2(probability)
                
            return entropy
            
        except Exception as e:
            self.logger.debug(f"Error calculating entropy: {e}")
            return 0.0
            
    def get_recent_operations(self, count: int = None) -> List[Dict[str, Any]]:
        """
        Get recent file operations.
        
        Args:
            count (int, optional): Number of operations to retrieve
            
        Returns:
            List[Dict[str, Any]]: List of recent file operations
        """
        if count is None:
            return list(self.file_operations)
        else:
            return list(self.file_operations)[-count:]
