"""
SBARDS Dynamic Analysis Layer (طبقة التحليل الديناميكي)

This module implements comprehensive dynamic analysis including:
- Sandbox execution (تشغيل الملف في Sandbox)
- API hooking and monitoring (مراقبة استدعاءات API)
- Behavioral analysis with ML (تحليل السلوكيات)
- Resource monitoring (مراقبة الموارد)
- Network activity monitoring (مراقبة النشاط الشبكي)
- File system monitoring (مراقبة نظام الملفات)
"""

import os
import logging
import json
import time
import threading
import subprocess
import psutil
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Set
from pathlib import Path
import docker
import tempfile
import shutil
from collections import defaultdict, deque


class DynamicAnalyzer:
    """
    Comprehensive Dynamic Analysis Engine for SBARDS

    Performs behavioral analysis of files in controlled environments.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the Dynamic Analyzer.

        Args:
            config (Dict[str, Any]): Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger("SBARDS.DynamicAnalyzer")

        # Dynamic analysis configuration
        self.dynamic_config = config.get("dynamic_analysis", {})

        # Analysis timeouts
        self.analysis_timeout = self.dynamic_config.get("analysis_timeout_seconds", 300)
        self.startup_timeout = self.dynamic_config.get("startup_timeout_seconds", 30)

        # Sandbox configuration
        self.sandbox_type = self.dynamic_config.get("sandbox_type", "docker")
        self.sandbox_image = self.dynamic_config.get("sandbox_image", "ubuntu:20.04")

        # Monitoring configuration
        self.monitor_network = self.dynamic_config.get("monitor_network", True)
        self.monitor_filesystem = self.dynamic_config.get("monitor_filesystem", True)
        self.monitor_processes = self.dynamic_config.get("monitor_processes", True)
        self.monitor_registry = self.dynamic_config.get("monitor_registry", True)

        # ML configuration
        self.ml_enabled = self.dynamic_config.get("ml_enabled", False)
        self.ml_model_path = self.dynamic_config.get("ml_model_path")

        # Initialize components
        self._init_sandbox()
        self._init_monitoring()
        self._init_ml_models()

        # Behavioral patterns
        self.ransomware_indicators = self._load_ransomware_indicators()
        self.malware_indicators = self._load_malware_indicators()

        # Analysis state
        self.current_analysis = None
        self.monitoring_active = False

    def _init_sandbox(self):
        """Initialize sandbox environment."""
        if self.sandbox_type == "docker":
            try:
                self.docker_client = docker.from_env()
                self.logger.info("Docker sandbox initialized")
            except Exception as e:
                self.logger.error(f"Failed to initialize Docker: {e}")
                self.docker_client = None
        else:
            self.docker_client = None
            self.logger.info("Using process-based sandbox")

    def _init_monitoring(self):
        """Initialize monitoring components."""
        self.network_connections = deque(maxlen=1000)
        self.file_operations = deque(maxlen=1000)
        self.process_events = deque(maxlen=1000)
        self.registry_operations = deque(maxlen=1000)

        # Resource monitoring
        self.resource_history = deque(maxlen=100)

    def _init_ml_models(self):
        """Initialize ML models for behavioral analysis."""
        self.ml_model = None
        if self.ml_enabled and self.ml_model_path:
            try:
                # Load ML model (placeholder for actual implementation)
                self.logger.info(f"Loading ML model from {self.ml_model_path}")
                # self.ml_model = joblib.load(self.ml_model_path)
            except Exception as e:
                self.logger.error(f"Failed to load ML model: {e}")

    def _load_ransomware_indicators(self) -> Dict[str, List[str]]:
        """Load ransomware behavioral indicators."""
        return {
            "file_operations": [
                "mass_file_encryption",
                "file_extension_changes",
                "shadow_copy_deletion",
                "backup_deletion",
                "ransom_note_creation"
            ],
            "network_operations": [
                "tor_communication",
                "cryptocurrency_addresses",
                "c2_communication",
                "data_exfiltration"
            ],
            "process_operations": [
                "privilege_escalation",
                "service_manipulation",
                "registry_modification",
                "process_injection"
            ],
            "system_operations": [
                "boot_record_modification",
                "system_file_modification",
                "security_software_termination"
            ]
        }

    def _load_malware_indicators(self) -> Dict[str, List[str]]:
        """Load general malware behavioral indicators."""
        return {
            "persistence": [
                "registry_autorun",
                "startup_folder",
                "scheduled_task",
                "service_installation"
            ],
            "evasion": [
                "anti_vm_checks",
                "anti_debug_checks",
                "code_injection",
                "process_hollowing"
            ],
            "communication": [
                "dns_tunneling",
                "http_beaconing",
                "encrypted_communication",
                "peer_to_peer"
            ]
        }

    def analyze_file(self, file_path: str, static_results: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Perform comprehensive dynamic analysis on a file.

        Args:
            file_path (str): Path to the file to analyze
            static_results (Optional[Dict[str, Any]]): Results from static analysis

        Returns:
            Dict[str, Any]: Complete dynamic analysis results
        """
        try:
            file_path = Path(file_path)

            if not file_path.exists():
                return {"error": f"File not found: {file_path}"}

            self.logger.info(f"Starting dynamic analysis of: {file_path}")

            # Initialize analysis session
            analysis_id = self._generate_analysis_id()
            self.current_analysis = {
                "id": analysis_id,
                "file_path": str(file_path),
                "start_time": datetime.now(),
                "static_results": static_results
            }

            # Initialize results
            results = {
                "analysis_id": analysis_id,
                "file_path": str(file_path),
                "timestamp": datetime.now().isoformat(),
                "analysis_duration": 0,
                "sandbox_type": self.sandbox_type
            }

            # Start monitoring
            self._start_monitoring()

            try:
                # Execute file in sandbox
                if self.sandbox_type == "docker" and self.docker_client:
                    execution_results = self._execute_in_docker(file_path)
                else:
                    execution_results = self._execute_in_process(file_path)

                results["execution"] = execution_results

                # Wait for analysis timeout
                time.sleep(self.analysis_timeout)

                # Stop monitoring and collect results
                monitoring_results = self._stop_monitoring()
                results["monitoring"] = monitoring_results

                # Behavioral analysis
                behavioral_results = self._analyze_behavior(monitoring_results)
                results["behavioral_analysis"] = behavioral_results

                # ML analysis (if enabled)
                if self.ml_enabled and self.ml_model:
                    ml_results = self._analyze_with_ml(monitoring_results)
                    results["ml_analysis"] = ml_results

                # Risk assessment
                risk_assessment = self._assess_dynamic_risk(results)
                results["risk_assessment"] = risk_assessment

                # Calculate analysis duration
                results["analysis_duration"] = (datetime.now() - self.current_analysis["start_time"]).total_seconds()

                self.logger.info(f"Dynamic analysis completed for: {file_path}")
                return results

            finally:
                # Ensure monitoring is stopped
                self._stop_monitoring()
                self._cleanup_analysis()

        except Exception as e:
            self.logger.error(f"Error during dynamic analysis of {file_path}: {e}")
            return {"error": str(e), "file_path": str(file_path)}

    def _generate_analysis_id(self) -> str:
        """Generate unique analysis ID."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        return f"dynamic_{timestamp}"

    def _start_monitoring(self):
        """Start all monitoring components."""
        self.monitoring_active = True

        # Clear previous data
        self.network_connections.clear()
        self.file_operations.clear()
        self.process_events.clear()
        self.registry_operations.clear()
        self.resource_history.clear()

        # Start monitoring threads
        if self.monitor_network:
            threading.Thread(target=self._monitor_network, daemon=True).start()

        if self.monitor_filesystem:
            threading.Thread(target=self._monitor_filesystem, daemon=True).start()

        if self.monitor_processes:
            threading.Thread(target=self._monitor_processes, daemon=True).start()

        # Start resource monitoring
        threading.Thread(target=self._monitor_resources, daemon=True).start()

        self.logger.info("Dynamic monitoring started")

    def _stop_monitoring(self) -> Dict[str, Any]:
        """Stop monitoring and return collected data."""
        self.monitoring_active = False

        # Collect all monitoring data
        monitoring_data = {
            "network_connections": list(self.network_connections),
            "file_operations": list(self.file_operations),
            "process_events": list(self.process_events),
            "registry_operations": list(self.registry_operations),
            "resource_usage": list(self.resource_history),
            "monitoring_duration": self.analysis_timeout
        }

        self.logger.info("Dynamic monitoring stopped")
        return monitoring_data

    def _monitor_network(self):
        """Monitor network connections and activity."""
        while self.monitoring_active:
            try:
                # Get current network connections
                connections = psutil.net_connections()

                for conn in connections:
                    if conn.status == 'ESTABLISHED':
                        connection_info = {
                            "timestamp": datetime.now().isoformat(),
                            "local_address": f"{conn.laddr.ip}:{conn.laddr.port}" if conn.laddr else "unknown",
                            "remote_address": f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else "unknown",
                            "status": conn.status,
                            "pid": conn.pid
                        }
                        self.network_connections.append(connection_info)

                time.sleep(1)  # Check every second

            except Exception as e:
                self.logger.error(f"Error monitoring network: {e}")
                time.sleep(5)

    def _monitor_filesystem(self):
        """Monitor file system operations."""
        # This is a simplified implementation
        # In a real scenario, you'd use tools like inotify on Linux or ReadDirectoryChangesW on Windows
        while self.monitoring_active:
            try:
                # Monitor specific directories for changes
                # This is a placeholder implementation
                time.sleep(2)

            except Exception as e:
                self.logger.error(f"Error monitoring filesystem: {e}")
                time.sleep(5)

    def _monitor_processes(self):
        """Monitor process creation and termination."""
        known_pids = set(psutil.pids())

        while self.monitoring_active:
            try:
                current_pids = set(psutil.pids())

                # New processes
                new_pids = current_pids - known_pids
                for pid in new_pids:
                    try:
                        proc = psutil.Process(pid)
                        process_info = {
                            "timestamp": datetime.now().isoformat(),
                            "event": "process_created",
                            "pid": pid,
                            "name": proc.name(),
                            "cmdline": " ".join(proc.cmdline()),
                            "parent_pid": proc.ppid()
                        }
                        self.process_events.append(process_info)
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass

                # Terminated processes
                terminated_pids = known_pids - current_pids
                for pid in terminated_pids:
                    process_info = {
                        "timestamp": datetime.now().isoformat(),
                        "event": "process_terminated",
                        "pid": pid
                    }
                    self.process_events.append(process_info)

                known_pids = current_pids
                time.sleep(2)

            except Exception as e:
                self.logger.error(f"Error monitoring processes: {e}")
                time.sleep(5)

    def _monitor_resources(self):
        """Monitor system resource usage."""
        while self.monitoring_active:
            try:
                resource_info = {
                    "timestamp": datetime.now().isoformat(),
                    "cpu_percent": psutil.cpu_percent(interval=1),
                    "memory_percent": psutil.virtual_memory().percent,
                    "disk_io": psutil.disk_io_counters()._asdict() if psutil.disk_io_counters() else {},
                    "network_io": psutil.net_io_counters()._asdict() if psutil.net_io_counters() else {}
                }
                self.resource_history.append(resource_info)

                time.sleep(5)  # Check every 5 seconds

            except Exception as e:
                self.logger.error(f"Error monitoring resources: {e}")
                time.sleep(10)

    def _execute_in_docker(self, file_path: Path) -> Dict[str, Any]:
        """Execute file in Docker sandbox."""
        try:
            # Create temporary directory for analysis
            temp_dir = Path(tempfile.mkdtemp())

            # Copy file to temp directory
            temp_file = temp_dir / file_path.name
            shutil.copy2(file_path, temp_file)

            # Run container
            container = self.docker_client.containers.run(
                self.sandbox_image,
                command=f"timeout {self.analysis_timeout} ./{file_path.name}",
                volumes={str(temp_dir): {'bind': '/analysis', 'mode': 'rw'}},
                working_dir='/analysis',
                detach=True,
                remove=True,
                network_mode='none'  # Isolate network
            )

            # Wait for container to finish
            result = container.wait(timeout=self.analysis_timeout + 30)
            logs = container.logs().decode('utf-8', errors='ignore')

            return {
                "success": True,
                "exit_code": result['StatusCode'],
                "logs": logs,
                "execution_time": self.analysis_timeout
            }

        except Exception as e:
            self.logger.error(f"Error executing in Docker: {e}")
            return {
                "success": False,
                "error": str(e)
            }
        finally:
            # Cleanup
            if 'temp_dir' in locals():
                shutil.rmtree(temp_dir, ignore_errors=True)

    def _execute_in_process(self, file_path: Path) -> Dict[str, Any]:
        """Execute file in isolated process (less secure than Docker)."""
        try:
            # This is a simplified implementation
            # In production, you'd want more sophisticated sandboxing

            start_time = time.time()

            # Try to execute the file
            process = subprocess.Popen(
                [str(file_path)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                timeout=self.analysis_timeout
            )

            stdout, stderr = process.communicate(timeout=self.analysis_timeout)
            execution_time = time.time() - start_time

            return {
                "success": True,
                "exit_code": process.returncode,
                "stdout": stdout.decode('utf-8', errors='ignore'),
                "stderr": stderr.decode('utf-8', errors='ignore'),
                "execution_time": execution_time
            }

        except subprocess.TimeoutExpired:
            process.kill()
            return {
                "success": False,
                "error": "Execution timeout",
                "execution_time": self.analysis_timeout
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

    def _analyze_behavior(self, monitoring_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze behavioral patterns from monitoring data."""
        behavioral_analysis = {
            "ransomware_indicators": [],
            "malware_indicators": [],
            "suspicious_activities": [],
            "risk_score": 0
        }

        # Analyze network behavior
        network_analysis = self._analyze_network_behavior(monitoring_data.get("network_connections", []))
        behavioral_analysis["network_behavior"] = network_analysis

        # Analyze process behavior
        process_analysis = self._analyze_process_behavior(monitoring_data.get("process_events", []))
        behavioral_analysis["process_behavior"] = process_analysis

        # Analyze resource usage
        resource_analysis = self._analyze_resource_behavior(monitoring_data.get("resource_usage", []))
        behavioral_analysis["resource_behavior"] = resource_analysis

        # Check for ransomware indicators
        ransomware_score = self._check_ransomware_indicators(monitoring_data)
        behavioral_analysis["ransomware_score"] = ransomware_score

        # Calculate overall behavioral risk
        behavioral_analysis["risk_score"] = self._calculate_behavioral_risk(behavioral_analysis)

        return behavioral_analysis

    def _analyze_network_behavior(self, connections: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze network connection patterns."""
        analysis = {
            "total_connections": len(connections),
            "unique_remote_ips": set(),
            "suspicious_ports": [],
            "suspicious_ips": []
        }

        for conn in connections:
            remote_addr = conn.get("remote_address", "")
            if ":" in remote_addr:
                ip, port = remote_addr.split(":")
                analysis["unique_remote_ips"].add(ip)

                # Check for suspicious ports
                if int(port) in [4444, 8080, 1337, 31337, 6666]:
                    analysis["suspicious_ports"].append(port)

        analysis["unique_remote_ips"] = len(analysis["unique_remote_ips"])

        return analysis

    def _analyze_process_behavior(self, events: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze process creation and behavior patterns."""
        analysis = {
            "processes_created": 0,
            "processes_terminated": 0,
            "suspicious_processes": [],
            "privilege_escalation_attempts": 0
        }

        for event in events:
            if event.get("event") == "process_created":
                analysis["processes_created"] += 1

                # Check for suspicious process names
                name = event.get("name", "").lower()
                cmdline = event.get("cmdline", "").lower()

                suspicious_patterns = ["powershell", "cmd", "regsvr32", "rundll32", "wscript", "cscript"]
                if any(pattern in name or pattern in cmdline for pattern in suspicious_patterns):
                    analysis["suspicious_processes"].append({
                        "name": name,
                        "cmdline": cmdline,
                        "pid": event.get("pid")
                    })

            elif event.get("event") == "process_terminated":
                analysis["processes_terminated"] += 1

        return analysis

    def _analyze_resource_behavior(self, resource_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze resource usage patterns."""
        if not resource_data:
            return {"error": "No resource data available"}

        cpu_usage = [r.get("cpu_percent", 0) for r in resource_data]
        memory_usage = [r.get("memory_percent", 0) for r in resource_data]

        analysis = {
            "avg_cpu_usage": sum(cpu_usage) / len(cpu_usage),
            "max_cpu_usage": max(cpu_usage),
            "avg_memory_usage": sum(memory_usage) / len(memory_usage),
            "max_memory_usage": max(memory_usage),
            "resource_spikes": 0
        }

        # Count resource spikes
        for cpu, memory in zip(cpu_usage, memory_usage):
            if cpu > 80 or memory > 80:
                analysis["resource_spikes"] += 1

        return analysis

    def _check_ransomware_indicators(self, monitoring_data: Dict[str, Any]) -> int:
        """Check for specific ransomware behavioral indicators."""
        score = 0

        # Check process events for ransomware-like behavior
        process_events = monitoring_data.get("process_events", [])
        for event in process_events:
            cmdline = event.get("cmdline", "").lower()

            # Check for shadow copy deletion
            if "vssadmin delete shadows" in cmdline or "wmic shadowcopy delete" in cmdline:
                score += 50

            # Check for backup deletion
            if "wbadmin delete" in cmdline:
                score += 30

            # Check for encryption-related commands
            if any(term in cmdline for term in ["encrypt", "cipher", "bcdedit"]):
                score += 20

        return min(score, 100)  # Cap at 100

    def _calculate_behavioral_risk(self, analysis: Dict[str, Any]) -> int:
        """Calculate overall behavioral risk score."""
        risk_score = 0

        # Network behavior risk
        network = analysis.get("network_behavior", {})
        if network.get("suspicious_ports"):
            risk_score += 20
        if network.get("unique_remote_ips", 0) > 10:
            risk_score += 15

        # Process behavior risk
        process = analysis.get("process_behavior", {})
        if process.get("suspicious_processes"):
            risk_score += len(process["suspicious_processes"]) * 10

        # Resource behavior risk
        resource = analysis.get("resource_behavior", {})
        if resource.get("max_cpu_usage", 0) > 90:
            risk_score += 15
        if resource.get("resource_spikes", 0) > 5:
            risk_score += 10

        # Ransomware indicators
        ransomware_score = analysis.get("ransomware_score", 0)
        risk_score += ransomware_score

        return min(risk_score, 100)  # Cap at 100

    def _analyze_with_ml(self, monitoring_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze behavior using ML models."""
        # Placeholder for ML analysis
        # In a real implementation, you'd extract features and use trained models
        return {
            "ml_enabled": True,
            "model_prediction": "benign",  # placeholder
            "confidence": 0.85,
            "features_analyzed": len(monitoring_data)
        }

    def _assess_dynamic_risk(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Assess overall dynamic analysis risk."""
        risk_score = 0
        risk_factors = []

        # Execution results
        execution = results.get("execution", {})
        if not execution.get("success", False):
            risk_score += 10
            risk_factors.append("Execution failed")

        # Behavioral analysis
        behavioral = results.get("behavioral_analysis", {})
        behavioral_risk = behavioral.get("risk_score", 0)
        risk_score += behavioral_risk * 0.8  # Weight behavioral analysis

        if behavioral.get("ransomware_score", 0) > 30:
            risk_factors.append("Ransomware indicators detected")

        if behavioral.get("network_behavior", {}).get("suspicious_ports"):
            risk_factors.append("Suspicious network activity")

        if behavioral.get("process_behavior", {}).get("suspicious_processes"):
            risk_factors.append("Suspicious process behavior")

        # ML analysis
        ml_analysis = results.get("ml_analysis", {})
        if ml_analysis.get("model_prediction") == "malicious":
            risk_score += 30
            risk_factors.append("ML model detected malicious behavior")

        # Determine risk level
        if risk_score >= 70:
            risk_level = "HIGH"
            recommendation = "QUARANTINE_IMMEDIATELY"
        elif risk_score >= 40:
            risk_level = "MEDIUM"
            recommendation = "ENHANCED_MONITORING"
        elif risk_score >= 20:
            risk_level = "LOW"
            recommendation = "CONTINUE_MONITORING"
        else:
            risk_level = "MINIMAL"
            recommendation = "ALLOW"

        return {
            "risk_score": min(int(risk_score), 100),
            "risk_level": risk_level,
            "recommendation": recommendation,
            "risk_factors": risk_factors
        }

    def _cleanup_analysis(self):
        """Clean up analysis resources."""
        self.current_analysis = None
        self.monitoring_active = False


def DynamicAnalyze(file_path: str, config: Dict[str, Any],
                  static_results: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Main dynamic analysis function for backward compatibility.

    Args:
        file_path (str): Path to file to analyze
        config (Dict[str, Any]): Configuration dictionary
        static_results (Optional[Dict[str, Any]]): Static analysis results

    Returns:
        Dict[str, Any]: Dynamic analysis results
    """
    analyzer = DynamicAnalyzer(config)
    return analyzer.analyze_file(file_path, static_results)