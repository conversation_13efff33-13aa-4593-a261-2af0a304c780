"""
Monitoring API Router for SBARDS

This module provides API endpoints for the Monitoring phase of the SBARDS project.
It includes endpoints for starting and stopping monitoring, retrieving monitoring status,
alerts, file changes, processes, and network connections.
"""

import os
import logging
import time
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Request, Query, Path
from fastapi.responses import JSONResponse
from typing import Dict, List, Any, Optional, Union

# Import models
from api.models.monitoring import (
    MonitoringStatus, AlertResponse, FileChangeResponse,
    ProcessInfo, NetworkConnectionInfo, MonitorDetailedStatus,
    AlertSeverity, AlertType
)

# Import security
from api.security import get_api_key, get_optional_api_key

# Create router
router = APIRouter(
    prefix="/api/monitoring",
    tags=["monitoring"],
    responses={
        404: {"description": "Not found"},
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        500: {"description": "Internal server error"}
    }
)

# Global variables
monitoring_manager = None

# Dependency to get the manager
def get_manager(request: Request):
    """
    Get the Monitoring manager.

    Args:
        request (Request): FastAPI request

    Returns:
        Any: Monitoring manager
    """
    if hasattr(request.state, "monitor_manager") and request.state.monitor_manager is not None:
        return request.state.monitor_manager

    if monitoring_manager is None:
        raise HTTPException(status_code=503, detail="Monitoring manager not initialized")

    return monitoring_manager

# Set the manager
def set_manager(manager):
    """
    Set the Monitoring manager.

    Args:
        manager: Monitoring manager
    """
    global monitoring_manager
    monitoring_manager = manager

# Endpoints
@router.post("/start", response_model=MonitoringStatus, tags=["monitoring"])
async def start_monitoring(
    background_tasks: BackgroundTasks,
    manager = Depends(get_manager),
    api_key: str = Depends(get_api_key)
):
    """
    Start monitoring.

    This endpoint starts the monitoring system. The monitoring process runs in the background.

    Args:
        background_tasks (BackgroundTasks): Background tasks
        manager: Monitoring manager
        api_key: API key for authentication

    Returns:
        MonitoringStatus: Monitoring status
    """
    # Start monitoring in background
    background_tasks.add_task(manager.start_monitoring)

    return {
        "status": "starting",
        "message": "Monitoring is starting"
    }

@router.post("/stop", response_model=MonitoringStatus)
async def stop_monitoring(
    background_tasks: BackgroundTasks,
    manager = Depends(get_manager),
    api_key: str = Depends(get_api_key)
):
    """
    Stop monitoring.

    This endpoint stops the monitoring system. The stopping process runs in the background.

    Args:
        background_tasks (BackgroundTasks): Background tasks
        manager: Monitoring manager
        api_key: API key for authentication

    Returns:
        MonitoringStatus: Monitoring status
    """
    # Stop monitoring in background
    background_tasks.add_task(manager.stop_monitoring)

    return {
        "status": "stopping",
        "message": "Monitoring is stopping"
    }

@router.get("/status", response_model=Union[Dict[str, Any], MonitorDetailedStatus], tags=["monitoring"])
async def get_monitoring_status(
    manager = Depends(get_manager),
    detailed: bool = Query(False, description="Whether to include detailed information"),
    api_key: Optional[str] = Depends(get_optional_api_key)
):
    """
    Get monitoring status.

    This endpoint returns the current status of the monitoring system.
    If detailed=true, it includes information about all monitors, alert manager, and file changes.

    Args:
        manager: Monitoring manager
        detailed: Whether to include detailed information
        api_key: Optional API key for authentication

    Returns:
        Monitoring status information
    """
    return manager.get_status(detailed=detailed)

@router.get("/monitors", response_model=Dict[str, Dict[str, Any]], tags=["monitoring"])
async def get_monitors(
    manager = Depends(get_manager),
    api_key: Optional[str] = Depends(get_optional_api_key)
):
    """
    Get information about all monitors.

    This endpoint returns information about all monitors in the system.

    Args:
        manager: Monitoring manager
        api_key: Optional API key for authentication

    Returns:
        Information about all monitors
    """
    status = manager.get_status(detailed=True)
    return status.get("monitors", {})

@router.get("/alerts", response_model=List[AlertResponse])
async def get_alerts(
    limit: int = Query(100, description="Maximum number of alerts to return", ge=1, le=1000),
    severity: Optional[str] = Query(None, description="Filter alerts by severity"),
    alert_type: Optional[str] = Query(None, description="Filter alerts by type"),
    source: Optional[str] = Query(None, description="Filter alerts by source"),
    time_range: Optional[str] = Query(None, description="Time range (e.g., '1h', '24h', '7d')"),
    manager = Depends(get_manager),
    api_key: Optional[str] = Depends(get_optional_api_key)
):
    """
    Get alerts.

    This endpoint returns a list of alerts generated by the monitoring system.
    You can filter by severity, type, source, and time range.

    Args:
        limit: Maximum number of alerts to return (1-1000)
        severity: Filter alerts by severity (critical, high, medium, info, low)
        alert_type: Filter alerts by type
        source: Filter alerts by source
        time_range: Time range (e.g., '1h', '24h', '7d')
        manager: Monitoring manager
        api_key: Optional API key for authentication

    Returns:
        List of alerts with details
    """
    # Validate severity if provided
    if severity and severity not in [s.value for s in AlertSeverity]:
        valid_severities = ", ".join([s.value for s in AlertSeverity])
        raise HTTPException(status_code=400, detail=f"Invalid severity. Valid options: {valid_severities}")

    # Get alerts
    alerts = manager.get_alerts(limit, severity, alert_type)

    # Apply additional filters
    if source:
        source = source.lower()
        alerts = [a for a in alerts if a.get("source", "").lower() and source in a["source"].lower()]

    # Apply time range filter if provided
    if time_range:
        # Parse time range
        try:
            unit = time_range[-1].lower()
            value = int(time_range[:-1])

            if unit == 'h':
                delta = timedelta(hours=value)
            elif unit == 'd':
                delta = timedelta(days=value)
            elif unit == 'w':
                delta = timedelta(weeks=value)
            else:
                raise ValueError(f"Invalid time unit: {unit}")

            # Calculate cutoff time
            cutoff_time = datetime.now() - delta
            cutoff_timestamp = cutoff_time.isoformat()

            # Filter alerts by time
            alerts = [a for a in alerts if a.get("time", "") >= cutoff_timestamp]
        except (ValueError, IndexError):
            raise HTTPException(status_code=400, detail=f"Invalid time range format: {time_range}. Use format like '1h', '24h', '7d'")

    return alerts

@router.get("/alerts/{alert_id}", response_model=AlertResponse)
async def get_alert_details(
    alert_id: int = Path(..., description="Alert ID"),
    manager = Depends(get_manager),
    api_key: Optional[str] = Depends(get_optional_api_key)
):
    """
    Get details for a specific alert.

    This endpoint returns detailed information about a specific alert.

    Args:
        alert_id: Alert ID
        manager: Monitoring manager
        api_key: Optional API key for authentication

    Returns:
        Detailed alert information
    """
    # Get alerts
    alerts = manager.get_alerts(1000, None, None)

    # Find the specific alert
    for alert in alerts:
        if alert.get("id") == alert_id:
            return alert

    raise HTTPException(status_code=404, detail=f"Alert not found: {alert_id}")

@router.post("/alerts/{alert_id}/process", response_model=AlertResponse)
async def process_alert(
    alert_id: int = Path(..., description="Alert ID"),
    manager = Depends(get_manager),
    api_key: str = Depends(get_api_key)
):
    """
    Mark an alert as processed.

    This endpoint marks a specific alert as processed.

    Args:
        alert_id: Alert ID
        manager: Monitoring manager
        api_key: API key for authentication

    Returns:
        Updated alert information
    """
    # Check if the manager has the process_alert method
    if not hasattr(manager, "process_alert"):
        raise HTTPException(status_code=501, detail="Alert processing not implemented")

    # Process the alert
    try:
        return manager.process_alert(alert_id)
    except Exception as e:
        raise HTTPException(status_code=404, detail=f"Error processing alert: {str(e)}")

@router.get("/statistics", response_model=Dict[str, Any])
async def get_statistics(
    time_range: str = Query("24h", description="Time range (e.g., '1h', '24h', '7d')"),
    manager = Depends(get_manager),
    api_key: Optional[str] = Depends(get_optional_api_key)
):
    """
    Get monitoring statistics.

    This endpoint returns statistics about the monitoring system.

    Args:
        time_range: Time range (e.g., '1h', '24h', '7d')
        manager: Monitoring manager
        api_key: Optional API key for authentication

    Returns:
        Monitoring statistics
    """
    # Check if the manager has the get_statistics method
    if not hasattr(manager, "get_statistics"):
        # Generate mock statistics
        alerts = manager.get_alerts(1000, None, None)

        # Parse time range
        try:
            unit = time_range[-1].lower()
            value = int(time_range[:-1])

            if unit == 'h':
                delta = timedelta(hours=value)
            elif unit == 'd':
                delta = timedelta(days=value)
            elif unit == 'w':
                delta = timedelta(weeks=value)
            else:
                raise ValueError(f"Invalid time unit: {unit}")

            # Calculate cutoff time
            cutoff_time = datetime.now() - delta
            cutoff_timestamp = cutoff_time.isoformat()

            # Filter alerts by time
            filtered_alerts = [a for a in alerts if a.get("time", "") >= cutoff_timestamp]

            # Count alerts by severity
            severity_counts = {}
            for severity in [s.value for s in AlertSeverity]:
                severity_counts[severity] = len([a for a in filtered_alerts if a.get("severity") == severity])

            # Count alerts by type
            type_counts = {}
            for alert in filtered_alerts:
                alert_type = alert.get("type", "unknown")
                if alert_type not in type_counts:
                    type_counts[alert_type] = 0
                type_counts[alert_type] += 1

            # Count alerts by source
            source_counts = {}
            for alert in filtered_alerts:
                source = alert.get("source", "unknown")
                if source not in source_counts:
                    source_counts[source] = 0
                source_counts[source] += 1

            # Get file changes
            file_changes = manager.get_file_changes(1000, None)

            return {
                "time_range": time_range,
                "total_alerts": len(filtered_alerts),
                "alerts_by_severity": severity_counts,
                "alerts_by_type": type_counts,
                "alerts_by_source": source_counts,
                "total_file_changes": len(file_changes),
                "monitors": {
                    name: monitor.get("is_running", False)
                    for name, monitor in manager.get_status(detailed=True).get("monitors", {}).items()
                }
            }
        except (ValueError, IndexError):
            raise HTTPException(status_code=400, detail=f"Invalid time range format: {time_range}. Use format like '1h', '24h', '7d'")

    return manager.get_statistics(time_range)

@router.get("/file-changes", response_model=List[FileChangeResponse])
async def get_file_changes(
    limit: int = Query(100, description="Maximum number of file changes to return", ge=1, le=1000),
    path_filter: Optional[str] = Query(None, description="Filter file changes by path"),
    sort_by: str = Query("last_change", description="Sort field (last_change, first_seen, size)"),
    sort_order: str = Query("desc", description="Sort order (asc, desc)"),
    manager = Depends(get_manager),
    api_key: Optional[str] = Depends(get_optional_api_key)
):
    """
    Get file changes.

    This endpoint returns a list of file changes detected by the monitoring system.
    You can filter by path and sort the results.

    Args:
        limit: Maximum number of file changes to return (1-1000)
        path_filter: Filter file changes by path (case-insensitive substring match)
        sort_by: Field to sort by (last_change, first_seen, size)
        sort_order: Sort order (asc, desc)
        manager: Monitoring manager
        api_key: Optional API key for authentication

    Returns:
        List of file changes with details
    """
    # Validate sort parameters
    valid_sort_fields = ["last_change", "first_seen", "size", "mtime"]
    if sort_by not in valid_sort_fields:
        raise HTTPException(status_code=400, detail=f"Invalid sort field. Valid options: {', '.join(valid_sort_fields)}")

    valid_sort_orders = ["asc", "desc"]
    if sort_order not in valid_sort_orders:
        raise HTTPException(status_code=400, detail=f"Invalid sort order. Valid options: {', '.join(valid_sort_orders)}")

    # Get file changes
    file_changes = manager.get_file_changes(limit, path_filter)

    # Sort results
    reverse = sort_order == "desc"
    file_changes.sort(key=lambda x: x.get(sort_by, 0), reverse=reverse)

    return file_changes

@router.get("/file-changes/{file_path:path}", response_model=FileChangeResponse)
async def get_file_change_details(
    file_path: str = Path(..., description="File path"),
    manager = Depends(get_manager),
    api_key: Optional[str] = Depends(get_optional_api_key)
):
    """
    Get details for a specific file change.

    This endpoint returns detailed information about a specific file that has been changed.

    Args:
        file_path: Path to the file
        manager: Monitoring manager
        api_key: Optional API key for authentication

    Returns:
        Detailed file change information
    """
    # Normalize path
    file_path = file_path.lower()

    # Get file changes
    file_changes = manager.get_file_changes(1000, None)

    # Find the specific file
    for file_change in file_changes:
        if file_change.get("file_path", "").lower() == file_path:
            return file_change

    raise HTTPException(status_code=404, detail=f"File change not found: {file_path}")

@router.get("/processes", response_model=List[ProcessInfo])
async def get_processes(
    limit: int = Query(100, description="Maximum number of processes to return", ge=1, le=1000),
    name_filter: Optional[str] = Query(None, description="Filter processes by name"),
    manager = Depends(get_manager),
    api_key: Optional[str] = Depends(get_optional_api_key)
):
    """
    Get processes.

    This endpoint returns a list of processes detected by the monitoring system.

    Args:
        limit: Maximum number of processes to return (1-1000)
        name_filter: Filter processes by name (case-insensitive substring match)
        manager: Monitoring manager
        api_key: Optional API key for authentication

    Returns:
        List of processes with details
    """
    # Check if the manager has the get_processes method
    if not hasattr(manager, "get_processes"):
        # Return mock data for demonstration
        processes = [
            {
                "name": "explorer.exe",
                "pid": 1234,
                "path": "C:\\Windows\\explorer.exe",
                "command_line": "C:\\Windows\\explorer.exe",
                "user": "SYSTEM",
                "start_time": time.time() - 3600
            },
            {
                "name": "chrome.exe",
                "pid": 5678,
                "path": "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
                "command_line": "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
                "user": "SYSTEM",
                "start_time": time.time() - 1800
            },
            {
                "name": "notepad.exe",
                "pid": 9012,
                "path": "C:\\Windows\\notepad.exe",
                "command_line": "C:\\Windows\\notepad.exe",
                "user": "SYSTEM",
                "start_time": time.time() - 900
            }
        ]

        # Apply name filter
        if name_filter:
            name_filter = name_filter.lower()
            processes = [p for p in processes if name_filter in p["name"].lower()]

        # Apply limit
        processes = processes[:limit]

        return processes

    return manager.get_processes(limit, name_filter)

@router.get("/network-connections", response_model=List[NetworkConnectionInfo])
async def get_network_connections(
    limit: int = Query(100, description="Maximum number of connections to return", ge=1, le=1000),
    process_filter: Optional[str] = Query(None, description="Filter connections by process name"),
    ip_filter: Optional[str] = Query(None, description="Filter connections by IP address"),
    manager = Depends(get_manager),
    api_key: Optional[str] = Depends(get_optional_api_key)
):
    """
    Get network connections.

    This endpoint returns a list of network connections detected by the monitoring system.

    Args:
        limit: Maximum number of connections to return (1-1000)
        process_filter: Filter connections by process name (case-insensitive substring match)
        ip_filter: Filter connections by IP address (exact match)
        manager: Monitoring manager
        api_key: Optional API key for authentication

    Returns:
        List of network connections with details
    """
    # Check if the manager has the get_network_connections method
    if not hasattr(manager, "get_network_connections"):
        # Return mock data for demonstration
        connections = [
            {
                "source_ip": "***********00",
                "source_port": 12345,
                "dest_ip": "*******",
                "dest_port": 443,
                "protocol": "TCP",
                "process_name": "chrome.exe",
                "process_id": 5678
            },
            {
                "source_ip": "***********00",
                "source_port": 54321,
                "dest_ip": "*******",
                "dest_port": 80,
                "protocol": "TCP",
                "process_name": "chrome.exe",
                "process_id": 5678
            },
            {
                "source_ip": "***********00",
                "source_port": 56789,
                "dest_ip": "***********",
                "dest_port": 53,
                "protocol": "UDP",
                "process_name": "svchost.exe",
                "process_id": 1234
            }
        ]

        # Apply process filter
        if process_filter:
            process_filter = process_filter.lower()
            connections = [c for c in connections if c.get("process_name", "").lower() and process_filter in c["process_name"].lower()]

        # Apply IP filter
        if ip_filter:
            connections = [c for c in connections if c["source_ip"] == ip_filter or c["dest_ip"] == ip_filter]

        # Apply limit
        connections = connections[:limit]

        return connections

    return manager.get_network_connections(limit, process_filter, ip_filter)
