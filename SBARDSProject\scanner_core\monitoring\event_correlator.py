"""
Event Correlator for SBARDS

This module provides event correlation capabilities for the monitoring layer.
"""

import os
import re
import time
import logging
import threading
from typing import Dict, List, Any, Optional
from collections import deque, defaultdict
from datetime import datetime

class EventCorrelator:
    """
    Correlates events from different monitoring components.

    This class analyzes events from process, filesystem, and network monitors
    to identify complex patterns that may indicate malicious activity.
    """

    def __init__(self, config: Dict[str, Any], alert_manager,
                 process_monitor=None, filesystem_monitor=None, network_monitor=None):
        """
        Initialize the event correlator.

        Args:
            config (Dict[str, Any]): Monitoring configuration
            alert_manager: Alert manager instance for generating alerts
            process_monitor: Process monitor instance
            filesystem_monitor: Filesystem monitor instance
            network_monitor: Network monitor instance
        """
        self.config = config
        self.alert_manager = alert_manager
        self.logger = logging.getLogger("SBARDS.EventCorrelator")

        # Monitor references
        self.process_monitor = process_monitor
        self.filesystem_monitor = filesystem_monitor
        self.network_monitor = network_monitor

        # Event correlation
        self.correlated_events = deque(maxlen=100)
        self.process_events = defaultdict(list)  # Process ID -> events
        self.file_events = defaultdict(list)     # File path -> events

        # Correlation rules
        self.correlation_rules = [
            self._check_ransomware_behavior,
            self._check_data_exfiltration,
            self._check_suspicious_process_file_access
        ]

        self.logger.info("Event Correlator initialized")

    def start_correlation(self, stop_event: threading.Event):
        """
        Start correlating events.

        Args:
            stop_event (threading.Event): Event to signal stopping
        """
        self.logger.info("Starting event correlation")

        # Correlation loop
        while not stop_event.is_set():
            try:
                # Collect events from monitors
                self._collect_events()

                # Apply correlation rules
                self._apply_correlation_rules()

                # Clean up old events
                self._cleanup_events()

                # Wait before next correlation cycle
                stop_event.wait(5.0)

            except Exception as e:
                self.logger.error(f"Error during event correlation: {e}")
                # Wait a bit before retrying
                stop_event.wait(1.0)

        self.logger.info("Event correlation stopped")

    def _collect_events(self):
        """Collect events from all monitors."""
        # Collect process events
        if self.process_monitor:
            process_history = self.process_monitor.get_process_history()
            for event in process_history:
                if "process" in event:
                    process_info = event["process"]
                    parts = process_info.split(":", 2)
                    if len(parts) >= 2:
                        pid = parts[1]
                        self.process_events[pid].append(event)

        # Collect filesystem events
        if self.filesystem_monitor:
            file_operations = self.filesystem_monitor.get_recent_operations()
            for operation in file_operations:
                if "path" in operation:
                    file_path = operation["path"]
                    self.file_events[file_path].append(operation)

    def _apply_correlation_rules(self):
        """Apply correlation rules to collected events."""
        for rule in self.correlation_rules:
            try:
                rule()
            except Exception as e:
                self.logger.error(f"Error applying correlation rule {rule.__name__}: {e}")

    def _cleanup_events(self):
        """Clean up old events to prevent memory growth."""
        current_time = time.time()

        # Clean up process events older than 10 minutes
        for pid in list(self.process_events.keys()):
            self.process_events[pid] = [
                event for event in self.process_events[pid]
                if current_time - event.get("timestamp", 0) < 600
            ]
            if not self.process_events[pid]:
                del self.process_events[pid]

        # Clean up file events older than 10 minutes
        for file_path in list(self.file_events.keys()):
            self.file_events[file_path] = [
                event for event in self.file_events[file_path]
                if current_time - event.get("timestamp", 0) < 600
            ]
            if not self.file_events[file_path]:
                del self.file_events[file_path]

    def _check_ransomware_behavior(self):
        """
        Check for ransomware behavior patterns.

        This rule looks for:
        1. Mass file modifications/deletions
        2. High entropy file creations
        3. Suspicious process names
        4. Suspicious file extensions
        5. Ransom note creation
        6. Permission changes
        """
        # Get configuration
        config = self.config.get("monitoring", {}).get("correlation", {})
        time_window = config.get("time_window_seconds", 300)
        min_events = config.get("min_events_for_correlation", 3)
        confidence_threshold = config.get("correlation_confidence_threshold", 0.7)

        # Initialize detection indicators
        indicators = {
            "mass_file_operations": False,
            "high_entropy_files": False,
            "suspicious_processes": False,
            "suspicious_extensions": False,
            "ransom_note": False,
            "permission_changes": False,
            "suspicious_network": False
        }

        # Evidence collection
        evidence = {
            "file_operations": defaultdict(int),
            "high_entropy_files": [],
            "suspicious_processes": [],
            "suspicious_extensions": [],
            "ransom_notes": [],
            "permission_changes": [],
            "suspicious_connections": []
        }

        # Get suspicious extensions from config
        suspicious_extensions = self.config.get("monitoring", {}).get("filesystem_monitoring", {}).get(
            "suspicious_extensions", [".encrypted", ".locked", ".crypted", ".crypt"]
        )

        # Count recent file operations (within time window)
        current_time = time.time()
        for file_path, events in self.file_events.items():
            for event in events:
                if current_time - event.get("timestamp", 0) < time_window:
                    event_type = event.get("type", "unknown")
                    evidence["file_operations"][event_type] += 1

                    # Check for high entropy files
                    if event_type in ["created", "modified"] and "details" in event:
                        details = event.get("details", {})
                        if "entropy" in details and details["entropy"] > 7.5:
                            evidence["high_entropy_files"].append(file_path)

                    # Check for suspicious extensions
                    if any(file_path.lower().endswith(ext) for ext in suspicious_extensions):
                        evidence["suspicious_extensions"].append(file_path)

                    # Check for potential ransom notes
                    if (file_path.lower().endswith(".txt") or file_path.lower().endswith(".html")) and event_type == "created":
                        if any(keyword in file_path.lower() for keyword in ["ransom", "readme", "help", "decrypt", "how_to", "recover"]):
                            evidence["ransom_notes"].append(file_path)

                    # Check for permission changes
                    if event_type == "permission_changed" and "details" in event:
                        evidence["permission_changes"].append({
                            "file": file_path,
                            "details": event.get("details", {})
                        })

        # Check for suspicious processes
        for pid, events in self.process_events.items():
            for event in events:
                if current_time - event.get("timestamp", 0) < time_window:
                    process_info = event.get("process", "")
                    suspicious_patterns = self.config.get("monitoring", {}).get("process_monitoring", {}).get(
                        "suspicious_process_patterns", ["encrypt", "ransom", "crypt"]
                    )

                    if any(pattern in process_info.lower() for pattern in suspicious_patterns):
                        evidence["suspicious_processes"].append(process_info)

        # Check for suspicious network connections
        if self.network_monitor:
            suspicious_domains = self.config.get("monitoring", {}).get("network_monitoring", {}).get(
                "suspicious_domains", []
            )

            for conn_info in self.network_monitor.get_current_connections():
                for domain in suspicious_domains:
                    if domain in conn_info:
                        evidence["suspicious_connections"].append(conn_info)

        # Set indicators based on evidence
        if sum(evidence["file_operations"].values()) > self.config.get("monitoring", {}).get("filesystem_monitoring", {}).get("mass_operation_threshold", 10):
            indicators["mass_file_operations"] = True

        if evidence["high_entropy_files"]:
            indicators["high_entropy_files"] = True

        if evidence["suspicious_processes"]:
            indicators["suspicious_processes"] = True

        if evidence["suspicious_extensions"]:
            indicators["suspicious_extensions"] = True

        if evidence["ransom_notes"]:
            indicators["ransom_note"] = True

        if evidence["permission_changes"]:
            indicators["permission_changes"] = True

        if evidence["suspicious_connections"]:
            indicators["suspicious_network"] = True

        # Calculate confidence score (simple weighted sum)
        weights = {
            "mass_file_operations": 0.2,
            "high_entropy_files": 0.25,
            "suspicious_processes": 0.15,
            "suspicious_extensions": 0.2,
            "ransom_note": 0.3,
            "permission_changes": 0.1,
            "suspicious_network": 0.1
        }

        confidence_score = sum(weights[indicator] for indicator, present in indicators.items() if present)

        # Count the number of positive indicators
        positive_indicators = sum(1 for present in indicators.values() if present)

        # Generate alert if confidence threshold is met and minimum number of indicators are present
        if confidence_score >= confidence_threshold and positive_indicators >= min_events:
            # Determine severity based on confidence score
            if confidence_score >= 0.8:
                severity = "critical"
            elif confidence_score >= 0.6:
                severity = "warning"
            else:
                severity = "info"

            # This is a strong indicator of ransomware activity
            self.alert_manager.add_alert(
                source="EventCorrelator",
                alert_type="ransomware_behavior",
                message=f"Possible ransomware activity detected (confidence: {confidence_score:.2f})",
                severity=severity,
                details={
                    "confidence_score": confidence_score,
                    "positive_indicators": positive_indicators,
                    "indicators": indicators,
                    "file_operations": dict(evidence["file_operations"]),
                    "high_entropy_files": evidence["high_entropy_files"][:5],  # First 5 files
                    "suspicious_processes": evidence["suspicious_processes"],
                    "suspicious_extensions": evidence["suspicious_extensions"][:5],
                    "ransom_notes": evidence["ransom_notes"],
                    "permission_changes": evidence["permission_changes"][:5],
                    "suspicious_connections": evidence["suspicious_connections"][:5]
                }
            )

            self.logger.critical(
                f"Possible ransomware activity detected: "
                f"Confidence score: {confidence_score:.2f}, "
                f"Positive indicators: {positive_indicators}/{len(indicators)}, "
                f"File operations: {dict(evidence['file_operations'])}, "
                f"High entropy files: {len(evidence['high_entropy_files'])}"
            )

            # Record the correlation
            self.correlated_events.append({
                "timestamp": time.time(),
                "datetime": datetime.now().isoformat(),
                "type": "ransomware_behavior",
                "confidence_score": confidence_score,
                "details": {
                    "indicators": indicators,
                    "file_operations": dict(evidence["file_operations"]),
                    "high_entropy_files_count": len(evidence["high_entropy_files"]),
                    "suspicious_processes_count": len(evidence["suspicious_processes"]),
                    "suspicious_extensions_count": len(evidence["suspicious_extensions"]),
                    "ransom_notes_count": len(evidence["ransom_notes"]),
                    "permission_changes_count": len(evidence["permission_changes"]),
                    "suspicious_connections_count": len(evidence["suspicious_connections"])
                }
            })

    def _check_data_exfiltration(self):
        """
        Check for data exfiltration patterns.

        This rule looks for:
        1. Mass file read operations
        2. Outbound network connections to suspicious domains
        3. Large data transfers
        4. Unusual network patterns
        5. Suspicious process activity
        """
        # Get configuration
        config = self.config.get("monitoring", {}).get("correlation", {})
        time_window = config.get("time_window_seconds", 300)
        min_events = config.get("min_events_for_correlation", 3)
        confidence_threshold = config.get("correlation_confidence_threshold", 0.7)

        # Network monitoring config
        network_config = self.config.get("monitoring", {}).get("network_monitoring", {})
        data_exfil_threshold_kb = network_config.get("data_exfiltration_threshold_kb", 1000)
        suspicious_domains = network_config.get("suspicious_domains", [])
        suspicious_ports = network_config.get("suspicious_ports", [4444, 8080, 1337, 31337, 6666])

        # Initialize detection indicators
        indicators = {
            "mass_file_reads": False,
            "suspicious_connections": False,
            "large_data_transfers": False,
            "unusual_network_patterns": False,
            "suspicious_processes": False
        }

        # Evidence collection
        evidence = {
            "file_reads": [],
            "suspicious_connections": [],
            "large_transfers": [],
            "unusual_patterns": [],
            "suspicious_processes": []
        }

        # Check for mass file read operations
        file_read_count = 0
        current_time = time.time()
        for file_path, events in self.file_events.items():
            for event in events:
                if current_time - event.get("timestamp", 0) < time_window:
                    if event.get("type") == "read":
                        file_read_count += 1
                        evidence["file_reads"].append(file_path)

        # Check for suspicious network connections and data transfers
        if self.network_monitor:
            connections = self.network_monitor.get_connection_history()

            # Group connections by remote host
            connections_by_host = defaultdict(list)
            for conn in connections:
                if current_time - conn.get("timestamp", 0) < time_window:
                    conn_info = conn.get("connection", "")
                    parts = conn_info.split(":")
                    if len(parts) >= 3:
                        remote = parts[2]
                        connections_by_host[remote].append(conn)

            # Check for suspicious connections
            for remote, conns in connections_by_host.items():
                # Check if connecting to suspicious domain
                if any(domain in remote for domain in suspicious_domains):
                    evidence["suspicious_connections"].append({
                        "remote": remote,
                        "connections": len(conns),
                        "first_seen": min(conn.get("timestamp", current_time) for conn in conns),
                        "last_seen": max(conn.get("timestamp", 0) for conn in conns)
                    })

                # Check for connections to suspicious ports
                for conn in conns:
                    conn_info = conn.get("connection", "")
                    parts = conn_info.split(":")
                    if len(parts) >= 3:
                        try:
                            port = int(parts[2].split(".")[-1])
                            if port in suspicious_ports:
                                evidence["suspicious_connections"].append({
                                    "remote": remote,
                                    "port": port,
                                    "timestamp": conn.get("timestamp", 0)
                                })
                        except (ValueError, IndexError):
                            pass

                # Check for large data transfers
                if "details" in conns[0] and "bytes_sent" in conns[0]["details"]:
                    total_bytes = sum(conn.get("details", {}).get("bytes_sent", 0) for conn in conns)
                    if total_bytes > data_exfil_threshold_kb * 1024:
                        evidence["large_transfers"].append({
                            "remote": remote,
                            "total_bytes": total_bytes,
                            "connections": len(conns)
                        })

                # Check for unusual patterns (beaconing, periodic connections)
                if len(conns) >= 3:
                    timestamps = sorted([conn.get("timestamp", 0) for conn in conns])
                    intervals = [timestamps[i+1] - timestamps[i] for i in range(len(timestamps)-1)]

                    # Check for regular intervals (beaconing)
                    if len(intervals) >= 2:
                        avg_interval = sum(intervals) / len(intervals)
                        variance = sum((interval - avg_interval) ** 2 for interval in intervals) / len(intervals)

                        # Low variance indicates regular beaconing
                        if variance < avg_interval * 0.2:  # Variance less than 20% of average
                            evidence["unusual_patterns"].append({
                                "remote": remote,
                                "pattern": "beaconing",
                                "avg_interval": avg_interval,
                                "variance": variance,
                                "connections": len(conns)
                            })

        # Check for suspicious processes
        for pid, events in self.process_events.items():
            for event in events:
                if current_time - event.get("timestamp", 0) < time_window:
                    process_info = event.get("process", "")

                    # Check for data exfiltration related process names
                    exfil_keywords = ["exfil", "upload", "ftp", "sftp", "scp", "curl", "wget", "dump", "copy"]
                    if any(keyword in process_info.lower() for keyword in exfil_keywords):
                        evidence["suspicious_processes"].append(process_info)

        # Set indicators based on evidence
        if len(evidence["file_reads"]) > 50:  # Threshold for mass file reads
            indicators["mass_file_reads"] = True

        if evidence["suspicious_connections"]:
            indicators["suspicious_connections"] = True

        if evidence["large_transfers"]:
            indicators["large_data_transfers"] = True

        if evidence["unusual_patterns"]:
            indicators["unusual_network_patterns"] = True

        if evidence["suspicious_processes"]:
            indicators["suspicious_processes"] = True

        # Calculate confidence score (simple weighted sum)
        weights = {
            "mass_file_reads": 0.2,
            "suspicious_connections": 0.25,
            "large_data_transfers": 0.3,
            "unusual_network_patterns": 0.15,
            "suspicious_processes": 0.1
        }

        confidence_score = sum(weights[indicator] for indicator, present in indicators.items() if present)

        # Count the number of positive indicators
        positive_indicators = sum(1 for present in indicators.values() if present)

        # Generate alert if confidence threshold is met and minimum number of indicators are present
        if confidence_score >= confidence_threshold and positive_indicators >= min_events:
            # Determine severity based on confidence score
            if confidence_score >= 0.8:
                severity = "critical"
            elif confidence_score >= 0.6:
                severity = "warning"
            else:
                severity = "info"

            # Generate alert for data exfiltration
            self.alert_manager.add_alert(
                source="EventCorrelator",
                alert_type="data_exfiltration",
                message=f"Possible data exfiltration detected (confidence: {confidence_score:.2f})",
                severity=severity,
                details={
                    "confidence_score": confidence_score,
                    "positive_indicators": positive_indicators,
                    "indicators": indicators,
                    "file_reads_count": len(evidence["file_reads"]),
                    "file_reads_sample": evidence["file_reads"][:5],  # First 5 files
                    "suspicious_connections": evidence["suspicious_connections"],
                    "large_transfers": evidence["large_transfers"],
                    "unusual_patterns": evidence["unusual_patterns"],
                    "suspicious_processes": evidence["suspicious_processes"]
                }
            )

            self.logger.critical(
                f"Possible data exfiltration detected: "
                f"Confidence score: {confidence_score:.2f}, "
                f"Positive indicators: {positive_indicators}/{len(indicators)}, "
                f"File reads: {len(evidence['file_reads'])}, "
                f"Suspicious connections: {len(evidence['suspicious_connections'])}"
            )

            # Record the correlation
            self.correlated_events.append({
                "timestamp": time.time(),
                "datetime": datetime.now().isoformat(),
                "type": "data_exfiltration",
                "confidence_score": confidence_score,
                "details": {
                    "indicators": indicators,
                    "file_reads_count": len(evidence["file_reads"]),
                    "suspicious_connections_count": len(evidence["suspicious_connections"]),
                    "large_transfers_count": len(evidence["large_transfers"]),
                    "unusual_patterns_count": len(evidence["unusual_patterns"]),
                    "suspicious_processes_count": len(evidence["suspicious_processes"])
                }
            })

    def _check_suspicious_process_file_access(self):
        """
        Check for suspicious processes accessing sensitive files.

        This rule correlates process activity with file access patterns
        to detect potentially malicious behavior, such as:
        1. Unauthorized access to sensitive files
        2. Unusual file access patterns by processes
        3. Processes accessing files outside their normal scope
        4. Suspicious processes accessing system files
        """
        # Get configuration
        config = self.config.get("monitoring", {}).get("correlation", {})
        time_window = config.get("time_window_seconds", 300)
        min_events = config.get("min_events_for_correlation", 3)
        confidence_threshold = config.get("correlation_confidence_threshold", 0.7)

        # Define sensitive file patterns
        sensitive_file_patterns = [
            r"\.conf$", r"\.config$", r"\.ini$", r"\.json$", r"\.xml$",  # Configuration files
            r"\.key$", r"\.pem$", r"\.crt$", r"\.cer$", r"\.p12$",       # Certificate/key files
            r"\.db$", r"\.sqlite$", r"\.mdb$",                           # Database files
            r"\.doc$", r"\.docx$", r"\.xls$", r"\.xlsx$", r"\.pdf$",     # Document files
            r"\.exe$", r"\.dll$", r"\.sys$",                             # System files
            r"passwd", r"shadow", r"\.htaccess$", r"hosts$"              # System configuration
        ]

        # Initialize detection indicators
        indicators = {
            "sensitive_file_access": False,
            "unusual_access_pattern": False,
            "suspicious_process": False,
            "system_file_access": False
        }

        # Evidence collection
        evidence = {
            "sensitive_file_accesses": [],
            "unusual_patterns": [],
            "suspicious_processes": [],
            "system_file_accesses": []
        }

        # Track process to file access mapping
        process_file_access = defaultdict(list)

        # Collect process-file access relationships
        current_time = time.time()

        # This would normally come from a more sophisticated file access tracking mechanism
        # For now, we'll use a simplified approach based on our existing data
        for file_path, events in self.file_events.items():
            for event in events:
                if current_time - event.get("timestamp", 0) < time_window:
                    if "details" in event and "process_id" in event["details"]:
                        pid = event["details"]["process_id"]
                        process_file_access[pid].append({
                            "file": file_path,
                            "operation": event.get("type", "unknown"),
                            "timestamp": event.get("timestamp", 0)
                        })

        # Check for suspicious processes accessing sensitive files
        for pid, accesses in process_file_access.items():
            # Get process info
            process_info = None
            for events in self.process_events.get(pid, []):
                if "process" in events:
                    process_info = events["process"]
                    break

            if not process_info:
                continue

            # Check if this is a suspicious process
            suspicious_patterns = self.config.get("monitoring", {}).get("process_monitoring", {}).get(
                "suspicious_process_patterns", ["encrypt", "ransom", "crypt"]
            )

            is_suspicious_process = any(pattern in process_info.lower() for pattern in suspicious_patterns)

            if is_suspicious_process:
                evidence["suspicious_processes"].append({
                    "pid": pid,
                    "process": process_info,
                    "file_access_count": len(accesses)
                })

            # Check for sensitive file access
            for access in accesses:
                file_path = access["file"]

                # Check if this is a sensitive file
                is_sensitive = any(re.search(pattern, file_path, re.IGNORECASE) for pattern in sensitive_file_patterns)

                if is_sensitive:
                    evidence["sensitive_file_accesses"].append({
                        "pid": pid,
                        "process": process_info,
                        "file": file_path,
                        "operation": access["operation"],
                        "timestamp": access["timestamp"]
                    })

                # Check for system file access
                system_paths = [
                    r"C:\\Windows\\", r"/etc/", r"/bin/", r"/sbin/",
                    r"C:\\Program Files\\", r"C:\\Program Files (x86)\\",
                    r"/usr/bin/", r"/usr/sbin/", r"/usr/local/bin/"
                ]

                is_system_file = any(re.match(pattern, file_path, re.IGNORECASE) for pattern in system_paths)

                if is_system_file and is_suspicious_process:
                    evidence["system_file_accesses"].append({
                        "pid": pid,
                        "process": process_info,
                        "file": file_path,
                        "operation": access["operation"],
                        "timestamp": access["timestamp"]
                    })

        # Check for unusual access patterns (high frequency, unusual times)
        for pid, accesses in process_file_access.items():
            if len(accesses) > 50:  # High frequency access
                process_info = None
                for events in self.process_events.get(pid, []):
                    if "process" in events:
                        process_info = events["process"]
                        break

                evidence["unusual_patterns"].append({
                    "pid": pid,
                    "process": process_info,
                    "access_count": len(accesses),
                    "pattern": "high_frequency"
                })

        # Set indicators based on evidence
        if evidence["sensitive_file_accesses"]:
            indicators["sensitive_file_access"] = True

        if evidence["unusual_patterns"]:
            indicators["unusual_access_pattern"] = True

        if evidence["suspicious_processes"]:
            indicators["suspicious_process"] = True

        if evidence["system_file_accesses"]:
            indicators["system_file_access"] = True

        # Calculate confidence score (simple weighted sum)
        weights = {
            "sensitive_file_access": 0.3,
            "unusual_access_pattern": 0.2,
            "suspicious_process": 0.3,
            "system_file_access": 0.2
        }

        confidence_score = sum(weights[indicator] for indicator, present in indicators.items() if present)

        # Count the number of positive indicators
        positive_indicators = sum(1 for present in indicators.values() if present)

        # Generate alert if confidence threshold is met and minimum number of indicators are present
        if confidence_score >= confidence_threshold and positive_indicators >= min_events:
            # Determine severity based on confidence score
            if confidence_score >= 0.8:
                severity = "critical"
            elif confidence_score >= 0.6:
                severity = "warning"
            else:
                severity = "info"

            # Generate alert for suspicious process file access
            self.alert_manager.add_alert(
                source="EventCorrelator",
                alert_type="suspicious_process_file_access",
                message=f"Suspicious process file access detected (confidence: {confidence_score:.2f})",
                severity=severity,
                details={
                    "confidence_score": confidence_score,
                    "positive_indicators": positive_indicators,
                    "indicators": indicators,
                    "sensitive_file_accesses": evidence["sensitive_file_accesses"][:5],
                    "unusual_patterns": evidence["unusual_patterns"],
                    "suspicious_processes": evidence["suspicious_processes"],
                    "system_file_accesses": evidence["system_file_accesses"][:5]
                }
            )

            self.logger.warning(
                f"Suspicious process file access detected: "
                f"Confidence score: {confidence_score:.2f}, "
                f"Positive indicators: {positive_indicators}/{len(indicators)}, "
                f"Sensitive file accesses: {len(evidence['sensitive_file_accesses'])}, "
                f"Suspicious processes: {len(evidence['suspicious_processes'])}"
            )

            # Record the correlation
            self.correlated_events.append({
                "timestamp": time.time(),
                "datetime": datetime.now().isoformat(),
                "type": "suspicious_process_file_access",
                "confidence_score": confidence_score,
                "details": {
                    "indicators": indicators,
                    "sensitive_file_accesses_count": len(evidence["sensitive_file_accesses"]),
                    "unusual_patterns_count": len(evidence["unusual_patterns"]),
                    "suspicious_processes_count": len(evidence["suspicious_processes"]),
                    "system_file_accesses_count": len(evidence["system_file_accesses"])
                }
            })

    def get_correlated_events(self, count: int = None) -> List[Dict[str, Any]]:
        """
        Get correlated events.

        Args:
            count (int, optional): Number of events to retrieve

        Returns:
            List[Dict[str, Any]]: List of correlated events
        """
        if count is None:
            return list(self.correlated_events)
        else:
            return list(self.correlated_events)[-count:]
