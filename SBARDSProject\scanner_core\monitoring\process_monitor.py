"""
Process Monitor for SBARDS

This module provides process monitoring capabilities for the SBARDS project.
"""

import os
import time
import logging
import threading
import platform
import subprocess
import re
from typing import Dict, List, Any, Optional, Set
from collections import deque

class ProcessMonitor:
    """
    Monitors system processes for suspicious activity.
    
    This class provides capabilities to monitor process creation, termination,
    and behavior patterns that may indicate malicious activity.
    """
    
    def __init__(self, config: Dict[str, Any], alert_manager):
        """
        Initialize the process monitor.
        
        Args:
            config (Dict[str, Any]): Process monitoring configuration
            alert_manager: Alert manager instance for generating alerts
        """
        self.config = config
        self.alert_manager = alert_manager
        self.logger = logging.getLogger("SBARDS.ProcessMonitor")
        
        # Process monitoring configuration
        self.check_interval = config.get("check_interval_seconds", 10)
        self.suspicious_patterns = config.get("suspicious_process_patterns", [])
        self.max_history = config.get("max_history_entries", 1000)
        
        # Process tracking
        self.process_history = deque(maxlen=self.max_history)
        self.current_processes = set()
        self.previous_processes = set()
        
        # Platform-specific settings
        self.platform = platform.system().lower()
        
        self.logger.info(f"Process Monitor initialized on {platform.system()}")
        
    def start_monitoring(self, stop_event: threading.Event):
        """
        Start monitoring processes.
        
        Args:
            stop_event (threading.Event): Event to signal stopping
        """
        self.logger.info("Starting process monitoring")
        
        # Initial process scan
        self._scan_processes()
        
        # Monitoring loop
        while not stop_event.is_set():
            try:
                # Scan processes
                self._scan_processes()
                
                # Check for suspicious processes
                self._check_suspicious_processes()
                
                # Wait for next check interval
                stop_event.wait(self.check_interval)
                
            except Exception as e:
                self.logger.error(f"Error during process monitoring: {e}")
                # Wait a bit before retrying
                stop_event.wait(1.0)
                
        self.logger.info("Process monitoring stopped")
        
    def _scan_processes(self):
        """Scan current processes and track changes."""
        # Update previous processes
        self.previous_processes = self.current_processes.copy()
        
        # Get current processes
        self.current_processes = self._get_running_processes()
        
        # Identify new and terminated processes
        new_processes = self.current_processes - self.previous_processes
        terminated_processes = self.previous_processes - self.current_processes
        
        # Log process changes
        for process_info in new_processes:
            self.process_history.append({
                "timestamp": time.time(),
                "event": "created",
                "process": process_info
            })
            self.logger.debug(f"New process: {process_info}")
            
        for process_info in terminated_processes:
            self.process_history.append({
                "timestamp": time.time(),
                "event": "terminated",
                "process": process_info
            })
            self.logger.debug(f"Terminated process: {process_info}")
            
    def _get_running_processes(self) -> Set[str]:
        """
        Get currently running processes.
        
        Returns:
            Set[str]: Set of process information strings
        """
        processes = set()
        
        try:
            if self.platform == "windows":
                # Use WMIC on Windows
                output = subprocess.check_output(
                    ["wmic", "process", "get", "ProcessId,Name,ExecutablePath"],
                    universal_newlines=True
                )
                
                # Parse output
                for line in output.strip().split("\n")[1:]:
                    parts = line.strip().split()
                    if len(parts) >= 2:
                        # Last part is PID, second to last is name
                        pid = parts[-1]
                        name = parts[-2]
                        path = " ".join(parts[:-2]) if len(parts) > 2 else ""
                        processes.add(f"{name}:{pid}:{path}")
                        
            else:
                # Use ps on Linux/macOS
                output = subprocess.check_output(
                    ["ps", "-eo", "pid,comm,args"],
                    universal_newlines=True
                )
                
                # Parse output
                for line in output.strip().split("\n")[1:]:
                    parts = line.strip().split(maxsplit=2)
                    if len(parts) >= 2:
                        pid = parts[0]
                        name = parts[1]
                        cmdline = parts[2] if len(parts) > 2 else ""
                        processes.add(f"{name}:{pid}:{cmdline}")
                        
        except Exception as e:
            self.logger.error(f"Error getting running processes: {e}")
            
        return processes
        
    def _check_suspicious_processes(self):
        """Check for suspicious processes based on patterns."""
        for process_info in self.current_processes:
            # Check against suspicious patterns
            for pattern in self.suspicious_patterns:
                if re.search(pattern, process_info, re.IGNORECASE):
                    # Extract process details
                    parts = process_info.split(":", 2)
                    name = parts[0] if len(parts) > 0 else "unknown"
                    pid = parts[1] if len(parts) > 1 else "unknown"
                    path = parts[2] if len(parts) > 2 else ""
                    
                    # Generate alert
                    self.alert_manager.add_alert(
                        source="ProcessMonitor",
                        alert_type="suspicious_process",
                        message=f"Suspicious process detected: {name} (PID: {pid})",
                        severity="warning",
                        details={
                            "process_name": name,
                            "process_id": pid,
                            "process_path": path,
                            "matched_pattern": pattern
                        }
                    )
                    
                    self.logger.warning(f"Suspicious process detected: {process_info} (matched pattern: {pattern})")
                    
    def get_process_history(self, count: int = None) -> List[Dict[str, Any]]:
        """
        Get process history.
        
        Args:
            count (int, optional): Number of history entries to retrieve
            
        Returns:
            List[Dict[str, Any]]: List of process history entries
        """
        if count is None:
            return list(self.process_history)
        else:
            return list(self.process_history)[-count:]
            
    def get_current_processes(self) -> Set[str]:
        """
        Get current processes.
        
        Returns:
            Set[str]: Set of current process information strings
        """
        return self.current_processes.copy()
