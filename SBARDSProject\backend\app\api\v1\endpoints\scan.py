"""
API endpoints for direct scanner integration.

This module provides API endpoints for directly triggering and monitoring scans.
"""

import os
import json
import asyncio
import uuid
from typing import Dict, List, Optional, Any

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query, Path, status, File, UploadFile, Form, Body
from sqlalchemy.orm import Session

from ....core.logging import logger
from ....core.config import settings
from ....core.security import verify_api_key
from ....core.errors import handle_error, DatabaseError, ScanError, NotFoundError, ValidationError
from ....db.session import get_db
from ....schemas.scan import ScanRequest, ScanResponse, ScanStatus
from ....services import scanner, reports

# Create router
router = APIRouter()


@router.post(
    "/",
    response_model=ScanResponse,
    status_code=status.HTTP_202_ACCEPTED,
    summary="Start a new scan",
    description="Start a new scan of a specified path or uploaded file."
)
async def start_scan(
    background_tasks: BackgroundTasks,
    path: Optional[str] = Form(None, description="Path to scan (local to the server)"),
    file: Optional[UploadFile] = File(None, description="File to scan"),
    recursive: bool = Form(False, description="Scan directories recursively"),
    api_key: str = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """
    Start a new scan.

    Either provide a path on the server to scan, or upload a file to scan.

    - **path**: Path on the server to scan (if scanning a local directory)
    - **file**: File to upload and scan
    - **recursive**: Whether to scan directories recursively (only applies to path scanning)

    Returns a scan ID that can be used to check the status of the scan.
    """
    try:
        # Generate a unique scan ID
        scan_id = f"scan_{uuid.uuid4().hex}"

        # Determine what to scan
        if path:
            # Validate path
            if not os.path.exists(path):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Path not found: {path}"
                )

            scan_path = path
            is_file_upload = False
        elif file:
            # Save uploaded file
            upload_dir = os.path.join(settings.UPLOAD_DIR, scan_id)
            os.makedirs(upload_dir, exist_ok=True)

            file_path = os.path.join(upload_dir, file.filename)
            with open(file_path, "wb") as f:
                content = await file.read()
                f.write(content)

            scan_path = file_path
            is_file_upload = True
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Either 'path' or 'file' must be provided"
            )

        # Start scan in background
        background_tasks.add_task(
            run_scan_task,
            scan_id=scan_id,
            scan_path=scan_path,
            recursive=recursive,
            is_file_upload=is_file_upload
        )

        # Return scan ID
        return {
            "scan_id": scan_id,
            "status": "queued",
            "message": "Scan queued successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting scan: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting scan: {str(e)}"
        )


@router.get(
    "/{scan_id}/status",
    response_model=ScanStatus,
    summary="Get scan status",
    description="Get the status of a scan by its ID."
)
async def get_scan_status(
    scan_id: str = Path(..., description="The unique identifier of the scan"),
    db: Session = Depends(get_db)
):
    """
    Get the status of a scan.

    - **scan_id**: The unique identifier of the scan

    Returns the current status of the scan.
    """
    try:
        # Check if scan exists in database
        try:
            db_report = reports.get_scan_report(db, scan_id, include_content=False)

            if db_report:
                return {
                    "scan_id": scan_id,
                    "status": "completed",
                    "files_scanned": db_report.files_scanned,
                    "threats_found": db_report.threats_found,
                    "report_id": db_report.id
                }
        except Exception as db_error:
            logger.error(f"Error querying database for scan {scan_id}: {db_error}")
            # Continue to check status file

        # Check if scan is in progress
        scan_dir = os.path.join(settings.UPLOAD_DIR, scan_id)
        status_file = os.path.join(scan_dir, "status.json")

        if os.path.exists(status_file):
            try:
                with open(status_file, "r") as f:
                    status_data = json.load(f)
                return status_data
            except json.JSONDecodeError as json_error:
                logger.error(f"Error parsing status file for scan {scan_id}: {json_error}")
                raise ValidationError(
                    message=f"Invalid status file format for scan {scan_id}",
                    field="status_file",
                    value=status_file
                )

        # Scan not found
        raise NotFoundError(
            message=f"Scan with ID {scan_id} not found",
            resource_type="scan",
            resource_id=scan_id
        )
    except (NotFoundError, ValidationError, DatabaseError, ScanError) as e:
        raise handle_error(e)
    except Exception as e:
        logger.error(f"Error getting scan status for {scan_id}: {e}")
        raise handle_error(ScanError(
            message=f"Error getting scan status",
            scan_id=scan_id,
            original_error=e
        ))


async def run_scan_task(scan_id: str, scan_path: str, recursive: bool, is_file_upload: bool):
    """
    Run a scan task in the background.

    Args:
        scan_id (str): Scan ID.
        scan_path (str): Path to scan.
        recursive (bool): Whether to scan recursively.
        is_file_upload (bool): Whether this is a file upload.
    """
    try:
        # Create scan directory
        scan_dir = os.path.join(settings.UPLOAD_DIR, scan_id)
        os.makedirs(scan_dir, exist_ok=True)

        # Create status file
        status_file = os.path.join(scan_dir, "status.json")
        with open(status_file, "w") as f:
            json.dump({
                "scan_id": scan_id,
                "status": "queued",
                "message": "Scan is queued and will start shortly"
            }, f)

        # Run the scan
        logger.info(f"Starting scan of {scan_path} with ID {scan_id}")

        try:
            # Call the scanner service to run the scan
            result = await scanner.run_scan(scan_path, scan_id, recursive)

            # Update status file with result
            with open(status_file, "w") as f:
                json.dump(result, f)

            logger.info(f"Scan {scan_id} completed with result: {result}")

            # Save to database
            try:
                from ....db.session import SessionLocal
                from ....db.models import ScanReport, FileResult

                db = SessionLocal()
                try:
                    # Check if report already exists
                    existing_report = db.query(ScanReport).filter(ScanReport.scan_id == scan_id).first()
                    if existing_report:
                        logger.info(f"Scan report already exists in database: {existing_report.id}")
                    else:
                        # Create scan report
                        db_report = ScanReport(
                            scan_id=scan_id,
                            scan_path=scan_path,
                            files_scanned=result.get("files_scanned", 0),
                            threats_found=result.get("threats_found", 0),
                            report_path=result.get("report_path", ""),
                            report_content=""  # Don't store content here
                        )
                        db.add(db_report)
                        db.commit()
                        logger.info(f"Saved scan report to database: {db_report.id}")
                except Exception as db_error:
                    db.rollback()
                    logger.error(f"Error saving scan report to database: {db_error}")
                finally:
                    db.close()
            except Exception as db_error:
                logger.error(f"Error with database operations: {db_error}")
        except Exception as scan_error:
            logger.error(f"Error during scan execution: {scan_error}")

            # Update status file with error
            error_data = {
                "scan_id": scan_id,
                "status": "error",
                "message": f"Error running scan: {str(scan_error)}"
            }

            with open(status_file, "w") as f:
                json.dump(error_data, f)

            # Try to send WebSocket update
            try:
                from .websocket import send_scan_update
                await send_scan_update(scan_id, error_data)
            except Exception:
                pass

        # Clean up uploaded file if needed
        if is_file_upload and os.path.exists(scan_path):
            try:
                os.remove(scan_path)
                logger.debug(f"Removed uploaded file: {scan_path}")
            except Exception as file_error:
                logger.error(f"Error removing uploaded file {scan_path}: {file_error}")
    except Exception as e:
        logger.error(f"Error running scan {scan_id}: {e}")

        # Update status file with error
        try:
            status_file = os.path.join(scan_dir, "status.json")
            with open(status_file, "w") as f:
                json.dump({
                    "scan_id": scan_id,
                    "status": "error",
                    "message": f"Error running scan: {str(e)}"
                }, f)
        except Exception as file_error:
            logger.error(f"Error updating status file: {file_error}")

        # Try to send WebSocket update
        try:
            from .websocket import send_scan_update
            await send_scan_update(scan_id, {
                "scan_id": scan_id,
                "status": "error",
                "message": f"Error running scan: {str(e)}"
            })
        except Exception:
            pass
