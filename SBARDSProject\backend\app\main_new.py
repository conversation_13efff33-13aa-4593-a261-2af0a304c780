"""
SBARDS Backend API

This module provides a FastAPI backend for the SBARDS project, offering:
- Collection of scan reports
- Integration with VirusTotal for file hash verification
- Centralized logging and reporting
- REST API for UIs, dashboards, and further integration
- WebSocket support for real-time updates
"""

import os
from fastapi import <PERSON><PERSON><PERSON>, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse
from fastapi.exceptions import RequestValidationError
from fastapi.exception_handlers import http_exception_handler, request_validation_exception_handler
from starlette.exceptions import HTTPException as StarletteHTTPException
import time

from .core.config import settings
from .core.logging import logger, setup_logging
from .api.v1.router import api_router as api_v1_router
from .api.v1.endpoints.auth import router as auth_router
from .middleware.rate_limit import RateLimitMiddleware
from .middleware.logging import LoggingMiddleware

# Set up logging
setup_logging()

# Initialize FastAPI app
app = FastAPI(
    title=settings.PROJECT_NAME,
    description=settings.PROJECT_DESCRIPTION,
    version=settings.PROJECT_VERSION,
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    openapi_url="/api/openapi.json",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add rate limiting middleware
app.add_middleware(RateLimitMiddleware)

# Add logging middleware
app.add_middleware(LoggingMiddleware)

# Include API routers
app.include_router(api_v1_router, prefix=settings.API_V1_STR)
app.include_router(auth_router, prefix=f"{settings.API_V1_STR}/auth", tags=["auth"])

# Create static directory if it doesn't exist
static_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "static")
os.makedirs(static_dir, exist_ok=True)

# Mount static files
app.mount("/static", StaticFiles(directory=static_dir), name="static")

# Create uploads directory if it doesn't exist
uploads_dir = os.path.abspath(settings.UPLOAD_DIR)
os.makedirs(uploads_dir, exist_ok=True)


# Custom exception handlers
@app.exception_handler(StarletteHTTPException)
async def custom_http_exception_handler(request, exc):
    """Custom HTTP exception handler."""
    logger.error(f"HTTP error: {exc.status_code} - {exc.detail}")
    return await http_exception_handler(request, exc)


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request, exc):
    """Custom validation exception handler."""
    logger.error(f"Validation error: {exc}")
    return await request_validation_exception_handler(request, exc)


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """General exception handler."""
    from .core.errors import handle_error

    logger.error(f"Unhandled exception: {exc}")
    try:
        # Try to convert to HTTPException
        http_exc = handle_error(exc)
        return await custom_http_exception_handler(request, http_exc)
    except Exception as e:
        logger.error(f"Error in exception handler: {e}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"detail": "Internal server error"}
        )


# Health check endpoint
@app.get("/api/health", tags=["health"])
async def health_check():
    """
    Health check endpoint.

    Returns basic information about the API health.
    """
    return {
        "status": "ok",
        "timestamp": time.time(),
        "version": settings.PROJECT_VERSION,
    }


# Root endpoint
@app.get("/", response_class=HTMLResponse, tags=["dashboard"])
async def root():
    """
    Redirect to the dashboard.

    This endpoint redirects users to the main dashboard page.
    """
    return RedirectResponse(url="/dashboard")


# Dashboard endpoint
@app.get("/dashboard", response_class=HTMLResponse, tags=["dashboard"])
async def dashboard():
    """
    Serve the dashboard HTML page.

    This endpoint serves the main dashboard HTML page that displays scan reports and statistics.
    """
    try:
        with open(os.path.join(static_dir, "dashboard.html"), "r") as f:
            return f.read()
    except FileNotFoundError:
        logger.error("Dashboard HTML file not found")
        return HTMLResponse(content="<h1>Dashboard not found</h1>", status_code=404)


# API root endpoint
@app.get("/api", tags=["api"])
async def api_root():
    """
    API root endpoint.

    Returns basic information about the API.
    """
    return {
        "message": "SBARDS Backend API",
        "version": settings.PROJECT_VERSION,
        "documentation": "/api/docs",
        "endpoints": {
            "v1": settings.API_V1_STR,
            "health": "/api/health",
        }
    }
