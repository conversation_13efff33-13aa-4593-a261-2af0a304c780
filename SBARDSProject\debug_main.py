#!/usr/bin/env python3
"""
Debug script to isolate the main.py initialization issue
"""

import sys
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import core modules
from core.config import load_config
from core.logging import setup_logging
from core.utils import validate_environment

def test_config_loading():
    """Test configuration loading."""
    print("Testing configuration loading...")
    try:
        config = load_config("config.json")
        print(f"✓ Configuration loaded successfully")
        print(f"  - Config keys: {list(config.keys())}")
        return config
    except Exception as e:
        print(f"✗ Configuration loading failed: {e}")
        return None

def test_logging_setup():
    """Test logging setup."""
    print("\nTesting logging setup...")
    try:
        setup_logging(level="INFO")
        logger = logging.getLogger("SBARDS.Debug")
        logger.info("Test log message")
        print("✓ Logging setup successful")
        return True
    except Exception as e:
        print(f"✗ Logging setup failed: {e}")
        return False

def test_environment_validation(config):
    """Test environment validation."""
    print("\nTesting environment validation...")
    try:
        result = validate_environment(config)
        if result:
            print("✓ Environment validation passed")
        else:
            print("⚠ Environment validation failed")
        return result
    except Exception as e:
        print(f"✗ Environment validation error: {e}")
        return False

def test_phase_imports():
    """Test individual phase imports."""
    print("\nTesting phase imports...")
    
    phases = [
        ("CaptureLayer", "phases.capture.capture", "CaptureLayer"),
        ("PreScanningOrchestrator", "phases.prescanning.orchestrator", "Orchestrator"),
        ("StaticAnalyzer", "phases.static_analysis.static_analyzer", "StaticAnalyzer"),
        ("DynamicAnalyzer", "phases.dynamic_analysis.dynamic_analyzer", "DynamicAnalyzer"),
        ("ResponseLayer", "phases.response.response", "ResponseLayer"),
        ("MonitorManager", "phases.monitoring.monitor_manager", "MonitorManager"),
    ]
    
    successful_imports = []
    failed_imports = []
    
    for phase_name, module_path, class_name in phases:
        try:
            module = __import__(module_path, fromlist=[class_name])
            phase_class = getattr(module, class_name)
            print(f"✓ {phase_name} imported successfully")
            successful_imports.append((phase_name, phase_class))
        except Exception as e:
            print(f"✗ {phase_name} import failed: {e}")
            failed_imports.append((phase_name, str(e)))
    
    return successful_imports, failed_imports

def test_phase_initialization(config, successful_imports):
    """Test phase initialization."""
    print("\nTesting phase initialization...")
    
    initialized_phases = []
    failed_phases = []
    
    for phase_name, phase_class in successful_imports:
        try:
            print(f"  Initializing {phase_name}...")
            phase_instance = phase_class(config)
            print(f"  ✓ {phase_name} initialized successfully")
            initialized_phases.append((phase_name, phase_instance))
        except Exception as e:
            print(f"  ✗ {phase_name} initialization failed: {e}")
            failed_phases.append((phase_name, str(e)))
    
    return initialized_phases, failed_phases

def main():
    """Main debug function."""
    print("SBARDS Main.py Debug Script")
    print("=" * 50)
    
    # Test configuration loading
    config = test_config_loading()
    if not config:
        print("Cannot proceed without configuration")
        return 1
    
    # Test logging setup
    if not test_logging_setup():
        print("Logging setup failed, but continuing...")
    
    # Test environment validation
    if not test_environment_validation(config):
        print("Environment validation failed, but continuing...")
    
    # Test phase imports
    successful_imports, failed_imports = test_phase_imports()
    
    if not successful_imports:
        print("No phases could be imported")
        return 1
    
    # Test phase initialization
    initialized_phases, failed_phases = test_phase_initialization(config, successful_imports)
    
    # Summary
    print("\n" + "=" * 50)
    print("SUMMARY")
    print("=" * 50)
    print(f"Successfully imported phases: {len(successful_imports)}")
    print(f"Failed to import phases: {len(failed_imports)}")
    print(f"Successfully initialized phases: {len(initialized_phases)}")
    print(f"Failed to initialize phases: {len(failed_phases)}")
    
    if failed_phases:
        print("\nFailed phase initializations:")
        for phase_name, error in failed_phases:
            print(f"  - {phase_name}: {error}")
    
    if initialized_phases:
        print("\nSuccessfully initialized phases:")
        for phase_name, _ in initialized_phases:
            print(f"  - {phase_name}")
    
    return 0 if not failed_phases else 1

if __name__ == "__main__":
    sys.exit(main())
