"""
Prescanning Orchestrator for SBARDS

This module provides the orchestrator for the prescanning phase of the SBARDS project.
"""

import os
import sys
import time
import uuid
import logging
import threading
import concurrent.futures
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime

# Add project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import core components
from core.config import ConfigLoader
from core.logging import Logger
from core.utils import get_file_hash, get_file_info, save_json, load_json, format_time, get_memory_usage

class Orchestrator:
    """
    Orchestrator for the prescanning phase.

    This class provides mechanisms for:
    1. Orchestrating the scanning process
    2. Managing file operations
    3. Logging results
    4. Coordinating with other phases
    """

    def __init__(self, config_or_path = "config.json"):
        """
        Initialize orchestrator.

        Args:
            config_or_path (str or dict, optional): Path to configuration file or config dictionary
        """
        # Load configuration
        if isinstance(config_or_path, dict):
            # Config dictionary provided
            self.config = config_or_path
            self.config_loader = None
        else:
            # Config path provided
            self.config_loader = ConfigLoader(config_or_path)
            self.config = self.config_loader.get_config()

        # Set up logging
        log_dir = self.config.get("output", {}).get("log_directory", "logs")
        log_level = self.config.get("output", {}).get("log_level", "info")
        self.logger = Logger(log_dir, log_level).get_logger("Prescanning")

        # Initialize scanners
        self._initialize_scanners()

        # Initialize scan state
        self.scans = {}
        self.active_scans = set()
        self.scan_threads = {}
        self.scan_stop_events = {}

        # Initialize scan results directory
        self.results_dir = os.path.join(
            self.config.get("output", {}).get("output_directory", "output"),
            "scan_results"
        )
        os.makedirs(self.results_dir, exist_ok=True)

        # Load existing scan results
        self._load_existing_results()

        # Initialize monitor manager reference
        self.monitor_manager = None

    def _initialize_scanners(self) -> None:
        """Initialize scanners."""
        self.logger.info("Initializing scanners")

        # Initialize scanners based on configuration
        # This would initialize YARA scanner, hash checker, etc.
        pass

    def _load_existing_results(self) -> None:
        """Load existing scan results."""
        try:
            # Load scan results from files
            for filename in os.listdir(self.results_dir):
                if filename.endswith(".json"):
                    file_path = os.path.join(self.results_dir, filename)
                    try:
                        result = load_json(file_path)
                        if result and "scan_id" in result:
                            self.scans[result["scan_id"]] = result
                    except Exception as e:
                        self.logger.error(f"Error loading scan result {filename}: {e}")
        except Exception as e:
            self.logger.error(f"Error loading existing scan results: {e}")

    def prepare_scan(self, target_directory: str, **kwargs) -> str:
        """
        Prepare a scan.

        Args:
            target_directory (str): Target directory to scan
            **kwargs: Additional scan options

        Returns:
            str: Scan ID
        """
        # Generate scan ID
        scan_id = str(uuid.uuid4())

        # Create scan configuration
        scan_config = {
            "scan_id": scan_id,
            "target_directory": os.path.abspath(target_directory),
            "recursive": kwargs.get("recursive", True),
            "max_depth": kwargs.get("max_depth", 5),
            "exclude_dirs": kwargs.get("exclude_dirs", []),
            "exclude_extensions": kwargs.get("exclude_extensions", []),
            "max_file_size_mb": kwargs.get("max_file_size_mb", 100),
            "enable_yara": kwargs.get("enable_yara", True),
            "enable_hash_check": kwargs.get("enable_hash_check", True),
            "enable_av_integration": kwargs.get("enable_av_integration", True),
            "enable_fast_track": kwargs.get("enable_fast_track", True),
            "threads": kwargs.get("threads", self.config.get("performance", {}).get("threads", 4)),
            "batch_size": kwargs.get("batch_size", self.config.get("performance", {}).get("batch_size", 20)),
            "created_at": time.time()
        }

        # Initialize scan state
        self.scans[scan_id] = {
            "scan_id": scan_id,
            "config": scan_config,
            "status": "prepared",
            "progress": {
                "total_files": 0,
                "processed_files": 0,
                "matched_files": 0,
                "current_file": None,
                "percent_complete": 0.0,
                "elapsed_time": 0.0,
                "estimated_time_remaining": None,
                "current_phase": "initializing"
            },
            "result": None
        }

        # Create stop event
        self.scan_stop_events[scan_id] = threading.Event()

        self.logger.info(f"Prepared scan {scan_id} for {target_directory}")
        return scan_id

    def run_scan(self, target_directory: str, **kwargs) -> Dict[str, Any]:
        """
        Run a scan.

        Args:
            target_directory (str): Target directory to scan
            **kwargs: Additional scan options

        Returns:
            Dict[str, Any]: Scan result
        """
        # Prepare scan
        scan_id = self.prepare_scan(target_directory, **kwargs)

        # Run scan synchronously
        return self._run_scan(scan_id)

    def run_scan_async(self, scan_id: str) -> None:
        """
        Run a scan asynchronously.

        Args:
            scan_id (str): Scan ID
        """
        # Check if scan exists
        if scan_id not in self.scans:
            self.logger.error(f"Scan {scan_id} not found")
            return

        # Check if scan is already running
        if scan_id in self.active_scans:
            self.logger.warning(f"Scan {scan_id} is already running")
            return

        # Start scan in a separate thread
        self.scan_threads[scan_id] = threading.Thread(
            target=self._run_scan,
            args=(scan_id,),
            daemon=True
        )
        self.scan_threads[scan_id].start()

    def _run_scan(self, scan_id: str) -> Dict[str, Any]:
        """
        Run a scan.

        Args:
            scan_id (str): Scan ID

        Returns:
            Dict[str, Any]: Scan result
        """
        # Check if scan exists
        if scan_id not in self.scans:
            self.logger.error(f"Scan {scan_id} not found")
            return None

        # Get scan configuration
        scan = self.scans[scan_id]
        scan_config = scan["config"]

        # Update scan status
        scan["status"] = "running"
        self.active_scans.add(scan_id)

        # Initialize scan metrics
        start_time = time.time()
        scan["start_time"] = start_time

        try:
            # Update progress
            self._update_progress(scan_id, current_phase="discovering_files")

            # Discover files
            files = self._discover_files(
                scan_config["target_directory"],
                recursive=scan_config["recursive"],
                max_depth=scan_config["max_depth"],
                exclude_dirs=scan_config["exclude_dirs"],
                exclude_extensions=scan_config["exclude_extensions"],
                max_file_size_mb=scan_config["max_file_size_mb"]
            )

            # Update progress
            total_files = len(files)
            scan["progress"]["total_files"] = total_files
            self._update_progress(scan_id, current_phase="scanning")

            # Scan files
            results = self._scan_files(
                scan_id,
                files,
                threads=scan_config["threads"],
                batch_size=scan_config["batch_size"],
                enable_yara=scan_config["enable_yara"],
                enable_hash_check=scan_config["enable_hash_check"],
                enable_av_integration=scan_config["enable_av_integration"]
            )

            # Update scan status
            scan["status"] = "completed"
            scan["end_time"] = time.time()
            scan["elapsed_time"] = scan["end_time"] - start_time
            scan["result"] = results

            # Save scan result
            self._save_scan_result(scan_id)

            # Return scan result
            return results

        except Exception as e:
            # Update scan status
            scan["status"] = "error"
            scan["error"] = str(e)
            scan["end_time"] = time.time()
            scan["elapsed_time"] = scan["end_time"] - start_time

            self.logger.error(f"Error running scan {scan_id}: {e}")
            return None

        finally:
            # Remove from active scans
            self.active_scans.discard(scan_id)

    def _discover_files(self, target_directory: str, recursive: bool = True, max_depth: int = 5,
                       exclude_dirs: List[str] = None, exclude_extensions: List[str] = None,
                       max_file_size_mb: int = 100) -> List[str]:
        """
        Discover files to scan.

        Args:
            target_directory (str): Target directory
            recursive (bool, optional): Whether to scan recursively
            max_depth (int, optional): Maximum recursion depth
            exclude_dirs (List[str], optional): Directories to exclude
            exclude_extensions (List[str], optional): File extensions to exclude
            max_file_size_mb (int, optional): Maximum file size in MB

        Returns:
            List[str]: List of file paths
        """
        # Initialize exclude lists
        exclude_dirs = exclude_dirs or []
        exclude_extensions = exclude_extensions or []

        # Convert to absolute paths
        target_directory = os.path.abspath(target_directory)
        exclude_dirs = [os.path.abspath(d) if not os.path.isabs(d) else d for d in exclude_dirs]

        # Discover files
        files = []

        def should_exclude_dir(dir_path: str) -> bool:
            """Check if directory should be excluded."""
            dir_name = os.path.basename(dir_path)
            return (
                dir_path in exclude_dirs or
                dir_name in exclude_dirs or
                dir_name.startswith(".") or
                any(dir_path.startswith(d) for d in exclude_dirs)
            )

        def should_exclude_file(file_path: str) -> bool:
            """Check if file should be excluded."""
            _, ext = os.path.splitext(file_path)
            return (
                ext.lower() in exclude_extensions or
                os.path.getsize(file_path) > max_file_size_mb * 1024 * 1024
            )

        def scan_directory(dir_path: str, current_depth: int = 0) -> None:
            """Scan directory for files."""
            if current_depth > max_depth:
                return

            try:
                for entry in os.scandir(dir_path):
                    try:
                        if entry.is_dir() and recursive:
                            if not should_exclude_dir(entry.path):
                                scan_directory(entry.path, current_depth + 1)
                        elif entry.is_file():
                            try:
                                if not should_exclude_file(entry.path):
                                    files.append(entry.path)
                            except Exception as e:
                                self.logger.warning(f"Error checking file {entry.path}: {e}")
                    except Exception as e:
                        self.logger.warning(f"Error processing entry {entry.path}: {e}")
            except Exception as e:
                self.logger.warning(f"Error scanning directory {dir_path}: {e}")

        # Start scanning
        scan_directory(target_directory)

        return files

    def _scan_files(self, scan_id: str, files: List[str], threads: int = 4, batch_size: int = 20,
                   enable_yara: bool = True, enable_hash_check: bool = True,
                   enable_av_integration: bool = True) -> List[Dict[str, Any]]:
        """
        Scan files.

        Args:
            scan_id (str): Scan ID
            files (List[str]): List of file paths
            threads (int, optional): Number of threads
            batch_size (int, optional): Batch size
            enable_yara (bool, optional): Whether to enable YARA scanning
            enable_hash_check (bool, optional): Whether to enable hash checking
            enable_av_integration (bool, optional): Whether to enable AV integration

        Returns:
            List[Dict[str, Any]]: Scan results
        """
        # Initialize results
        results = []

        # Get stop event
        stop_event = self.scan_stop_events.get(scan_id)

        # Process files in batches
        total_files = len(files)
        processed_files = 0
        matched_files = 0

        # Create batches
        batches = [files[i:i+batch_size] for i in range(0, len(files), batch_size)]

        # Process batches
        for batch_index, batch in enumerate(batches):
            # Check if scan should be stopped
            if stop_event and stop_event.is_set():
                self.logger.info(f"Scan {scan_id} stopped")
                break

            # Update progress
            self._update_progress(
                scan_id,
                processed_files=processed_files,
                matched_files=matched_files,
                current_file=f"Batch {batch_index+1}/{len(batches)}"
            )

            # Scan batch
            batch_results = self._scan_batch(
                batch,
                threads=threads,
                enable_yara=enable_yara,
                enable_hash_check=enable_hash_check,
                enable_av_integration=enable_av_integration
            )

            # Update metrics
            processed_files += len(batch)
            matched_files += sum(1 for r in batch_results if r.get("matches"))

            # Update progress
            self._update_progress(
                scan_id,
                processed_files=processed_files,
                matched_files=matched_files
            )

            # Add batch results to results
            results.extend(batch_results)

        return results

    def _scan_batch(self, batch: List[str], threads: int = 4, enable_yara: bool = True,
                   enable_hash_check: bool = True, enable_av_integration: bool = True) -> List[Dict[str, Any]]:
        """
        Scan a batch of files.

        Args:
            batch (List[str]): Batch of file paths
            threads (int, optional): Number of threads
            enable_yara (bool, optional): Whether to enable YARA scanning
            enable_hash_check (bool, optional): Whether to enable hash checking
            enable_av_integration (bool, optional): Whether to enable AV integration

        Returns:
            List[Dict[str, Any]]: Batch scan results
        """
        # Initialize results
        results = []

        # Scan files in parallel
        with concurrent.futures.ThreadPoolExecutor(max_workers=threads) as executor:
            # Submit tasks
            future_to_file = {
                executor.submit(
                    self._scan_file,
                    file_path,
                    enable_yara=enable_yara,
                    enable_hash_check=enable_hash_check,
                    enable_av_integration=enable_av_integration
                ): file_path
                for file_path in batch
            }

            # Process results
            for future in concurrent.futures.as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    self.logger.error(f"Error scanning file {file_path}: {e}")
                    results.append({
                        "file_path": file_path,
                        "error": str(e)
                    })

        return results

    def _scan_file(self, file_path: str, enable_yara: bool = True, enable_hash_check: bool = True,
                  enable_av_integration: bool = True) -> Dict[str, Any]:
        """
        Scan a file.

        Args:
            file_path (str): File path
            enable_yara (bool, optional): Whether to enable YARA scanning
            enable_hash_check (bool, optional): Whether to enable hash checking
            enable_av_integration (bool, optional): Whether to enable AV integration

        Returns:
            Dict[str, Any]: Scan result
        """
        # Initialize result
        result = {
            "file_path": file_path,
            "file_info": get_file_info(file_path),
            "matches": [],
            "scanned_with": []
        }

        try:
            # YARA scanning
            if enable_yara:
                # This would call the YARA scanner
                result["scanned_with"].append("yara")

            # Hash checking
            if enable_hash_check:
                # This would check file hash against known hashes
                result["scanned_with"].append("hash")

            # AV integration
            if enable_av_integration:
                # This would call the AV scanner
                result["scanned_with"].append("av")

            return result
        except Exception as e:
            self.logger.error(f"Error scanning file {file_path}: {e}")
            result["error"] = str(e)
            return result

    def _save_scan_result(self, scan_id: str) -> None:
        """
        Save scan result to file.

        Args:
            scan_id (str): Scan ID
        """
        # Get scan
        scan = self.scans[scan_id]

        # Save to file
        file_path = os.path.join(self.results_dir, f"{scan_id}.json")
        save_json(scan, file_path)

    def _update_progress(self, scan_id: str, **kwargs) -> None:
        """
        Update scan progress.

        Args:
            scan_id (str): Scan ID
            **kwargs: Progress updates
        """
        # Get scan
        scan = self.scans[scan_id]

        # Update progress
        for key, value in kwargs.items():
            scan["progress"][key] = value

        # Calculate percent complete
        if scan["progress"]["total_files"] > 0:
            scan["progress"]["percent_complete"] = (
                scan["progress"]["processed_files"] / scan["progress"]["total_files"] * 100
            )

        # Calculate elapsed time
        if "start_time" in scan:
            scan["progress"]["elapsed_time"] = time.time() - scan["start_time"]

        # Calculate estimated time remaining
        if (
            scan["progress"]["percent_complete"] > 0 and
            scan["progress"]["elapsed_time"] > 0
        ):
            scan["progress"]["estimated_time_remaining"] = (
                scan["progress"]["elapsed_time"] / scan["progress"]["percent_complete"] * (100 - scan["progress"]["percent_complete"])
            )

    def get_scan_status(self, scan_id: str) -> Optional[Dict[str, Any]]:
        """
        Get scan status.

        Args:
            scan_id (str): Scan ID

        Returns:
            Optional[Dict[str, Any]]: Scan status or None if not found
        """
        # Check if scan exists
        if scan_id not in self.scans:
            return None

        # Get scan
        scan = self.scans[scan_id]

        # Return status
        return {
            "scan_id": scan_id,
            "status": scan["status"],
            "progress": scan["progress"],
            "start_time": scan.get("start_time"),
            "end_time": scan.get("end_time"),
            "elapsed_time": scan.get("elapsed_time")
        }

    def get_scan_result(self, scan_id: str) -> Optional[Dict[str, Any]]:
        """
        Get scan result.

        Args:
            scan_id (str): Scan ID

        Returns:
            Optional[Dict[str, Any]]: Scan result or None if not found
        """
        # Check if scan exists
        if scan_id not in self.scans:
            return None

        # Get scan
        scan = self.scans[scan_id]

        # Check if scan is completed
        if scan["status"] != "completed":
            return None

        # Return result
        return scan["result"]

    def get_all_scans(self) -> List[Dict[str, Any]]:
        """
        Get all scans.

        Returns:
            List[Dict[str, Any]]: List of scan statuses
        """
        # Return all scan statuses
        return [self.get_scan_status(scan_id) for scan_id in self.scans]

    def cancel_scan(self, scan_id: str) -> bool:
        """
        Cancel a scan.

        Args:
            scan_id (str): Scan ID

        Returns:
            bool: True if cancelled successfully, False otherwise
        """
        # Check if scan exists
        if scan_id not in self.scans:
            return False

        # Check if scan is running
        if scan_id not in self.active_scans:
            return False

        # Set stop event
        if scan_id in self.scan_stop_events:
            self.scan_stop_events[scan_id].set()

        # Update scan status
        self.scans[scan_id]["status"] = "cancelled"

        return True

    def get_recent_results(self) -> List[Dict[str, Any]]:
        """
        Get recent scan results.

        Returns:
            List[Dict[str, Any]]: Recent scan results
        """
        # Get completed scans
        completed_scans = [
            scan for scan in self.scans.values()
            if scan["status"] == "completed" and "result" in scan
        ]

        # Sort by end time
        completed_scans.sort(key=lambda s: s.get("end_time", 0), reverse=True)

        # Return recent results
        return [scan["result"] for scan in completed_scans[:10]]

    def handle_alerts(self, alerts: List[Dict[str, Any]]) -> None:
        """
        Handle alerts from monitoring phase.

        Args:
            alerts (List[Dict[str, Any]]): Alerts
        """
        for alert in alerts:
            self.logger.info(f"Received alert: {alert.get('message')}")

            # Check if alert contains file path
            file_path = alert.get("file_path")
            if file_path and os.path.exists(file_path):
                # Scan file
                self.scan_file(file_path, priority=alert.get("priority", 5))

    def scan_file(self, file_path: str, priority: int = 5) -> Dict[str, Any]:
        """
        Scan a single file.

        Args:
            file_path (str): File path
            priority (int, optional): Priority level

        Returns:
            Dict[str, Any]: Scan result
        """
        self.logger.info(f"Scanning file {file_path} with priority {priority}")

        # Scan file
        result = self._scan_file(
            file_path,
            enable_yara=True,
            enable_hash_check=True,
            enable_av_integration=True
        )

        # Check if file matches
        if result.get("matches"):
            self.logger.warning(f"File {file_path} matched rules: {result['matches']}")

            # Notify monitoring phase if available
            if self.monitor_manager:
                self.monitor_manager.handle_detection({
                    "file_path": file_path,
                    "matches": result["matches"],
                    "priority": priority
                })

        return result

    def scan_file(self, file_path: str) -> Dict[str, Any]:
        """
        Scan a single file (for main.py compatibility).

        Args:
            file_path (str): Path to the file to scan

        Returns:
            Dict[str, Any]: Scan result
        """
        try:
            start_time = time.time()

            # Scan the file
            result = self._scan_file(file_path)

            # Add timing information
            scan_time = time.time() - start_time

            # Format result for main.py compatibility
            formatted_result = {
                "file_path": file_path,
                "scan_time": scan_time,
                "matches": result.get("matches", []),
                "scanned_with": result.get("scanned_with", []),
                "file_info": result.get("file_info", {}),
                "status": "completed"
            }

            if "error" in result:
                formatted_result["error"] = result["error"]
                formatted_result["status"] = "error"

            return formatted_result

        except Exception as e:
            return {
                "file_path": file_path,
                "error": str(e),
                "status": "error",
                "scan_time": 0,
                "matches": []
            }
