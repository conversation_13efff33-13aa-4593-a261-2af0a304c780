"""
Tests for the monitor manager module.
"""

import os
import json
import tempfile
import unittest
from unittest.mock import patch, MagicMock
from phases.monitoring.monitor_manager import MonitorManager
from phases.monitoring.alert_manager import AlertManager

class TestMonitorManager(unittest.TestCase):
    """Tests for the MonitorManager class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary directory
        self.temp_dir = tempfile.TemporaryDirectory()
        
        # Create a temporary config file
        self.config_path = os.path.join(self.temp_dir.name, "config.json")
        self.test_config = {
            "monitoring": {
                "enabled": True,
                "process_monitoring": True,
                "filesystem_monitoring": True,
                "network_monitoring": True,
                "check_interval_seconds": 0.1,
                "alert_threshold": 0.7,
                "process": {
                    "suspicious_process_patterns": ["test_pattern"],
                    "memory_usage_threshold_percent": 80,
                    "cpu_usage_threshold_percent": 90
                },
                "filesystem": {
                    "watch_directories": [self.temp_dir.name],
                    "detect_mass_operations": True,
                    "mass_operation_threshold": 5,
                    "mass_operation_time_window_seconds": 10,
                    "suspicious_extensions": [".exe", ".dll"]
                },
                "network": {
                    "suspicious_ports": [4444, 8080],
                    "suspicious_addresses": ["test.com"],
                    "detect_connection_spikes": True,
                    "connection_spike_threshold": 10
                },
                "alert": {
                    "log_alerts": True,
                    "alert_level": "info",
                    "max_alerts_per_minute": 20,
                    "deduplicate_alerts": True,
                    "deduplication_window_seconds": 300
                },
                "response": {
                    "enabled": True,
                    "auto_quarantine": False,
                    "auto_block_process": False,
                    "auto_block_network": False,
                    "quarantine_directory": os.path.join(self.temp_dir.name, "quarantine")
                }
            },
            "output": {
                "log_directory": os.path.join(self.temp_dir.name, "logs"),
                "output_directory": os.path.join(self.temp_dir.name, "output"),
                "log_level": "info"
            }
        }
        
        with open(self.config_path, "w") as f:
            json.dump(self.test_config, f)
    
    def tearDown(self):
        """Tear down test fixtures."""
        # Remove the temporary directory
        self.temp_dir.cleanup()
    
    @patch("phases.monitoring.monitor_manager.MonitorManager._initialize_components")
    def test_start_monitoring(self, mock_initialize_components):
        """Test starting monitoring."""
        # Create monitor manager
        manager = MonitorManager(self.config_path)
        
        # Mock components
        manager.process_monitor = MagicMock()
        manager.filesystem_monitor = MagicMock()
        manager.network_monitor = MagicMock()
        manager.event_correlator = MagicMock()
        manager.response_manager = MagicMock()
        
        # Start monitoring
        result = manager.start_monitoring()
        
        # Check result
        self.assertTrue(result)
        self.assertTrue(manager.is_running_flag)
        
        # Check if components were started
        manager.process_monitor.start.assert_called_once()
        manager.filesystem_monitor.start.assert_called_once()
        manager.network_monitor.start.assert_called_once()
        manager.event_correlator.start.assert_called_once()
        manager.response_manager.start.assert_called_once()
    
    @patch("phases.monitoring.monitor_manager.MonitorManager._initialize_components")
    def test_stop_monitoring(self, mock_initialize_components):
        """Test stopping monitoring."""
        # Create monitor manager
        manager = MonitorManager(self.config_path)
        
        # Mock components
        manager.process_monitor = MagicMock()
        manager.filesystem_monitor = MagicMock()
        manager.network_monitor = MagicMock()
        manager.event_correlator = MagicMock()
        manager.response_manager = MagicMock()
        
        # Set running flag
        manager.is_running_flag = True
        
        # Stop monitoring
        result = manager.stop_monitoring()
        
        # Check result
        self.assertTrue(result)
        self.assertFalse(manager.is_running_flag)
        
        # Check if components were stopped
        manager.process_monitor.stop.assert_called_once()
        manager.filesystem_monitor.stop.assert_called_once()
        manager.network_monitor.stop.assert_called_once()
        manager.event_correlator.stop.assert_called_once()
        manager.response_manager.stop.assert_called_once()
    
    @patch("phases.monitoring.monitor_manager.MonitorManager._initialize_components")
    def test_get_status(self, mock_initialize_components):
        """Test getting monitoring status."""
        # Create monitor manager
        manager = MonitorManager(self.config_path)
        
        # Mock components
        manager.process_monitor = MagicMock()
        manager.process_monitor.is_running.return_value = True
        
        manager.filesystem_monitor = MagicMock()
        manager.filesystem_monitor.is_running.return_value = True
        
        manager.network_monitor = MagicMock()
        manager.network_monitor.is_running.return_value = True
        
        manager.event_correlator = MagicMock()
        manager.event_correlator.is_running.return_value = True
        
        manager.response_manager = MagicMock()
        manager.response_manager.is_running.return_value = True
        
        # Set running flag and start time
        manager.is_running_flag = True
        manager.start_time = 1000.0
        
        # Set metrics
        manager.metrics = {
            "alerts": 5,
            "processes": 10,
            "file_operations": 15,
            "network_connections": 20,
            "correlated_events": 2,
            "responses": 3
        }
        
        # Get status
        status = manager.get_status()
        
        # Check status
        self.assertTrue(status["is_running"])
        self.assertEqual(status["start_time"], 1000.0)
        self.assertIsNotNone(status["uptime_seconds"])
        self.assertEqual(len(status["active_monitors"]), 5)
        self.assertEqual(status["alert_count"], 5)
        self.assertEqual(status["process_count"], 10)
        self.assertEqual(status["file_operation_count"], 15)
        self.assertEqual(status["network_connection_count"], 20)
    
    @patch("phases.monitoring.monitor_manager.MonitorManager._initialize_components")
    def test_handle_detection(self, mock_initialize_components):
        """Test handling a detection."""
        # Create monitor manager
        manager = MonitorManager(self.config_path)
        
        # Mock alert manager
        manager.alert_manager = MagicMock()
        
        # Mock filesystem monitor
        manager.filesystem_monitor = MagicMock()
        
        # Create detection
        detection = {
            "file_path": os.path.join(self.temp_dir.name, "test_file.txt"),
            "matches": ["test_match"]
        }
        
        # Handle detection
        manager.handle_detection(detection)
        
        # Check if alert was created
        manager.alert_manager.add_alert.assert_called_once()
        
        # Check if file is being monitored
        manager.filesystem_monitor.monitor_file.assert_called_once_with(detection["file_path"])

if __name__ == "__main__":
    unittest.main()
