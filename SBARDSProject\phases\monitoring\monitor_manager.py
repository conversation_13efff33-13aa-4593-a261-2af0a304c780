"""
Monitor Manager for SBARDS

This module provides the main monitoring manager for the SBARDS project.

The Monitor Manager is responsible for:
1. Initializing and coordinating all monitoring components
2. Starting and stopping monitoring activities
3. Managing the lifecycle of platform-specific monitors
4. Collecting and aggregating monitoring data
5. Providing status information and alerts to the API

It serves as the central coordination point for the Monitoring Phase and
maintains separation of concerns by delegating specific monitoring tasks
to specialized monitor implementations.

Dependencies:
- AlertManager: For alert processing and management
- Platform-specific monitors: For OS-specific monitoring functionality

Usage:
    config = load_config()
    monitor_manager = MonitorManager(config)
    monitor_manager.start_monitoring()
    # ... perform other operations ...
    monitor_manager.stop_monitoring()
"""

import os
import time
import logging
import threading
import platform
from datetime import datetime
from typing import Dict, List, Any, Optional, Set

class MonitorManager:
    """
    Monitor Manager for SBARDS.

    This class manages all monitoring components and coordinates their activities.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the monitor manager.

        Args:
            config (Dict[str, Any]): Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger("SBARDS.MonitorManager")

        # Platform detection
        self.platform = platform.system().lower()
        self.logger.info(f"Detected platform: {self.platform}")

        # Alert manager
        from phases.monitoring.alerts.alert_manager import AlertManager
        self.alert_manager = AlertManager(config)

        # Monitoring components
        self.monitors = {}

        # Initialize platform-specific monitors
        self._init_platform_monitors()

        # Monitoring thread
        self.monitoring_thread = None
        self.stop_event = threading.Event()
        self.is_running = False

        # Monitoring interval - use a shorter interval for more responsive monitoring
        self.monitoring_interval = config.get("monitoring", {}).get("interval_seconds", 2)

        # Set up whole device monitoring
        self.monitor_whole_device = config.get("monitoring", {}).get("monitor_whole_device", True)

        # File change tracking
        self.file_change_history = {}
        self.file_change_lock = threading.Lock()

        self.logger.info("Monitor Manager initialized")

    def _init_platform_monitors(self) -> None:
        """Initialize platform-specific monitors."""
        if self.platform == "windows":
            self._init_windows_monitors()
        elif self.platform == "linux":
            self._init_linux_monitors()
        else:
            self.logger.warning(f"Unsupported platform: {self.platform}")

    def _init_windows_monitors(self) -> None:
        """Initialize Windows-specific monitors."""
        # Check if we should use mock monitors
        use_mock = self.config.get("monitoring", {}).get("use_mock_monitors", True)

        if use_mock:
            # Import mock monitors
            from phases.monitoring.platforms.windows.mock_osquery_monitor import MockOSQueryMonitor
            from phases.monitoring.platforms.windows.mock_sysmon_monitor import MockSysmonMonitor
            from phases.monitoring.platforms.windows.mock_etw_monitor import MockETWMonitor

            # Initialize mock monitors
            if self.config.get("monitoring", {}).get("enable_osquery", True):
                self.monitors["osquery"] = MockOSQueryMonitor(self.config)
                self.monitors["osquery"].set_alert_manager(self.alert_manager)

            if self.config.get("monitoring", {}).get("enable_sysmon", True):
                self.monitors["sysmon"] = MockSysmonMonitor(self.config)
                self.monitors["sysmon"].set_alert_manager(self.alert_manager)

            if self.config.get("monitoring", {}).get("enable_etw", True):
                self.monitors["etw"] = MockETWMonitor(self.config)
                self.monitors["etw"].set_alert_manager(self.alert_manager)

            self.logger.info("Using mock monitors for Windows")
        else:
            # Try to import real monitors
            try:
                # Import Windows-specific monitors
                from phases.monitoring.platforms.windows.osquery_monitor import OSQueryMonitor
                from phases.monitoring.platforms.windows.sysmon_monitor import SysmonMonitor
                from phases.monitoring.platforms.windows.etw_monitor import ETWMonitor

                # Initialize OSQuery monitor
                if self.config.get("monitoring", {}).get("enable_osquery", True):
                    self.monitors["osquery"] = OSQueryMonitor(self.config, self.alert_manager)

                # Initialize Sysmon monitor
                if self.config.get("monitoring", {}).get("enable_sysmon", True):
                    self.monitors["sysmon"] = SysmonMonitor(self.config, self.alert_manager)

                # Initialize ETW monitor
                if self.config.get("monitoring", {}).get("enable_etw", True):
                    self.monitors["etw"] = ETWMonitor(self.config, self.alert_manager)
            except Exception as e:
                # Fall back to mock monitors if real monitors fail
                self.logger.error(f"Error initializing Windows monitors: {e}")
                self.logger.warning("Falling back to mock monitors")

                # Import mock monitors
                from phases.monitoring.platforms.windows.mock_osquery_monitor import MockOSQueryMonitor
                from phases.monitoring.platforms.windows.mock_sysmon_monitor import MockSysmonMonitor
                from phases.monitoring.platforms.windows.mock_etw_monitor import MockETWMonitor

                # Initialize mock monitors
                if self.config.get("monitoring", {}).get("enable_osquery", True):
                    self.monitors["osquery"] = MockOSQueryMonitor(self.config)
                    self.monitors["osquery"].set_alert_manager(self.alert_manager)

                if self.config.get("monitoring", {}).get("enable_sysmon", True):
                    self.monitors["sysmon"] = MockSysmonMonitor(self.config)
                    self.monitors["sysmon"].set_alert_manager(self.alert_manager)

                if self.config.get("monitoring", {}).get("enable_etw", True):
                    self.monitors["etw"] = MockETWMonitor(self.config)
                    self.monitors["etw"].set_alert_manager(self.alert_manager)

    def _init_linux_monitors(self) -> None:
        """Initialize Linux-specific monitors."""
        # Import Linux-specific monitors
        try:
            from phases.monitoring.platforms.linux.osquery_monitor import OSQueryMonitor
            from phases.monitoring.platforms.linux.auditd_monitor import AuditDMonitor
            from phases.monitoring.platforms.linux.syslog_monitor import SyslogMonitor
            from phases.monitoring.platforms.linux.zeek_monitor import ZeekMonitor

            # Initialize OSQuery monitor
            if self.config.get("monitoring", {}).get("enable_osquery", True):
                self.monitors["osquery"] = OSQueryMonitor(self.config, self.alert_manager)

            # Initialize AuditD monitor
            if self.config.get("monitoring", {}).get("enable_auditd", True):
                self.monitors["auditd"] = AuditDMonitor(self.config, self.alert_manager)

            # Initialize Syslog monitor
            if self.config.get("monitoring", {}).get("enable_syslog", True):
                self.monitors["syslog"] = SyslogMonitor(self.config, self.alert_manager)

            # Initialize Zeek monitor
            if self.config.get("monitoring", {}).get("enable_zeek", True):
                self.monitors["zeek"] = ZeekMonitor(self.config, self.alert_manager)

        except ImportError as e:
            self.logger.warning(f"Error importing Linux monitors: {e}")

    def start_monitoring(self) -> bool:
        """
        Start monitoring.

        Returns:
            bool: True if successful, False otherwise
        """
        if self.is_running:
            self.logger.warning("Monitoring is already running")
            return True

        self.logger.info("Starting monitoring")
        self.stop_event.clear()

        # Start alert manager
        self.alert_manager.start()

        # Start all monitors
        for name, monitor in self.monitors.items():
            try:
                self.logger.info(f"Starting monitor: {name}")
                monitor.start_monitoring(self.stop_event)
            except Exception as e:
                self.logger.error(f"Error starting monitor {name}: {e}")

        # Start monitoring thread
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self.monitoring_thread.start()

        self.is_running = True
        return True

    def stop_monitoring(self) -> bool:
        """
        Stop monitoring.

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.is_running:
            return True

        self.logger.info("Stopping monitoring")
        self.stop_event.set()

        # Stop all monitors
        for name, monitor in self.monitors.items():
            try:
                self.logger.info(f"Stopping monitor: {name}")
                monitor.stop_monitoring()
            except Exception as e:
                self.logger.error(f"Error stopping monitor {name}: {e}")

        # Stop alert manager
        self.alert_manager.stop()

        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=10.0)

        self.is_running = False
        return True

    def _monitoring_loop(self) -> None:
        """Monitoring loop."""
        while not self.stop_event.is_set():
            try:
                # Check monitor status
                for name, monitor in self.monitors.items():
                    if hasattr(monitor, "is_running") and not monitor.is_running:
                        self.logger.warning(f"Monitor {name} is not running, attempting to restart")
                        try:
                            monitor.start_monitoring(self.stop_event)
                        except Exception as e:
                            self.logger.error(f"Error restarting monitor {name}: {e}")

                # Wait for next monitoring cycle
                self.stop_event.wait(self.monitoring_interval)

            except Exception as e:
                self.logger.error(f"Error during monitoring: {e}")
                # Wait a bit before retrying
                self.stop_event.wait(1.0)

        self.logger.info("Monitoring stopped")

    def get_status(self, detailed: bool = False) -> Dict[str, Any]:
        """
        Get monitoring status.

        Args:
            detailed (bool): Whether to include detailed information

        Returns:
            Dict[str, Any]: Monitoring status
        """
        status = {
            "is_running": self.is_running,
            "platform": self.platform,
            "monitors": {},
            "monitor_whole_device": self.monitor_whole_device,
            "monitoring_interval": self.monitoring_interval
        }

        # Get status of all monitors
        for name, monitor in self.monitors.items():
            monitor_status = {
                "is_running": getattr(monitor, "is_running", False)
            }

            # Add detailed information if requested
            if detailed and hasattr(monitor, "get_detailed_status"):
                monitor_status.update(monitor.get_detailed_status())

            status["monitors"][name] = monitor_status

        # Get alert manager status
        status["alert_manager"] = {
            "is_running": self.alert_manager.is_running,
            "alert_count": len(self.alert_manager.alerts)
        }

        # Add file change statistics
        file_changes = self.get_file_changes(limit=1)
        status["file_changes"] = {
            "total_tracked_files": len(self.file_change_history),
            "recent_changes": len(file_changes)
        }

        return status

    def get_alerts(self, limit: int = 100, severity: Optional[str] = None, alert_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get alerts.

        Args:
            limit (int): Maximum number of alerts to return
            severity (Optional[str]): Filter alerts by severity
            alert_type (Optional[str]): Filter alerts by type

        Returns:
            List[Dict[str, Any]]: List of alerts
        """
        alerts = self.alert_manager.get_alerts(limit, severity)

        # Filter by alert type if specified
        if alert_type:
            alerts = [
                alert for alert in alerts
                if alert.get("type", "").lower() == alert_type.lower()
            ]

        return alerts[:limit]

    def get_file_changes(self, limit: int = 100, path_filter: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get file changes.

        Args:
            limit (int): Maximum number of file changes to return
            path_filter (Optional[str]): Filter file changes by path

        Returns:
            List[Dict[str, Any]]: List of file changes
        """
        # Collect file changes from all monitors
        file_changes = []

        # Get file changes from OSQuery monitor
        for name, monitor in self.monitors.items():
            if name == "osquery" and hasattr(monitor, "file_change_history"):
                with self.file_change_lock:
                    # Update our file change history with the monitor's history
                    self.file_change_history.update(monitor.file_change_history)

        # Convert file change history to list
        with self.file_change_lock:
            for file_path, change_info in self.file_change_history.items():
                # Skip if path filter is specified and doesn't match
                if path_filter and path_filter.lower() not in file_path.lower():
                    continue

                file_changes.append({
                    "file_path": file_path,
                    "size": change_info.get("size", 0),
                    "mtime": change_info.get("mtime", 0),
                    "first_seen": change_info.get("first_seen", 0),
                    "last_change": change_info.get("last_change", 0)
                })

        # Sort by last change time (newest first)
        file_changes.sort(key=lambda x: x.get("last_change", 0), reverse=True)

        # Limit number of file changes
        return file_changes[:limit]

    def add_threats(self, threats: List[Dict[str, Any]]) -> None:
        """
        Add threats detected by the pre-scanning phase.

        Args:
            threats (List[Dict[str, Any]]): List of threats
        """
        for threat in threats:
            self.alert_manager.add_alert(
                source="PreScanning",
                alert_type="yara_match",
                message=f"YARA match: {threat.get('rule', 'unknown')} in {threat.get('file_path', 'unknown')}",
                severity=threat.get('severity', 'warning'),
                details=threat
            )

    def update_with_workflow_results(self, workflow_results: Dict[str, Any]) -> bool:
        """
        Update monitoring with workflow results.

        Args:
            workflow_results (Dict[str, Any]): Workflow results from orchestrator

        Returns:
            bool: True if successful
        """
        try:
            # Extract relevant information from workflow results
            workflow_id = workflow_results.get("workflow_id", "unknown")
            decision = workflow_results.get("final_decision", {}).get("decision", "UNKNOWN")

            # Create monitoring event
            monitoring_event = {
                "timestamp": datetime.now().isoformat(),
                "event_type": "workflow_completed",
                "workflow_id": workflow_id,
                "decision": decision,
                "source": "workflow_orchestrator",
                "details": workflow_results
            }

            # Add to alert manager if decision requires attention
            if decision in ["QUARANTINED", "ISOLATED", "ERROR"]:
                alert_level = "CRITICAL" if decision == "QUARANTINED" else "HIGH"
                alert = {
                    "timestamp": datetime.now().isoformat(),
                    "level": alert_level,
                    "source": "workflow_orchestrator",
                    "message": f"Workflow {workflow_id} completed with decision: {decision}",
                    "details": workflow_results
                }

                if self.alert_manager:
                    self.alert_manager.add_alert(alert)

            # Update monitoring statistics
            self._update_monitoring_stats(workflow_results)

            self.logger.info(f"Updated monitoring with workflow {workflow_id} results: {decision}")
            return True

        except Exception as e:
            self.logger.error(f"Error updating monitoring with workflow results: {e}")
            return False

    def _update_monitoring_stats(self, workflow_results: Dict[str, Any]) -> None:
        """
        Update monitoring statistics with workflow results.

        Args:
            workflow_results (Dict[str, Any]): Workflow results
        """
        try:
            decision = workflow_results.get("final_decision", {}).get("decision", "UNKNOWN")

            # Initialize stats if not exists
            if not hasattr(self, "workflow_stats"):
                self.workflow_stats = {
                    "total_workflows": 0,
                    "allowed": 0,
                    "quarantined": 0,
                    "isolated": 0,
                    "errors": 0,
                    "last_updated": None
                }

            # Update counters
            self.workflow_stats["total_workflows"] += 1

            if decision == "ALLOWED":
                self.workflow_stats["allowed"] += 1
            elif decision == "QUARANTINED":
                self.workflow_stats["quarantined"] += 1
            elif decision == "ISOLATED":
                self.workflow_stats["isolated"] += 1
            elif decision == "ERROR":
                self.workflow_stats["errors"] += 1

            self.workflow_stats["last_updated"] = datetime.now().isoformat()

        except Exception as e:
            self.logger.error(f"Error updating monitoring stats: {e}")

    def get_workflow_stats(self) -> Dict[str, Any]:
        """
        Get workflow statistics.

        Returns:
            Dict[str, Any]: Workflow statistics
        """
        if hasattr(self, "workflow_stats"):
            return self.workflow_stats.copy()
        else:
            return {
                "total_workflows": 0,
                "allowed": 0,
                "quarantined": 0,
                "isolated": 0,
                "errors": 0,
                "last_updated": None
            }
