"""
Test script to demonstrate the optimized inspection layer.
"""

import os
import sys
import time
import logging
import argparse
from datetime import datetime

# Add parent directory to path to import SBARDS modules
sys.path.append(os.path.abspath(os.path.dirname(__file__)))
from scanner_core.python.orchestrator import Orchestrator
from scanner_core.inspection import OptimizedInspector
from scanner_core.python.yara_wrapper import YaraScanner

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("SBARDS.TestOptimizedInspection")

def test_optimized_inspection(target_dir=None, compare_with_standard=False):
    """
    Test the optimized inspection layer.
    
    Args:
        target_dir (str, optional): Target directory to scan
        compare_with_standard (bool): Whether to compare with standard scanning
    """
    logger.info("Testing optimized inspection layer")
    
    # Initialize orchestrator
    orchestrator = Orchestrator("config.json")
    
    # Get target directory from config if not provided
    if not target_dir:
        target_dir = orchestrator.config["scanner"]["target_directory"]
        
    logger.info(f"Target directory: {target_dir}")
    
    # Get all files in the target directory
    files_to_scan = []
    for root, _, files in os.walk(target_dir):
        for file in files:
            file_path = os.path.join(root, file)
            files_to_scan.append(file_path)
            
    logger.info(f"Found {len(files_to_scan)} files to scan")
    
    # Initialize optimized inspector
    optimized_inspector = orchestrator.optimized_inspector
    
    if not optimized_inspector:
        logger.error("Optimized inspector is not enabled in configuration")
        return
        
    # Define callback for real-time logging
    def scan_callback(file_path, matches):
        if matches:
            logger.info(f"Found {len(matches)} matches in {file_path}")
            for match in matches:
                logger.info(f"  - Rule: {match['rule']} (Namespace: {match.get('namespace', 'N/A')})")
                
    # Run optimized scan
    logger.info("Starting optimized scan")
    start_time = time.time()
    optimized_results = optimized_inspector.scan_files(files_to_scan, scan_callback)
    optimized_time = time.time() - start_time
    
    # Log performance statistics
    stats = optimized_inspector.get_stats()
    logger.info(f"Optimized scan completed in {optimized_time:.2f} seconds")
    logger.info(f"Files scanned: {stats['files_scanned']}, Files matched: {stats['files_matched']}")
    logger.info(f"Cache hits: {stats['cache_hits']}, Early terminations: {stats['early_terminations']}")
    logger.info(f"Average scan time per file: {stats.get('avg_scan_time', 0):.4f} seconds")
    
    # Compare with standard scanning if requested
    if compare_with_standard:
        logger.info("Starting standard scan for comparison")
        
        # Run standard parallel scan
        start_time = time.time()
        standard_results = orchestrator._parallel_scan(orchestrator.yara_scanner, files_to_scan)
        standard_time = time.time() - start_time
        
        logger.info(f"Standard scan completed in {standard_time:.2f} seconds")
        
        # Compare results
        optimized_matches = sum(1 for matches in optimized_results.values() if matches)
        standard_matches = sum(1 for matches in standard_results.values() if matches)
        
        logger.info(f"Optimized scan found {optimized_matches} files with matches")
        logger.info(f"Standard scan found {standard_matches} files with matches")
        
        # Calculate speedup
        speedup = standard_time / optimized_time if optimized_time > 0 else float('inf')
        logger.info(f"Speedup: {speedup:.2f}x")
        
        # Verify results match
        result_differences = 0
        for file_path in files_to_scan:
            opt_matches = set(match["rule"] for match in optimized_results.get(file_path, []))
            std_matches = set(match["rule"] for match in standard_results.get(file_path, []))
            
            if opt_matches != std_matches:
                result_differences += 1
                logger.warning(f"Result mismatch for {file_path}")
                logger.warning(f"  Optimized: {opt_matches}")
                logger.warning(f"  Standard: {std_matches}")
                
        if result_differences == 0:
            logger.info("All results match between optimized and standard scanning")
        else:
            logger.warning(f"Found {result_differences} files with result differences")
            
    # Return results
    return {
        "optimized_time": optimized_time,
        "optimized_results": optimized_results,
        "stats": stats
    }

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Test the optimized inspection layer")
    parser.add_argument("--target", help="Target directory to scan")
    parser.add_argument("--compare", action="store_true", help="Compare with standard scanning")
    args = parser.parse_args()
    
    test_optimized_inspection(args.target, args.compare)

if __name__ == "__main__":
    main()
