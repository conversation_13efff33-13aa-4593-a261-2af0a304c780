import os
import json
import platform
import logging

class ConfigLoader:
    """
    Configuration loader for the SBARDS Project.
    Loads and validates configuration from a JSON file.
    """
    
    def __init__(self, config_path="config.json"):
        """
        Initialize the configuration loader.
        
        Args:
            config_path (str): Path to the configuration file
        """
        self.config_path = os.path.abspath(config_path)
        self.config = self._load_config()
        self._validate_config()
        self._setup_platform_specifics()
        
    def _load_config(self):
        """
        Load configuration from the JSON file.
        
        Returns:
            dict: Configuration dictionary
        """
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config
        except FileNotFoundError:
            logging.error(f"Configuration file not found: {self.config_path}")
            return self._create_default_config()
        except json.JSONDecodeError:
            logging.error(f"Invalid JSON in configuration file: {self.config_path}")
            return self._create_default_config()
    
    def _create_default_config(self):
        """
        Create a default configuration.
        
        Returns:
            dict: Default configuration dictionary
        """
        default_config = {
            "scanner": {
                "target_directory": "samples",
                "recursive": True,
                "max_depth": 5,
                "exclude_dirs": [],
                "exclude_extensions": [],
                "max_file_size_mb": 100
            },
            "rules": {
                "rule_files": ["rules/custom_rules.yar"],
                "enable_categories": ["all"]
            },
            "output": {
                "log_directory": "logs",
                "output_directory": "output",
                "json_output": True,
                "csv_output": False,
                "html_report": False,
                "log_level": "info"
            },
            "performance": {
                "threads": 1,
                "batch_size": 10,
                "timeout_seconds": 30
            }
        }
        
        # Save the default configuration
        os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
        with open(self.config_path, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=4)
        
        return default_config
    
    def _validate_config(self):
        """
        Validate the configuration.
        """
        # Ensure required sections exist
        required_sections = ["scanner", "rules", "output", "performance"]
        for section in required_sections:
            if section not in self.config:
                logging.warning(f"Missing configuration section: {section}")
                self.config[section] = self._create_default_config()[section]
        
        # Ensure target directory exists
        target_dir = self.config["scanner"]["target_directory"]
        if not os.path.exists(target_dir):
            logging.warning(f"Target directory does not exist: {target_dir}")
            if target_dir != "samples":
                logging.info("Falling back to 'samples' directory")
                self.config["scanner"]["target_directory"] = "samples"
                os.makedirs("samples", exist_ok=True)
    
    def _setup_platform_specifics(self):
        """
        Set up platform-specific configurations.
        """
        self.platform = platform.system()
        
        # Add platform-specific exclusions
        if self.platform == "Windows":
            win_exclusions = [
                "System Volume Information",
                "$RECYCLE.BIN",
                "Windows",
                "Program Files",
                "Program Files (x86)"
            ]
            self.config["scanner"]["exclude_dirs"].extend(
                [d for d in win_exclusions if d not in self.config["scanner"]["exclude_dirs"]]
            )
        elif self.platform == "Linux":
            linux_exclusions = [
                "/proc",
                "/sys",
                "/dev",
                "/run",
                "/tmp"
            ]
            self.config["scanner"]["exclude_dirs"].extend(
                [d for d in linux_exclusions if d not in self.config["scanner"]["exclude_dirs"]]
            )
    
    def get_config(self):
        """
        Get the configuration.
        
        Returns:
            dict: Configuration dictionary
        """
        return self.config
    
    def get_scanner_config(self):
        """
        Get the scanner configuration.
        
        Returns:
            dict: Scanner configuration dictionary
        """
        return self.config["scanner"]
    
    def get_rules_config(self):
        """
        Get the rules configuration.
        
        Returns:
            dict: Rules configuration dictionary
        """
        return self.config["rules"]
    
    def get_output_config(self):
        """
        Get the output configuration.
        
        Returns:
            dict: Output configuration dictionary
        """
        return self.config["output"]
    
    def get_performance_config(self):
        """
        Get the performance configuration.
        
        Returns:
            dict: Performance configuration dictionary
        """
        return self.config["performance"]
