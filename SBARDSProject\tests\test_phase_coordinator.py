"""
Tests for the phase coordinator module.
"""

import os
import json
import tempfile
import unittest
from unittest.mock import patch, MagicMock
from phases.integration.phase_coordinator import PhaseCoordinator, SharedState

class TestSharedState(unittest.TestCase):
    """Tests for the SharedState class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create shared state
        self.shared_state = SharedState()
    
    def test_add_prescanning_result(self):
        """Test adding a prescanning result."""
        # Create result
        result = {
            "scan_id": "test_scan_id",
            "target_directory": "/test/dir",
            "matches": ["test_match"]
        }
        
        # Add result
        self.shared_state.add_prescanning_result(result)
        
        # Check result
        self.assertIn("test_scan_id", self.shared_state._prescanning_results)
        self.assertEqual(self.shared_state._prescanning_results["test_scan_id"], result)
    
    def test_get_prescanning_result(self):
        """Test getting a prescanning result."""
        # Create result
        result = {
            "scan_id": "test_scan_id",
            "target_directory": "/test/dir",
            "matches": ["test_match"]
        }
        
        # Add result
        self.shared_state._prescanning_results["test_scan_id"] = result
        
        # Get result
        retrieved_result = self.shared_state.get_prescanning_result("test_scan_id")
        
        # Check result
        self.assertEqual(retrieved_result, result)
    
    def test_get_all_prescanning_results(self):
        """Test getting all prescanning results."""
        # Create results
        result1 = {
            "scan_id": "test_scan_id_1",
            "target_directory": "/test/dir/1",
            "matches": ["test_match_1"]
        }
        
        result2 = {
            "scan_id": "test_scan_id_2",
            "target_directory": "/test/dir/2",
            "matches": ["test_match_2"]
        }
        
        # Add results
        self.shared_state._prescanning_results["test_scan_id_1"] = result1
        self.shared_state._prescanning_results["test_scan_id_2"] = result2
        
        # Get all results
        results = self.shared_state.get_all_prescanning_results()
        
        # Check results
        self.assertEqual(len(results), 2)
        self.assertEqual(results["test_scan_id_1"], result1)
        self.assertEqual(results["test_scan_id_2"], result2)
    
    def test_add_monitoring_alert(self):
        """Test adding a monitoring alert."""
        # Create alert
        alert = {
            "id": "test_alert_id",
            "level": "warning",
            "message": "Test alert"
        }
        
        # Add alert
        self.shared_state.add_monitoring_alert(alert)
        
        # Check alert
        self.assertIn(alert, self.shared_state._monitoring_alerts)
    
    def test_get_new_monitoring_alerts(self):
        """Test getting new monitoring alerts."""
        # Create alerts
        alert1 = {
            "id": "test_alert_id_1",
            "level": "warning",
            "message": "Test alert 1"
        }
        
        alert2 = {
            "id": "test_alert_id_2",
            "level": "critical",
            "message": "Test alert 2"
        }
        
        # Add alerts
        self.shared_state._monitoring_alerts.append(alert1)
        self.shared_state._monitoring_alerts.append(alert2)
        
        # Mark alert1 as processed
        self.shared_state._processed_alerts.add("test_alert_id_1")
        
        # Get new alerts
        new_alerts = self.shared_state.get_new_monitoring_alerts()
        
        # Check alerts
        self.assertEqual(len(new_alerts), 1)
        self.assertEqual(new_alerts[0], alert2)
    
    def test_mark_monitoring_alerts_as_processed(self):
        """Test marking monitoring alerts as processed."""
        # Create alerts
        alert1 = {
            "id": "test_alert_id_1",
            "level": "warning",
            "message": "Test alert 1"
        }
        
        alert2 = {
            "id": "test_alert_id_2",
            "level": "critical",
            "message": "Test alert 2"
        }
        
        # Add alerts
        self.shared_state._monitoring_alerts.append(alert1)
        self.shared_state._monitoring_alerts.append(alert2)
        
        # Mark alerts as processed
        self.shared_state.mark_monitoring_alerts_as_processed()
        
        # Check processed alerts
        self.assertIn("test_alert_id_1", self.shared_state._processed_alerts)
        self.assertIn("test_alert_id_2", self.shared_state._processed_alerts)
    
    def test_set_and_get_shared_data(self):
        """Test setting and getting shared data."""
        # Set data
        self.shared_state.set_shared_data("test_key", "test_value")
        
        # Get data
        value = self.shared_state.get_shared_data("test_key")
        
        # Check value
        self.assertEqual(value, "test_value")
        
        # Get non-existent data
        default_value = "default"
        value = self.shared_state.get_shared_data("non_existent_key", default_value)
        
        # Check value
        self.assertEqual(value, default_value)

class TestPhaseCoordinator(unittest.TestCase):
    """Tests for the PhaseCoordinator class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary directory
        self.temp_dir = tempfile.TemporaryDirectory()
        
        # Create a temporary config file
        self.config_path = os.path.join(self.temp_dir.name, "config.json")
        self.test_config = {
            "integration": {
                "enabled": True,
                "coordination_interval_seconds": 0.1,
                "fast_track_enabled": True,
                "fast_track_priority_threshold": 7
            },
            "output": {
                "log_directory": os.path.join(self.temp_dir.name, "logs"),
                "log_level": "info"
            }
        }
        
        with open(self.config_path, "w") as f:
            json.dump(self.test_config, f)
    
    def tearDown(self):
        """Tear down test fixtures."""
        # Remove the temporary directory
        self.temp_dir.cleanup()
    
    def test_start_coordination(self):
        """Test starting coordination."""
        # Create phase coordinator
        coordinator = PhaseCoordinator(self.config_path)
        
        # Start coordination
        result = coordinator.start_coordination()
        
        # Check result
        self.assertTrue(result)
        self.assertTrue(coordinator.is_running)
        
        # Stop coordination
        coordinator.stop_coordination()
    
    def test_stop_coordination(self):
        """Test stopping coordination."""
        # Create phase coordinator
        coordinator = PhaseCoordinator(self.config_path)
        
        # Start coordination
        coordinator.start_coordination()
        
        # Stop coordination
        result = coordinator.stop_coordination()
        
        # Check result
        self.assertTrue(result)
        self.assertFalse(coordinator.is_running)
    
    def test_add_task(self):
        """Test adding a task."""
        # Create phase coordinator
        coordinator = PhaseCoordinator(self.config_path)
        
        # Create mock task
        mock_task = MagicMock()
        
        # Add task
        coordinator.add_task(mock_task, args=(1, 2), kwargs={"key": "value"}, priority=3)
        
        # Check task queue
        self.assertEqual(coordinator.task_queue.qsize(), 1)
        
        # Get task
        priority, task, args, kwargs = coordinator.task_queue.get()
        
        # Check task
        self.assertEqual(priority, 3)
        self.assertEqual(task, mock_task)
        self.assertEqual(args, (1, 2))
        self.assertEqual(kwargs, {"key": "value"})
    
    def test_get_metrics(self):
        """Test getting metrics."""
        # Create phase coordinator
        coordinator = PhaseCoordinator(self.config_path)
        
        # Set metrics
        coordinator.metrics = {
            "coordination_calls": 10,
            "shared_detections": 5,
            "fast_track_requests": 2,
            "response_time_ms": [10.0, 20.0, 30.0]
        }
        
        # Get metrics
        metrics = coordinator.get_metrics()
        
        # Check metrics
        self.assertEqual(metrics["coordination_calls"], 10)
        self.assertEqual(metrics["shared_detections"], 5)
        self.assertEqual(metrics["fast_track_requests"], 2)
        self.assertEqual(metrics["avg_response_time_ms"], 20.0)

if __name__ == "__main__":
    unittest.main()
