"""
Phase Coordinator for SBARDS

This module provides coordination between different phases of the SBARDS project.
"""

import os
import time
import logging
import threading
from typing import Dict, List, Any, Optional

class PhaseCoordinator:
    """
    Coordinates between different phases of the SBARDS project.
    
    This class manages the interaction between the Pre-Scanning and Monitoring phases,
    allowing them to share information and work together.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the phase coordinator.
        
        Args:
            config (Dict[str, Any]): Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger("SBARDS.PhaseCoordinator")
        
        # Phase instances
        self.prescanning_orchestrator = None
        self.monitoring_manager = None
        
        # Shared state
        self.shared_state = {
            "scan_results": {},
            "alerts": [],
            "detected_threats": [],
            "monitored_processes": set(),
            "monitored_files": set(),
            "monitored_connections": set()
        }
        
        # Coordination thread
        self.coordination_thread = None
        self.stop_event = threading.Event()
        self.is_running = False
        
        # Coordination interval
        self.coordination_interval = config.get("integration", {}).get(
            "coordination_interval_seconds", 5.0
        )
        
        self.logger.info("Phase Coordinator initialized")
        
    def set_prescanning_orchestrator(self, orchestrator) -> None:
        """
        Set the Pre-Scanning orchestrator.
        
        Args:
            orchestrator: Pre-Scanning orchestrator instance
        """
        self.prescanning_orchestrator = orchestrator
        self.logger.info("Pre-Scanning orchestrator set")
        
    def set_monitoring_manager(self, manager) -> None:
        """
        Set the Monitoring manager.
        
        Args:
            manager: Monitoring manager instance
        """
        self.monitoring_manager = manager
        self.logger.info("Monitoring manager set")
        
    def start_coordination(self) -> bool:
        """
        Start coordination between phases.
        
        Returns:
            bool: True if successful, False otherwise
        """
        if self.is_running:
            self.logger.warning("Coordination is already running")
            return True
            
        if not self.prescanning_orchestrator and not self.monitoring_manager:
            self.logger.error("No phases to coordinate")
            return False
            
        self.logger.info("Starting phase coordination")
        self.stop_event.clear()
        
        # Start coordination thread
        self.coordination_thread = threading.Thread(
            target=self._coordination_loop,
            daemon=True
        )
        self.coordination_thread.start()
        
        self.is_running = True
        return True
        
    def stop_coordination(self) -> bool:
        """
        Stop coordination between phases.
        
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.is_running:
            return True
            
        self.logger.info("Stopping phase coordination")
        self.stop_event.set()
        
        if self.coordination_thread:
            self.coordination_thread.join(timeout=10.0)
            
        self.is_running = False
        return True
        
    def _coordination_loop(self) -> None:
        """Coordination loop between phases."""
        while not self.stop_event.is_set():
            try:
                # Share information between phases
                self._share_information()
                
                # Wait for next coordination cycle
                self.stop_event.wait(self.coordination_interval)
                
            except Exception as e:
                self.logger.error(f"Error during phase coordination: {e}")
                # Wait a bit before retrying
                self.stop_event.wait(1.0)
                
        self.logger.info("Phase coordination stopped")
        
    def _share_information(self) -> None:
        """Share information between phases."""
        # Share Pre-Scanning results with Monitoring phase
        if self.prescanning_orchestrator:
            # Get scan results
            scan_results = getattr(self.prescanning_orchestrator, "scan_results", {})
            if scan_results:
                self.shared_state["scan_results"] = scan_results
                
                # Extract detected threats
                detected_threats = []
                for file_path, matches in scan_results.items():
                    if matches:
                        for match in matches:
                            detected_threats.append({
                                "file_path": file_path,
                                "rule": match.get("rule", "unknown"),
                                "category": match.get("meta", {}).get("category", "unknown"),
                                "severity": match.get("meta", {}).get("severity", "unknown")
                            })
                
                self.shared_state["detected_threats"] = detected_threats
                
                # If monitoring is active, inform it about detected threats
                if self.monitoring_manager and hasattr(self.monitoring_manager, "add_threats"):
                    self.monitoring_manager.add_threats(detected_threats)
        
        # Share Monitoring alerts with Pre-Scanning phase
        if self.monitoring_manager:
            # Get alerts
            alerts = getattr(self.monitoring_manager, "get_alerts", lambda: [])()
            if alerts:
                self.shared_state["alerts"] = alerts
                
                # If pre-scanning is active, inform it about alerts
                if self.prescanning_orchestrator and hasattr(self.prescanning_orchestrator, "add_alerts"):
                    self.prescanning_orchestrator.add_alerts(alerts)
    
    def get_shared_state(self) -> Dict[str, Any]:
        """
        Get the shared state between phases.
        
        Returns:
            Dict[str, Any]: Shared state dictionary
        """
        return self.shared_state
