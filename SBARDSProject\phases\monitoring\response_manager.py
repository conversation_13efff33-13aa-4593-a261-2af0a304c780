"""
Response Manager for SBARDS

This module provides automated response for the monitoring phase of the SBARDS project.
"""

import os
import time
import logging
import threading
import platform
import shutil
from typing import Dict, List, Any, Optional, Set

class ResponseManager:
    """
    Response manager for the monitoring phase.
    
    This class provides mechanisms for:
    1. Automated response to alerts
    2. Quarantining suspicious files
    3. Blocking suspicious processes
    4. Blocking suspicious network connections
    """
    
    def __init__(self, config: Dict[str, Any], alert_manager):
        """
        Initialize response manager.
        
        Args:
            config (Dict[str, Any]): Response configuration
            alert_manager: Alert manager
        """
        self.config = config
        self.enabled = config.get("enabled", True)
        self.check_interval_seconds = config.get("check_interval_seconds", 5)
        self.auto_quarantine = config.get("auto_quarantine", False)
        self.auto_block_process = config.get("auto_block_process", False)
        self.auto_block_network = config.get("auto_block_network", False)
        self.quarantine_directory = config.get("quarantine_directory", "quarantine")
        self.response_rules = config.get("response_rules", [])
        
        # Set up logging
        self.logger = logging.getLogger("ResponseManager")
        
        # Set alert manager
        self.alert_manager = alert_manager
        
        # Initialize response storage
        self.responses = []
        self.response_count = 0
        
        # Initialize monitoring state
        self.is_running_flag = False
        self.stop_event = threading.Event()
        self.monitoring_thread = None
        
        # Create quarantine directory
        os.makedirs(self.quarantine_directory, exist_ok=True)
    
    def start(self) -> bool:
        """
        Start response manager.
        
        Returns:
            bool: True if started successfully, False otherwise
        """
        if not self.enabled:
            self.logger.warning("Response manager is not enabled in configuration")
            return False
        
        if self.is_running_flag:
            self.logger.warning("Response manager is already running")
            return True
        
        self.logger.info("Starting response manager")
        
        try:
            # Reset stop event
            self.stop_event.clear()
            
            # Start monitoring thread
            self.monitoring_thread = threading.Thread(
                target=self._monitoring_loop,
                daemon=True
            )
            self.monitoring_thread.start()
            
            self.is_running_flag = True
            self.logger.info("Response manager started")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error starting response manager: {e}")
            return False
    
    def stop(self) -> bool:
        """
        Stop response manager.
        
        Returns:
            bool: True if stopped successfully, False otherwise
        """
        if not self.is_running_flag:
            self.logger.warning("Response manager is not running")
            return True
        
        self.logger.info("Stopping response manager")
        
        try:
            # Set stop event
            self.stop_event.set()
            
            # Wait for monitoring thread to stop
            if self.monitoring_thread:
                self.monitoring_thread.join(timeout=5.0)
            
            self.is_running_flag = False
            self.logger.info("Response manager stopped")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error stopping response manager: {e}")
            return False
    
    def is_running(self) -> bool:
        """
        Check if response manager is running.
        
        Returns:
            bool: True if running, False otherwise
        """
        return self.is_running_flag
    
    def _monitoring_loop(self) -> None:
        """Response manager loop."""
        while not self.stop_event.is_set():
            try:
                # Get recent alerts
                recent_alerts = self.alert_manager.get_recent_alerts(20)
                
                # Process alerts
                for alert in recent_alerts:
                    self._process_alert(alert)
                
                # Wait for next check
                self.stop_event.wait(self.check_interval_seconds)
                
            except Exception as e:
                self.logger.error(f"Error in response manager loop: {e}")
                # Wait a bit before retrying
                self.stop_event.wait(1.0)
    
    def _process_alert(self, alert: Dict[str, Any]) -> None:
        """
        Process an alert.
        
        Args:
            alert (Dict[str, Any]): Alert data
        """
        # Check if alert has already been processed
        if self._is_alert_processed(alert):
            return
        
        # Apply response rules
        for rule in self.response_rules:
            self._apply_response_rule(rule, alert)
        
        # Apply built-in response rules
        self._process_file_alert(alert)
        self._process_process_alert(alert)
        self._process_network_alert(alert)
        self._process_correlated_alert(alert)
    
    def _is_alert_processed(self, alert: Dict[str, Any]) -> bool:
        """
        Check if alert has already been processed.
        
        Args:
            alert (Dict[str, Any]): Alert data
            
        Returns:
            bool: True if alert has been processed, False otherwise
        """
        alert_id = alert.get("id")
        
        if not alert_id:
            return False
        
        # Check if alert has already been processed
        for response in self.responses:
            if response.get("alert_id") == alert_id:
                return True
        
        return False
    
    def _apply_response_rule(self, rule: Dict[str, Any], alert: Dict[str, Any]) -> None:
        """
        Apply a response rule.
        
        Args:
            rule (Dict[str, Any]): Response rule
            alert (Dict[str, Any]): Alert data
        """
        # This would implement custom response rules
        pass
    
    def _process_file_alert(self, alert: Dict[str, Any]) -> None:
        """
        Process a file alert.
        
        Args:
            alert (Dict[str, Any]): Alert data
        """
        # Check if alert is from filesystem monitor
        if alert.get("source") != "filesystem_monitor":
            return
        
        # Check if alert is about a suspicious file
        if "Suspicious file" not in alert.get("message", ""):
            return
        
        # Get file path
        file_path = alert.get("details", {}).get("path")
        
        if not file_path or not os.path.exists(file_path):
            return
        
        # Check if auto quarantine is enabled
        if self.auto_quarantine:
            # Quarantine file
            self._quarantine_file(file_path, alert)
    
    def _process_process_alert(self, alert: Dict[str, Any]) -> None:
        """
        Process a process alert.
        
        Args:
            alert (Dict[str, Any]): Alert data
        """
        # Check if alert is from process monitor
        if alert.get("source") != "process_monitor":
            return
        
        # Check if alert is about a suspicious process
        if "Suspicious process" not in alert.get("message", ""):
            return
        
        # Get process ID
        pid = alert.get("details", {}).get("pid")
        
        if not pid:
            return
        
        # Check if auto block process is enabled
        if self.auto_block_process:
            # Block process
            self._block_process(pid, alert)
    
    def _process_network_alert(self, alert: Dict[str, Any]) -> None:
        """
        Process a network alert.
        
        Args:
            alert (Dict[str, Any]): Alert data
        """
        # Check if alert is from network monitor
        if alert.get("source") != "network_monitor":
            return
        
        # Check if alert is about a suspicious connection
        if "Connection to suspicious" not in alert.get("message", ""):
            return
        
        # Get connection details
        details = alert.get("details", {})
        local_address = details.get("local_address")
        local_port = details.get("local_port")
        remote_address = details.get("remote_address")
        remote_port = details.get("remote_port")
        
        if not local_address or not local_port or not remote_address or not remote_port:
            return
        
        # Check if auto block network is enabled
        if self.auto_block_network:
            # Block network connection
            self._block_network_connection(local_address, local_port, remote_address, remote_port, alert)
    
    def _process_correlated_alert(self, alert: Dict[str, Any]) -> None:
        """
        Process a correlated alert.
        
        Args:
            alert (Dict[str, Any]): Alert data
        """
        # Check if alert is from event correlator
        if alert.get("source") != "event_correlator":
            return
        
        # Check if alert is critical
        if alert.get("level") != "critical":
            return
        
        # Create response
        response = {
            "id": str(len(self.responses) + 1),
            "timestamp": time.time(),
            "alert_id": alert.get("id"),
            "type": "correlated",
            "action": "notify",
            "details": {
                "message": alert.get("message"),
                "level": alert.get("level"),
                "source": alert.get("source")
            }
        }
        
        # Add to responses
        self.responses.append(response)
        self.response_count += 1
        
        # Log response
        self.logger.warning(f"Correlated alert response: {alert.get('message')}")
    
    def _quarantine_file(self, file_path: str, alert: Dict[str, Any]) -> None:
        """
        Quarantine a file.
        
        Args:
            file_path (str): File path
            alert (Dict[str, Any]): Alert data
        """
        try:
            # Create quarantine path
            file_name = os.path.basename(file_path)
            quarantine_path = os.path.join(self.quarantine_directory, f"{file_name}.{int(time.time())}")
            
            # Move file to quarantine
            shutil.move(file_path, quarantine_path)
            
            # Create response
            response = {
                "id": str(len(self.responses) + 1),
                "timestamp": time.time(),
                "alert_id": alert.get("id"),
                "type": "file",
                "action": "quarantine",
                "details": {
                    "file_path": file_path,
                    "quarantine_path": quarantine_path
                }
            }
            
            # Add to responses
            self.responses.append(response)
            self.response_count += 1
            
            # Log response
            self.logger.warning(f"Quarantined file: {file_path} -> {quarantine_path}")
            
            # Create alert
            self.alert_manager.add_alert(
                level="info",
                source="response_manager",
                message=f"File quarantined: {file_path}",
                details={
                    "file_path": file_path,
                    "quarantine_path": quarantine_path,
                    "response_id": response["id"]
                }
            )
            
        except Exception as e:
            self.logger.error(f"Error quarantining file {file_path}: {e}")
    
    def _block_process(self, pid: int, alert: Dict[str, Any]) -> None:
        """
        Block a process.
        
        Args:
            pid (int): Process ID
            alert (Dict[str, Any]): Alert data
        """
        try:
            import psutil
            
            # Get process
            process = psutil.Process(pid)
            
            # Terminate process
            process.terminate()
            
            # Create response
            response = {
                "id": str(len(self.responses) + 1),
                "timestamp": time.time(),
                "alert_id": alert.get("id"),
                "type": "process",
                "action": "terminate",
                "details": {
                    "pid": pid,
                    "name": process.name()
                }
            }
            
            # Add to responses
            self.responses.append(response)
            self.response_count += 1
            
            # Log response
            self.logger.warning(f"Terminated process: {pid} ({process.name()})")
            
            # Create alert
            self.alert_manager.add_alert(
                level="info",
                source="response_manager",
                message=f"Process terminated: {pid} ({process.name()})",
                details={
                    "pid": pid,
                    "name": process.name(),
                    "response_id": response["id"]
                }
            )
            
        except Exception as e:
            self.logger.error(f"Error terminating process {pid}: {e}")
    
    def _block_network_connection(self, local_address: str, local_port: int, remote_address: str, remote_port: int, alert: Dict[str, Any]) -> None:
        """
        Block a network connection.
        
        Args:
            local_address (str): Local address
            local_port (int): Local port
            remote_address (str): Remote address
            remote_port (int): Remote port
            alert (Dict[str, Any]): Alert data
        """
        # This would implement network connection blocking
        # For example, using Windows Firewall or iptables
        
        # Create response
        response = {
            "id": str(len(self.responses) + 1),
            "timestamp": time.time(),
            "alert_id": alert.get("id"),
            "type": "network",
            "action": "block",
            "details": {
                "local_address": local_address,
                "local_port": local_port,
                "remote_address": remote_address,
                "remote_port": remote_port
            }
        }
        
        # Add to responses
        self.responses.append(response)
        self.response_count += 1
        
        # Log response
        self.logger.warning(f"Blocked network connection: {local_address}:{local_port} -> {remote_address}:{remote_port}")
        
        # Create alert
        self.alert_manager.add_alert(
            level="info",
            source="response_manager",
            message=f"Network connection blocked: {local_address}:{local_port} -> {remote_address}:{remote_port}",
            details={
                "local_address": local_address,
                "local_port": local_port,
                "remote_address": remote_address,
                "remote_port": remote_port,
                "response_id": response["id"]
            }
        )
    
    def get_response_count(self) -> int:
        """
        Get response count.
        
        Returns:
            int: Response count
        """
        return self.response_count
    
    def get_responses(self, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Get responses.
        
        Args:
            limit (int, optional): Maximum number of responses to return
            
        Returns:
            List[Dict[str, Any]]: Responses
        """
        # Sort responses by timestamp (newest first)
        sorted_responses = sorted(self.responses, key=lambda r: r["timestamp"], reverse=True)
        
        # Return limited number of responses
        return sorted_responses[:limit]
