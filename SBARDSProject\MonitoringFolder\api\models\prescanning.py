"""
Pre-Scanning API Models for SBARDS

This module provides API models for the Pre-Scanning phase of the SBARDS project.
"""

from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional

class ScanRequest(BaseModel):
    """
    Scan request model.
    """
    target_directory: str = Field(..., description="Target directory to scan")
    recursive: bool = Field(True, description="Scan recursively")
    max_depth: int = Field(5, description="Maximum recursion depth")
    rule_files: List[str] = Field(["rules/custom_rules.yar"], description="YARA rule files")

class ScanResult(BaseModel):
    """
    Scan result model.
    """
    scan_id: str = Field(..., description="Scan ID")
    status: str = Field(..., description="Scan status")
    message: str = Field(..., description="Scan message")

class ScanStatus(BaseModel):
    """
    Scan status model.
    """
    scan_id: str = Field(..., description="Scan ID")
    status: str = Field(..., description="Scan status")
    results: Dict[str, Any] = Field({}, description="Scan results")
