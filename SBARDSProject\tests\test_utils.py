"""
Tests for the utilities module.
"""

import os
import json
import tempfile
import unittest
from core.utils import get_file_hash, get_file_info, save_json, load_json, format_time

class TestUtils(unittest.TestCase):
    """Tests for the utilities module."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary directory
        self.temp_dir = tempfile.TemporaryDirectory()
        
        # Create a temporary file
        self.test_file_path = os.path.join(self.temp_dir.name, "test_file.txt")
        with open(self.test_file_path, "w") as f:
            f.write("Test content")
        
        # Create a temporary JSON file
        self.test_json_path = os.path.join(self.temp_dir.name, "test_file.json")
        self.test_data = {"key": "value", "number": 42, "list": [1, 2, 3]}
        with open(self.test_json_path, "w") as f:
            json.dump(self.test_data, f)
    
    def tearDown(self):
        """Tear down test fixtures."""
        # Remove the temporary directory
        self.temp_dir.cleanup()
    
    def test_get_file_hash(self):
        """Test getting a file hash."""
        # Get file hash
        file_hash = get_file_hash(self.test_file_path)
        
        # Check hash
        self.assertIsNotNone(file_hash)
        self.assertEqual(len(file_hash), 64)  # SHA-256 hash length
    
    def test_get_file_info(self):
        """Test getting file information."""
        # Get file info
        file_info = get_file_info(self.test_file_path)
        
        # Check info
        self.assertEqual(file_info["path"], self.test_file_path)
        self.assertEqual(file_info["size"], 12)  # "Test content" is 12 bytes
        self.assertEqual(file_info["is_file"], True)
        self.assertEqual(file_info["is_dir"], False)
        self.assertEqual(file_info["extension"], ".txt")
        self.assertIn("sha256", file_info)
    
    def test_save_and_load_json(self):
        """Test saving and loading JSON."""
        # Create a new JSON file path
        new_json_path = os.path.join(self.temp_dir.name, "new_file.json")
        
        # Save JSON
        save_result = save_json(self.test_data, new_json_path)
        
        # Check result
        self.assertTrue(save_result)
        self.assertTrue(os.path.exists(new_json_path))
        
        # Load JSON
        loaded_data = load_json(new_json_path)
        
        # Check data
        self.assertEqual(loaded_data, self.test_data)
    
    def test_format_time(self):
        """Test formatting time."""
        # Format time in seconds
        seconds_str = format_time(30)
        self.assertEqual(seconds_str, "30.00 seconds")
        
        # Format time in minutes
        minutes_str = format_time(150)
        self.assertEqual(minutes_str, "2.50 minutes")
        
        # Format time in hours
        hours_str = format_time(7200)
        self.assertEqual(hours_str, "2.00 hours")

if __name__ == "__main__":
    unittest.main()
