2025-05-16 20:40:57,069 - SBARDS.Backend.Server - INFO - Starting SBARDS Backend Server on 0.0.0.0:8000
2025-05-16 20:40:57,799 - SBARDS.Backend.Server - ERROR - Error starting server: `BaseSettings` has been moved to the `pydantic-settings` package. See https://docs.pydantic.dev/2.8/migration/#basesettings-has-moved-to-pydantic-settings for more details.

For further information visit https://errors.pydantic.dev/2.8/u/import-error
2025-05-16 20:42:31,341 - SBARDS.Backend.Server - INFO - Starting SBARDS Backend Server on 0.0.0.0:8000
2025-05-16 20:42:32,774 - fastapi - ERROR - Form data requires "python-multipart" to be installed. 
You can install "python-multipart" with: 

pip install python-multipart

2025-05-16 20:42:32,789 - SBARDS.Backend.Server - ERROR - Error starting server: Form data requires "python-multipart" to be installed. 
You can install "python-multipart" with: 

pip install python-multipart

2025-05-16 20:43:09,358 - SBARDS.Backend.Server - INFO - Starting SBARDS Backend Server on 0.0.0.0:8000
2025-05-16 20:43:10,389 - SBARDS.Backend.Server - ERROR - Error starting server: cannot import name 'verify_api_key' from 'app.core.security' (C:\Users\<USER>\Desktop\SBARDS\SBARDSProject\backend\app\core\security.py)
2025-05-16 20:45:06,910 - SBARDS.Backend.Server - INFO - Starting SBARDS Backend Server on 0.0.0.0:8000
2025-05-16 20:45:08,121 - SBARDS.Backend - INFO - Logging configured with level: INFO
2025-05-16 20:47:52,159 - SBARDS.Backend - ERROR - HTTP error: 404 - Not Found
2025-05-16 20:49:28,860 - SBARDS.Backend.Server - INFO - Starting SBARDS Backend Server on 0.0.0.0:8000
2025-05-16 20:49:29,891 - SBARDS.Backend - INFO - Logging configured with level: INFO
2025-05-16 20:49:49,867 - SBARDS.Backend - ERROR - HTTP error: 404 - Not Found
2025-05-16 20:51:48,926 - SBARDS.Backend.Server - INFO - Starting SBARDS Backend Server on 0.0.0.0:8000
2025-05-16 20:51:49,993 - SBARDS.Backend - INFO - Logging configured with level: INFO
2025-05-16 21:04:36,986 - SBARDS.Backend - ERROR - HTTP error: 401 - Could not validate credentials
2025-05-16 21:05:38,088 - SBARDS.Backend - WARNING - No API key configured, allowing all requests
2025-05-16 21:05:38,088 - SBARDS.Backend - WARNING - No API key configured, allowing all requests
2025-05-16 21:05:38,088 - SBARDS.Backend - ERROR - HTTP error: 400 - Path not found: C:/Users/<USER>/Desktop/SBARDS/samples
2025-05-16 21:06:32,895 - SBARDS.Backend - WARNING - No API key configured, allowing all requests
2025-05-16 21:06:32,895 - SBARDS.Backend - WARNING - No API key configured, allowing all requests
2025-05-16 21:06:32,914 - SBARDS.Backend - INFO - Starting scan of C:/Users/<USER>/Desktop/SBARDS/SBARDSProject/samples with ID scan_3dda2294172f467d8ad187be135bb08a
2025-05-16 21:06:32,914 - SBARDS.Backend - INFO - Running scan on C:/Users/<USER>/Desktop/SBARDS/SBARDSProject/samples with ID scan_3dda2294172f467d8ad187be135bb08a
2025-05-16 21:06:33,645 - SBARDS.Backend - WARNING - No log file found for scan scan_3dda2294172f467d8ad187be135bb08a
2025-05-16 21:06:33,659 - SBARDS.Backend - INFO - Scan scan_3dda2294172f467d8ad187be135bb08a completed with result: {'scan_id': 'scan_3dda2294172f467d8ad187be135bb08a', 'status': 'completed', 'files_scanned': 7, 'threats_found': 0, 'report_path': './uploads\\scan_3dda2294172f467d8ad187be135bb08a\\output\\reports\\scan_report_20250516_210633.html', 'message': 'Scan completed successfully'}
2025-05-16 21:06:44,893 - SBARDS.Backend - ERROR - Error getting scan report scan_3dda2294172f467d8ad187be135bb08a: (sqlite3.OperationalError) no such table: scan_reports
[SQL: SELECT scan_reports.id AS scan_reports_id, scan_reports.scan_id AS scan_reports_scan_id, scan_reports.timestamp AS scan_reports_timestamp, scan_reports.scan_path AS scan_reports_scan_path, scan_reports.files_scanned AS scan_reports_files_scanned, scan_reports.threats_found AS scan_reports_threats_found, scan_reports.report_path AS scan_reports_report_path, scan_reports.report_content AS scan_reports_report_content 
FROM scan_reports 
WHERE scan_reports.scan_id = ?
 LIMIT ? OFFSET ?]
[parameters: ('scan_3dda2294172f467d8ad187be135bb08a', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-16 21:06:44,893 - SBARDS.Backend - ERROR - Error getting scan status for scan_3dda2294172f467d8ad187be135bb08a: (sqlite3.OperationalError) no such table: scan_reports
[SQL: SELECT scan_reports.id AS scan_reports_id, scan_reports.scan_id AS scan_reports_scan_id, scan_reports.timestamp AS scan_reports_timestamp, scan_reports.scan_path AS scan_reports_scan_path, scan_reports.files_scanned AS scan_reports_files_scanned, scan_reports.threats_found AS scan_reports_threats_found, scan_reports.report_path AS scan_reports_report_path, scan_reports.report_content AS scan_reports_report_content 
FROM scan_reports 
WHERE scan_reports.scan_id = ?
 LIMIT ? OFFSET ?]
[parameters: ('scan_3dda2294172f467d8ad187be135bb08a', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-16 21:06:44,893 - SBARDS.Backend - ERROR - HTTP error: 500 - Error getting scan status: (sqlite3.OperationalError) no such table: scan_reports
[SQL: SELECT scan_reports.id AS scan_reports_id, scan_reports.scan_id AS scan_reports_scan_id, scan_reports.timestamp AS scan_reports_timestamp, scan_reports.scan_path AS scan_reports_scan_path, scan_reports.files_scanned AS scan_reports_files_scanned, scan_reports.threats_found AS scan_reports_threats_found, scan_reports.report_path AS scan_reports_report_path, scan_reports.report_content AS scan_reports_report_content 
FROM scan_reports 
WHERE scan_reports.scan_id = ?
 LIMIT ? OFFSET ?]
[parameters: ('scan_3dda2294172f467d8ad187be135bb08a', 1, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-16 21:14:24,447 - SBARDS.Backend.Server - INFO - Starting SBARDS Backend Server on 0.0.0.0:8000
2025-05-16 21:14:25,385 - SBARDS.Backend - INFO - Logging configured with level: INFO
2025-05-16 21:15:02,373 - SBARDS.Backend - ERROR - HTTP error: 404 - Not Found
2025-05-16 22:05:55,994 - SBARDS.Backend.Server - INFO - Starting SBARDS Backend Server on 0.0.0.0:8000
2025-05-16 22:05:57,109 - SBARDS.Backend - INFO - Logging configured with level: INFO
2025-05-17 21:49:51,441 - SBARDS.Backend.Server - INFO - Starting SBARDS Backend Server on 0.0.0.0:8000
2025-05-17 21:49:53,909 - SBARDS.Backend - INFO - Logging configured with level: INFO
2025-05-17 21:58:47,692 - SBARDS.Backend.Server - INFO - Starting SBARDS Backend Server on 0.0.0.0:8000
2025-05-17 21:58:48,829 - SBARDS.Backend - INFO - Logging configured with level: INFO
2025-05-17 22:06:02,301 - SBARDS.Backend.Server - INFO - Starting SBARDS Backend Server on 0.0.0.0:8000
2025-05-17 22:06:03,849 - SBARDS.Backend - INFO - Logging configured with level: INFO
