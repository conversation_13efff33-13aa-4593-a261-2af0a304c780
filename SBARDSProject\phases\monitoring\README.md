# SBARDS Project - Monitoring Phase

This document provides detailed information about the Monitoring Phase of the SBARDS Project, which is designed to detect and respond to suspicious activities in real-time.

## Overview

The Monitoring Phase is responsible for:

1. Real-time monitoring of system activities
2. Detection of suspicious behaviors
3. Alerting and response to potential threats
4. Integration with the Pre-Scanning Phase

## Components

### Windows Monitoring

The Windows monitoring components include:

#### OSQuery Monitor

Uses OSQuery to monitor processes, files, registry, and network activities on Windows systems.

- Process monitoring: Detects suspicious processes based on name patterns
- File monitoring: Watches for suspicious file operations and extensions
- Registry monitoring: Monitors registry changes for persistence mechanisms
- Network monitoring: Detects suspicious network connections

#### Sysmon Monitor

Leverages Microsoft Sysmon to monitor system events:

- Process creation and termination
- File creation and modification
- Network connections
- Registry modifications
- Driver loading

#### ETW Monitor

Uses Event Tracing for Windows (ETW) to capture system events:

- PowerShell script execution
- WMI activity
- Security events
- System events

### Alert Management

The Alert Manager is responsible for:

- Collecting alerts from all monitoring components
- Deduplicating and prioritizing alerts
- Logging alerts to file
- Triggering responses based on alert severity

### Response Management

The Response Manager can take automated actions based on alerts:

- Terminate suspicious processes
- Quarantine suspicious files
- Block suspicious network connections
- Send notifications

## Configuration

The monitoring phase is configured through the main `config.json` file, in the `monitoring` section:

```json
"monitoring": {
    "enabled": true,
    "process_monitoring": {
        "enabled": true,
        "suspicious_process_patterns": [
            "encrypt",
            "ransom",
            "crypt"
        ]
    },
    "filesystem_monitoring": {
        "enabled": true,
        "watch_directories": [
            "samples",
            "C:\\Users\\<USER>