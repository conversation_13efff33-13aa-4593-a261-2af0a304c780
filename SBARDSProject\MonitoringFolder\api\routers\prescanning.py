"""
Pre-Scanning API Router for SBARDS

This module provides API endpoints for the Pre-Scanning phase of the SBARDS project.
"""

import os
import logging
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from typing import Dict, List, Any, Optional

# Import models
from api.models.prescanning import ScanRequest, ScanResult, ScanStatus

# Create router
router = APIRouter()

# Global variables
prescanning_orchestrator = None

# Dependency to get the orchestrator
def get_orchestrator():
    """
    Get the Pre-Scanning orchestrator.
    
    Returns:
        Any: Pre-Scanning orchestrator
    """
    if prescanning_orchestrator is None:
        raise HTTPException(status_code=503, detail="Pre-Scanning orchestrator not initialized")
    return prescanning_orchestrator

# Set the orchestrator
def set_orchestrator(orchestrator):
    """
    Set the Pre-Scanning orchestrator.
    
    Args:
        orchestrator: Pre-Scanning orchestrator
    """
    global prescanning_orchestrator
    prescanning_orchestrator = orchestrator

# Endpoints
@router.post("/scan", response_model=ScanResult)
async def start_scan(
    scan_request: ScanRequest,
    background_tasks: BackgroundTasks,
    orchestrator = Depends(get_orchestrator)
):
    """
    Start a scan.
    
    Args:
        scan_request (ScanRequest): Scan request
        background_tasks (BackgroundTasks): Background tasks
        orchestrator: Pre-Scanning orchestrator
        
    Returns:
        ScanResult: Scan result
    """
    # Create scan ID
    scan_id = f"scan_{len(orchestrator.scan_results) + 1}"
    
    # Start scan in background
    background_tasks.add_task(orchestrator.run_scan, scan_request.target_directory)
    
    return {
        "scan_id": scan_id,
        "status": "running",
        "message": f"Scan started for {scan_request.target_directory}"
    }

@router.get("/scan/{scan_id}", response_model=ScanStatus)
async def get_scan_status(
    scan_id: str,
    orchestrator = Depends(get_orchestrator)
):
    """
    Get scan status.
    
    Args:
        scan_id (str): Scan ID
        orchestrator: Pre-Scanning orchestrator
        
    Returns:
        ScanStatus: Scan status
    """
    # Check if scan exists
    if scan_id not in orchestrator.scan_results:
        raise HTTPException(status_code=404, detail=f"Scan {scan_id} not found")
    
    return {
        "scan_id": scan_id,
        "status": "completed" if orchestrator.scan_results[scan_id] else "running",
        "results": orchestrator.scan_results[scan_id]
    }

@router.get("/results", response_model=Dict[str, Any])
async def get_scan_results(
    orchestrator = Depends(get_orchestrator)
):
    """
    Get all scan results.
    
    Args:
        orchestrator: Pre-Scanning orchestrator
        
    Returns:
        Dict[str, Any]: Scan results
    """
    return orchestrator.scan_results
