# SBARDS Pre-scanning Phase: Technical Implementation Guide

## Table of Contents
1. [Development Environment Setup](#development-environment-setup)
2. [Implementation Details](#implementation-details)
3. [Code Examples](#code-examples)
4. [Testing Procedures](#testing-procedures)
5. [Performance Optimization](#performance-optimization)
6. [Unicode Support Implementation](#unicode-support-implementation)
7. [Memory Management](#memory-management)
8. [Reporting System](#reporting-system)

## Development Environment Setup

### Prerequisites

- Python 3.8+ (3.10+ recommended)
- C++ compiler (MSVC on Windows, GCC on Linux)
- YARA 4.1.0+
- Required Python packages:
  - `yara-python`
  - `psutil`
  - `pandas`
  - `jinja2`
  - `chardet`

### Setup Instructions

1. **Clone the repository**:
   ```bash
   git clone https://github.com/your-org/sbards-project.git
   cd sbards-project
   ```

2. **Create a virtual environment**:
   ```bash
   # Windows
   python -m venv venv
   venv\Scripts\activate

   # Linux
   python3 -m venv venv
   source venv/bin/activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Install YARA**:
   - Windows: Download and install from [VirusTotal/yara](https://github.com/VirusTotal/yara/releases)
   - Linux: `sudo apt-get install yara` or build from source

5. **Configure the project**:
   - Copy `config.json.example` to `config.json`
   - Update the configuration with your settings

## Implementation Details

### Core Components Implementation

#### 1. Orchestrator

The orchestrator (`orchestrator.py`) is implemented as a class with the following structure:

```python
class Orchestrator:
    def __init__(self, config_path):
        # Initialize configuration, logger, and scanners
        self.config_loader = ConfigLoader(config_path)
        self.config = self.config_loader.get_config()
        self.logger = Logger(self.config["logging"]["log_directory"], 
                            self.config["logging"]["log_level"]).get_logger()
        self.threads = self.config["performance"]["threads"]
        self.batch_size = self.config["performance"]["batch_size"]
        
    def run_scan(self):
        # Main entry point for scanning
        self.logger.info("Starting scan process")
        files = self._discover_files()
        scanner = self._initialize_scanner()
        results = self._parallel_scan(scanner, files)
        self._generate_reports(results)
        return results
        
    def _discover_files(self):
        # Discover files to scan
        file_scanner = FileScanner(self.config, self.logger)
        return file_scanner.scan()
        
    def _initialize_scanner(self):
        # Initialize the YARA scanner
        scanner = YaraScanner(self.config, self.logger)
        return scanner
        
    def _parallel_scan(self, scanner, files):
        # Scan files in parallel with memory management
        # Implementation details in the Memory Management section
        
    def _generate_reports(self, scan_results):
        # Generate reports in various formats
        # Implementation details in the Reporting System section
```

#### 2. YARA Scanner

The YARA scanner (`scanner.py`) is implemented as a class with the following structure:

```python
class YaraScanner:
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
        self.rules = None
        self.compile_rules()
        
    def compile_rules(self):
        # Compile YARA rules from rule files
        try:
            rule_files = self.config["rules"]["rule_files"]
            filepaths = {f"namespace_{i}": os.path.abspath(rule_file) 
                        for i, rule_file in enumerate(rule_files)}
            self.rules = yara.compile(filepaths=filepaths, 
                                     externals=self.config["rules"].get("externals", {}))
            self.logger.info(f"Compiled YARA rules from {len(rule_files)} files")
        except Exception as e:
            self.logger.error(f"YARA compilation error: {e}")
            
    def scan_file(self, file_path):
        # Scan a single file with YARA rules
        try:
            if not os.path.exists(file_path):
                self.logger.warning(f"File not found: {file_path}")
                return []
                
            # Check file size
            file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
            if file_size_mb > self.config["scanner"]["max_file_size_mb"]:
                self.logger.info(f"Skipping large file: {file_path} ({file_size_mb:.2f} MB)")
                return []
                
            # Scan the file
            matches = self.rules.match(file_path, timeout=self.config["rules"]["timeout"])
            return self._format_matches(matches)
        except Exception as e:
            self.logger.error(f"Error scanning {file_path}: {e}")
            return []
            
    def _format_matches(self, matches):
        # Format YARA matches into a standardized format
        formatted_matches = []
        for match in matches:
            formatted_match = {
                "rule": match.rule,
                "namespace": match.namespace,
                "tags": match.tags,
                "meta": match.meta,
                "strings": [{"name": s[1], "offset": s[0], "data": s[2]} 
                           for s in match.strings]
            }
            formatted_matches.append(formatted_match)
        return formatted_matches
```

#### 3. File Scanner

The file scanner is responsible for discovering files to scan:

```python
class FileScanner:
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
        self.target_dir = config["scanner"]["target_directory"]
        self.recursive = config["scanner"]["recursive"]
        self.max_depth = config["scanner"]["max_depth"]
        self.excluded_extensions = config["scanner"]["excluded_extensions"]
        self.excluded_directories = config["scanner"]["excluded_directories"]
        
    def scan(self):
        # Discover files to scan
        self.logger.info(f"Starting file scan in {self.target_dir}")
        self.logger.info(f"Recursive: {self.recursive}, Max depth: {self.max_depth}")
        
        files = []
        if not os.path.exists(self.target_dir):
            self.logger.error(f"Target directory does not exist: {self.target_dir}")
            return files
            
        for root, dirs, filenames in os.walk(self.target_dir):
            # Check depth
            relative_path = os.path.relpath(root, self.target_dir)
            depth = len(relative_path.split(os.sep)) if relative_path != '.' else 0
            if depth > self.max_depth:
                continue
                
            # Filter directories
            dirs[:] = [d for d in dirs if d not in self.excluded_directories]
            
            # Add files
            for filename in filenames:
                file_path = os.path.join(root, filename)
                _, ext = os.path.splitext(filename.lower())
                if ext not in self.excluded_extensions:
                    files.append(file_path)
                    
            # Stop if not recursive
            if not self.recursive:
                break
                
        self.logger.info(f"Found {len(files)} files to scan")
        return files
```

## Code Examples

### Unicode Support Implementation

```python
def run_cpp_scanner(rules_file, target_file):
    """Run the C++ scanner with enhanced Unicode support"""
    # Create a temporary file with the target file path
    temp_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp_path.txt")
    with open(temp_path, "w", encoding="utf-8") as f:
        f.write(target_file)

    # Create a temporary output file
    temp_output = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp_output.json")
    
    # Set environment variables for proper Unicode handling
    env_vars = dict(os.environ)
    env_vars["PYTHONIOENCODING"] = "utf-8"
    
    if os.name == 'nt':  # Windows
        # Force UTF-8 mode on Windows
        env_vars["PYTHONUTF8"] = "1"
        # Set console code page to UTF-8
        os.system("chcp 65001 > nul")
    
    try:
        # Use --output-file parameter to write results to a file
        cmd = [
            "python", 
            "scanner_core/cpp/mock_scanner.py", 
            rules_file, 
            "--path-file", temp_path,
            "--output-file", temp_output
        ]
        
        # Run the process without capturing output
        subprocess.run(cmd, capture_output=False, check=False, env=env_vars)
        
        # Read results from the output file
        matches = []
        if os.path.exists(temp_output):
            with open(temp_output, "r", encoding="utf-8") as f:
                output_data = json.load(f)
                if "matches" in output_data:
                    for match in output_data["matches"]:
                        if "rule" in match:
                            matches.append(match["rule"])
        
        # Clean up temporary files
        for temp_file in [temp_path, temp_output]:
            if os.path.exists(temp_file):
                os.remove(temp_file)
                
        return matches
    except Exception as e:
        print(f"Error running scanner: {e}")
        
        # Clean up temporary files
        for temp_file in [temp_path, temp_output]:
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass
                
        return []
```

### Memory Management Implementation

```python
def _parallel_scan(self, scanner, files):
    """Scan files in parallel with advanced memory management"""
    import psutil
    import gc
    
    results = {}
    total_files = len(files)
    processed_files = 0
    
    # Get performance configuration
    threads = self.threads
    batch_size = self.batch_size
    adaptive_threading = self.config["performance"].get("adaptive_threading", False)
    max_memory_mb = self.config["performance"].get("max_memory_mb", 1024)
    
    # Memory management settings
    memory_check_interval = 10  # Check memory every N files
    gc_threshold = 85  # Run garbage collection when memory usage exceeds this percentage
    critical_memory_threshold = 95  # Emergency measures when memory exceeds this percentage
    
    # Sort files by size and priority
    file_info = []
    for file_path in files:
        try:
            size = os.path.getsize(file_path)
            _, ext = os.path.splitext(file_path.lower())
            priority = 1 if ext in self.config["performance"].get("priority_extensions", []) else 0
            file_info.append((file_path, size, priority))
        except Exception as e:
            self.logger.warning(f"Error getting file size for {file_path}: {e}")
            file_info.append((file_path, 0, 0))
    
    # Sort files: first by priority (high to low), then by size (small to large)
    sorted_files = [f[0] for f in sorted(file_info, key=lambda x: (-x[2], x[1]))]
    
    # Process files in batches
    for i in range(0, total_files, batch_size):
        batch = sorted_files[i:i+batch_size]
        batch_results = {}
        
        # Check memory before starting a new batch
        memory_info = psutil.virtual_memory()
        memory_percent = memory_info.percent
        
        # Determine if we need emergency memory measures
        if memory_percent > critical_memory_threshold:
            self.logger.critical(f"CRITICAL MEMORY USAGE: {memory_percent:.1f}%. Taking emergency measures.")
            gc.collect()
            batch = batch[:max(1, len(batch) // 4)]
            adjusted_threads = 1
        elif adaptive_threading:
            if memory_percent > 90:
                adjusted_threads = max(1, threads // 4)
            elif memory_percent > 75:
                adjusted_threads = max(2, threads // 2)
            else:
                adjusted_threads = threads
        else:
            adjusted_threads = threads
        
        # Process the batch with controlled parallelism
        with concurrent.futures.ThreadPoolExecutor(max_workers=adjusted_threads) as executor:
            future_to_file = {}
            active_futures = set()
            max_concurrent = adjusted_threads * 2
            
            j = 0
            while j < len(batch) or active_futures:
                # Submit new tasks if we have capacity
                while j < len(batch) and len(active_futures) < max_concurrent:
                    file_path = batch[j]
                    future = executor.submit(scanner.scan_file, file_path)
                    future_to_file[future] = file_path
                    active_futures.add(future)
                    j += 1
                
                # Process completed futures
                done_futures = set()
                for future in list(active_futures):
                    if future.done():
                        file_path = future_to_file[future]
                        processed_files += 1
                        
                        try:
                            matches = future.result()
                            batch_results[file_path] = matches
                            
                            # Check memory periodically
                            if processed_files % memory_check_interval == 0:
                                current_memory = psutil.virtual_memory().percent
                                if current_memory > gc_threshold:
                                    self.logger.warning(f"Memory usage at {current_memory:.1f}%. Running garbage collection.")
                                    gc.collect()
                        except Exception as e:
                            self.logger.error(f"Error scanning {file_path}: {e}")
                        
                        done_futures.add(future)
                
                # Remove processed futures
                active_futures -= done_futures
                
                # If we still have active futures, wait a bit
                if active_futures:
                    time.sleep(0.1)
        
        # Update results with batch results
        results.update(batch_results)
        
        # Memory management between batches
        if i + batch_size < total_files:
            gc.collect()
            memory_percent = psutil.virtual_memory().percent
            if memory_percent > 85:
                pause_seconds = min(30, int(memory_percent - 80) * 2)
                self.logger.warning(f"High memory usage ({memory_percent:.1f}%). Pausing for {pause_seconds} seconds")
                time.sleep(pause_seconds)
    
    return results
```

## Testing Procedures

### Unit Testing

Create unit tests for each component using the `unittest` framework:

```python
import unittest
from scanner_core.python.scanner import YaraScanner
from scanner_core.utils.config_loader import ConfigLoader

class TestYaraScanner(unittest.TestCase):
    def setUp(self):
        self.config = {
            "rules": {
                "rule_files": ["tests/test_rules.yar"],
                "timeout": 60
            },
            "scanner": {
                "max_file_size_mb": 100
            }
        }
        self.logger = logging.getLogger("test")
        self.scanner = YaraScanner(self.config, self.logger)
        
    def test_compile_rules(self):
        self.assertIsNotNone(self.scanner.rules)
        
    def test_scan_file(self):
        results = self.scanner.scan_file("tests/test_files/malicious.txt")
        self.assertTrue(len(results) > 0)
        
    def test_scan_nonexistent_file(self):
        results = self.scanner.scan_file("nonexistent.txt")
        self.assertEqual(len(results), 0)
        
    def test_scan_large_file(self):
        # Create a temporary large file
        with tempfile.NamedTemporaryFile(delete=False) as f:
            f.write(b"X" * (101 * 1024 * 1024))  # 101 MB
            large_file = f.name
            
        try:
            results = self.scanner.scan_file(large_file)
            self.assertEqual(len(results), 0)  # Should skip the file
        finally:
            os.unlink(large_file)
```

### Integration Testing

Test the entire pre-scanning phase:

```python
def test_prescanning_phase():
    # Create a test configuration
    config = {
        "scanner": {
            "target_directory": "tests/test_files",
            "recursive": True,
            "max_depth": 5,
            "max_file_size_mb": 100,
            "excluded_extensions": [],
            "excluded_directories": []
        },
        "rules": {
            "rule_files": ["tests/test_rules.yar"],
            "timeout": 60
        },
        "performance": {
            "threads": 2,
            "batch_size": 5,
            "adaptive_threading": False
        },
        "reporting": {
            "output_directory": "tests/output",
            "generate_json": True,
            "generate_csv": True,
            "generate_html": True
        },
        "logging": {
            "log_directory": "tests/logs",
            "log_level": "info"
        }
    }
    
    # Save the configuration to a temporary file
    with open("tests/test_config.json", "w") as f:
        json.dump(config, f)
    
    # Run the pre-scanning phase
    orchestrator = Orchestrator("tests/test_config.json")
    results = orchestrator.run_scan()
    
    # Verify results
    assert len(results) > 0
    
    # Check that reports were generated
    assert os.path.exists("tests/output/scan_results.json")
    assert os.path.exists("tests/output/scan_results.csv")
    assert os.path.exists("tests/output/scan_report.html")
```

## Performance Optimization

### Batch Processing

Process files in batches to control memory usage and improve performance:

```python
def process_in_batches(files, batch_size):
    """Process files in batches"""
    for i in range(0, len(files), batch_size):
        batch = files[i:i+batch_size]
        yield batch
```

### Adaptive Threading

Adjust thread count based on system load:

```python
def get_optimal_thread_count(base_threads):
    """Get optimal thread count based on system load"""
    import psutil
    
    # Get CPU and memory usage
    cpu_percent = psutil.cpu_percent()
    memory_percent = psutil.virtual_memory().percent
    
    # Adjust thread count based on system load
    if cpu_percent > 90 or memory_percent > 90:
        return max(1, base_threads // 4)
    elif cpu_percent > 75 or memory_percent > 75:
        return max(2, base_threads // 2)
    else:
        return base_threads
```

### File Prioritization

Prioritize scanning of high-risk file types:

```python
def prioritize_files(files, priority_extensions):
    """Prioritize files based on extension"""
    priority_files = []
    normal_files = []
    
    for file_path in files:
        _, ext = os.path.splitext(file_path.lower())
        if ext in priority_extensions:
            priority_files.append(file_path)
        else:
            normal_files.append(file_path)
    
    return priority_files + normal_files
```
