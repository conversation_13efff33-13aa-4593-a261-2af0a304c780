"""
Alert Manager for SBARDS

This module provides alert management for the monitoring phase of the SBARDS project.
"""

import os
import time
import uuid
import logging
import threading
from typing import Dict, List, Any, Optional
from datetime import datetime

class AlertManager:
    """
    Alert manager for the monitoring phase.
    
    This class provides mechanisms for:
    1. Managing alerts
    2. Deduplicating alerts
    3. Prioritizing alerts
    4. Notifying users
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize alert manager.
        
        Args:
            config (Dict[str, Any]): Alert configuration
        """
        self.config = config
        self.enabled = config.get("log_alerts", True)
        self.alert_level = config.get("alert_level", "info")
        self.max_alerts_per_minute = config.get("max_alerts_per_minute", 20)
        self.deduplicate_alerts = config.get("deduplicate_alerts", True)
        self.deduplication_window_seconds = config.get("deduplication_window_seconds", 300)
        self.alert_actions = config.get("alert_actions", {
            "critical": ["log", "notify", "respond"],
            "warning": ["log", "notify"],
            "info": ["log"]
        })
        
        # Set up logging
        self.logger = logging.getLogger("AlertManager")
        
        # Initialize alert storage
        self.alerts = []
        self.alert_hashes = set()
        self.alert_count = 0
        
        # Initialize lock
        self._lock = threading.RLock()
    
    def add_alert(self, level: str, source: str, message: str, details: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """
        Add an alert.
        
        Args:
            level (str): Alert level (critical, warning, info)
            source (str): Alert source
            message (str): Alert message
            details (Optional[Dict[str, Any]], optional): Alert details
            
        Returns:
            Optional[str]: Alert ID or None if alert was deduplicated
        """
        if not self.enabled:
            return None
        
        # Create alert
        alert_id = str(uuid.uuid4())
        timestamp = time.time()
        
        alert = {
            "id": alert_id,
            "timestamp": timestamp,
            "level": level,
            "source": source,
            "message": message,
            "details": details or {}
        }
        
        # Check for rate limiting
        with self._lock:
            # Count alerts in the last minute
            minute_ago = timestamp - 60
            recent_alerts = sum(1 for a in self.alerts if a["timestamp"] > minute_ago)
            
            if recent_alerts >= self.max_alerts_per_minute:
                self.logger.warning(f"Alert rate limit exceeded: {recent_alerts} alerts in the last minute")
                return None
            
            # Check for deduplication
            if self.deduplicate_alerts:
                # Create alert hash
                alert_hash = f"{level}:{source}:{message}"
                
                # Check if similar alert exists
                dedup_window = timestamp - self.deduplication_window_seconds
                
                if alert_hash in self.alert_hashes:
                    # Check if alert is within deduplication window
                    for a in self.alerts:
                        if (
                            a["level"] == level and
                            a["source"] == source and
                            a["message"] == message and
                            a["timestamp"] > dedup_window
                        ):
                            self.logger.debug(f"Alert deduplicated: {message}")
                            return None
                
                # Add alert hash
                self.alert_hashes.add(alert_hash)
            
            # Add alert
            self.alerts.append(alert)
            self.alert_count += 1
            
            # Keep only the last 1000 alerts
            if len(self.alerts) > 1000:
                removed = self.alerts.pop(0)
                # Remove hash for old alert
                if self.deduplicate_alerts:
                    alert_hash = f"{removed['level']}:{removed['source']}:{removed['message']}"
                    self.alert_hashes.discard(alert_hash)
        
        # Log alert
        if "log" in self.alert_actions.get(level, []):
            if level == "critical":
                self.logger.critical(f"[{source}] {message}")
            elif level == "warning":
                self.logger.warning(f"[{source}] {message}")
            else:
                self.logger.info(f"[{source}] {message}")
        
        # Notify user
        if "notify" in self.alert_actions.get(level, []):
            self._notify_user(alert)
        
        # Respond to alert
        if "respond" in self.alert_actions.get(level, []):
            self._respond_to_alert(alert)
        
        return alert_id
    
    def get_recent_alerts(self, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Get recent alerts.
        
        Args:
            limit (int, optional): Maximum number of alerts to return
            
        Returns:
            List[Dict[str, Any]]: Recent alerts
        """
        with self._lock:
            # Sort alerts by timestamp (newest first)
            sorted_alerts = sorted(self.alerts, key=lambda a: a["timestamp"], reverse=True)
            
            # Return limited number of alerts
            return sorted_alerts[:limit]
    
    def get_alert_count(self) -> int:
        """
        Get total alert count.
        
        Returns:
            int: Total alert count
        """
        return self.alert_count
    
    def _notify_user(self, alert: Dict[str, Any]) -> None:
        """
        Notify user of alert.
        
        Args:
            alert (Dict[str, Any]): Alert data
        """
        # This would implement user notification
        # For example, desktop notification, email, SMS, etc.
        pass
    
    def _respond_to_alert(self, alert: Dict[str, Any]) -> None:
        """
        Respond to alert.
        
        Args:
            alert (Dict[str, Any]): Alert data
        """
        # This would implement automated response
        # For example, terminate process, quarantine file, etc.
        pass
