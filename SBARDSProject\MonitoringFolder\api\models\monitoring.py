"""
Monitoring API Models for SBARDS

This module provides API models for the Monitoring phase of the SBARDS project.
"""

from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional
from datetime import datetime

class MonitoringStatus(BaseModel):
    """
    Monitoring status model.
    """
    status: str = Field(..., description="Monitoring status")
    message: str = Field(..., description="Monitoring message")

class AlertResponse(BaseModel):
    """
    Alert response model.
    """
    id: int = Field(..., description="Alert ID")
    source: str = Field(..., description="Alert source")
    type: str = Field(..., description="Alert type")
    message: str = Field(..., description="Alert message")
    severity: str = Field(..., description="Alert severity")
    time: str = Field(..., description="Alert time")
    details: Dict[str, Any] = Field({}, description="Alert details")
    processed: bool = Field(False, description="Alert processed status")
