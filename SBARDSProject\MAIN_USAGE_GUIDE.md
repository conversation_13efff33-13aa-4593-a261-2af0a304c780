# SBARDS Main.py Usage Guide

## Overview

The new `main.py` provides a comprehensive interface for running all SBARDS phases individually or as a complete workflow. All the previous `run_` files have been consolidated into this single entry point.

## Available Phases

### 1. Capture Layer (طبقة الالتقاط)
Handles file ingestion, validation, and initial metadata extraction.

```bash
python main.py --phase capture --file /path/to/file
```

### 2. Pre-Scanning Quick Check (فحص أولي سريع)
Performs rapid threat detection using YARA rules.

```bash
python main.py --phase prescanning --file /path/to/file
```

### 3. Static Analysis Layer (طبقة التحليل الثابت)
Comprehensive static analysis including signatures, entropy, YARA, VirusTotal, and PE analysis.

```bash
python main.py --phase static --file /path/to/file
```

### 4. Dynamic Analysis Layer (طبقة التحليل الديناميكي)
Behavioral analysis in sandbox environment with ML-based detection.

```bash
python main.py --phase dynamic --file /path/to/file
```

### 5. Response Layer (طبقة الاستجابة)
Automated response actions including quarantine, alerts, and permission modifications.

```bash
# Response runs automatically with complete workflow
python main.py --phase workflow --file /path/to/file
```

### 6. Continuous Monitoring Layer (طبقة المراقبة المستمرة)
Real-time system monitoring and threat detection.

```bash
python main.py --phase monitoring --duration 30
```

### 7. Complete Workflow (جميع المراحل)
Executes all phases in the correct order according to the comprehensive workflow.

```bash
python main.py --phase workflow --file /path/to/file
```

### 8. Interactive Mode (الوضع التفاعلي)
Menu-driven interface for selecting and running phases.

```bash
python main.py --phase interactive
# or simply
python main.py
```

## Command Line Options

### Basic Usage
```bash
python main.py [OPTIONS]
```

### Options
- `--phase, -p`: Phase to run (capture, prescanning, static, dynamic, response, monitoring, workflow, interactive)
- `--file, -f`: File path to analyze (required for most phases)
- `--config, -c`: Configuration file path (default: config.json)
- `--output, -o`: Output directory (default: output)
- `--log-level, -l`: Logging level (DEBUG, INFO, WARNING, ERROR)
- `--duration, -d`: Monitoring duration in minutes (default: 60)

## Usage Examples

### 1. Run Complete Workflow
```bash
# Analyze a suspicious file through all phases
python main.py --phase workflow --file /path/to/suspicious.exe

# With custom output directory and debug logging
python main.py --phase workflow --file /path/to/file --output /custom/output --log-level DEBUG
```

### 2. Run Individual Phases
```bash
# Quick pre-scanning check
python main.py --phase prescanning --file /path/to/file

# Detailed static analysis
python main.py --phase static --file /path/to/file

# Behavioral analysis in sandbox
python main.py --phase dynamic --file /path/to/file
```

### 3. Monitoring Mode
```bash
# Monitor system for 30 minutes
python main.py --phase monitoring --duration 30

# Continuous monitoring (60 minutes default)
python main.py --phase monitoring
```

### 4. Interactive Mode
```bash
# Start interactive menu
python main.py --phase interactive

# Or simply
python main.py
```

## Interactive Mode Menu

When running in interactive mode, you'll see:

```
================================================================================
SBARDS - Smart Behavioral Analysis and Ransomware Detection System
================================================================================
Available Phases:
1. Capture Layer (طبقة الالتقاط)
2. Pre-Scanning Quick Check (فحص أولي سريع)
3. Static Analysis Layer (طبقة التحليل الثابت)
4. Dynamic Analysis Layer (طبقة التحليل الديناميكي)
5. Response Layer (طبقة الاستجابة)
6. External Integration Layer (طبقة التكامل الخارجي)
7. Memory Protection Layer (طبقة حماية الذاكرة)
8. Continuous Monitoring Layer (طبقة المراقبة المستمرة)
9. Complete Workflow (جميع المراحل)
10. Interactive Mode (الوضع التفاعلي)
0. Exit
================================================================================

Select phase (0-10):
```

## Output and Results

### Result Files
All phase results are saved as JSON files in the output directory:
- `capture_results_YYYYMMDD_HHMMSS.json`
- `prescanning_results_YYYYMMDD_HHMMSS.json`
- `static_analysis_results_YYYYMMDD_HHMMSS.json`
- `dynamic_analysis_results_YYYYMMDD_HHMMSS.json`
- `monitoring_results_YYYYMMDD_HHMMSS.json`
- `complete_workflow_results_YYYYMMDD_HHMMSS.json`

### Log Output
Detailed logging is provided for each phase:
- ✓ Success indicators
- ⚠ Warning indicators
- ✗ Error indicators
- Detailed analysis results and metrics

## Configuration

The system uses `config.json` for configuration. Key sections:

```json
{
  "capture": {
    "directory": "captured_files",
    "max_file_size_mb": 100
  },
  "static_analysis": {
    "entropy_threshold": 7.5,
    "virustotal": {
      "enabled": false,
      "api_key": ""
    }
  },
  "dynamic_analysis": {
    "analysis_timeout_seconds": 300,
    "sandbox_type": "docker"
  },
  "response": {
    "auto_quarantine": false,
    "notification_methods": ["log", "email"]
  }
}
```

## Best Practices

### 1. File Analysis Workflow
```bash
# Start with pre-scanning for quick assessment
python main.py --phase prescanning --file suspicious.exe

# If suspicious, run complete workflow
python main.py --phase workflow --file suspicious.exe
```

### 2. System Monitoring
```bash
# Start monitoring before analyzing files
python main.py --phase monitoring --duration 120 &

# Then analyze files in another terminal
python main.py --phase workflow --file /path/to/file
```

### 3. Batch Processing
```bash
# Process multiple files
for file in /path/to/files/*; do
    python main.py --phase workflow --file "$file"
done
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed
   ```bash
   pip install -r requirements.txt
   ```

2. **Configuration Errors**: Verify config.json syntax
   ```bash
   python -m json.tool config.json
   ```

3. **Permission Errors**: Run with appropriate permissions
   ```bash
   # On Linux/Mac
   sudo python main.py --phase workflow --file /path/to/file
   
   # On Windows (Run as Administrator)
   python main.py --phase workflow --file C:\path\to\file
   ```

4. **File Not Found**: Use absolute paths
   ```bash
   python main.py --phase workflow --file "$(pwd)/suspicious.exe"
   ```

## Migration from Old run_ Files

### Old Command → New Command
- `python run_prescanning.py` → `python main.py --phase prescanning`
- `python run_monitoring.py` → `python main.py --phase monitoring`
- `python run_sbards.py` → `python main.py --phase workflow`
- `python run_scanner.py` → `python main.py --phase static`

### Benefits of New Structure
- Single entry point for all functionality
- Consistent command-line interface
- Better error handling and logging
- Comprehensive help and documentation
- Interactive mode for ease of use
- Standardized output format

## Advanced Usage

### Custom Configuration
```bash
python main.py --phase workflow --file /path/to/file --config custom_config.json
```

### Debug Mode
```bash
python main.py --phase workflow --file /path/to/file --log-level DEBUG
```

### Automated Scripting
```bash
#!/bin/bash
# Automated analysis script
RESULT=$(python main.py --phase workflow --file "$1" --output results)
echo "Analysis completed. Results in: results/"
```

This new main.py structure provides a comprehensive, user-friendly interface for all SBARDS functionality while maintaining the full power and flexibility of the individual phases.
