#!/usr/bin/env python3
"""
Dependency Installer for SBARDS

This script installs the required dependencies for the SBARDS project.
"""

import os
import sys
import platform
import subprocess
import argparse

def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Install dependencies for SBARDS")
    parser.add_argument("--dev", action="store_true", help="Install development dependencies")
    parser.add_argument("--docker", action="store_true", help="Install Docker dependencies")
    parser.add_argument("--all", action="store_true", help="Install all dependencies")
    return parser.parse_args()

def check_python_version():
    """Check Python version."""
    print("Checking Python version...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print(f"Error: Python 3.9 or higher is required. Found {version.major}.{version.minor}")
        return False
    print(f"Python version {version.major}.{version.minor}.{version.micro} is compatible.")
    return True

def install_pip_dependencies(dev=False, docker=False):
    """Install pip dependencies."""
    print("Installing pip dependencies...")
    
    # Base requirements
    cmd = [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"]
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"Error installing dependencies: {result.stderr}")
        return False
    
    print("Base dependencies installed successfully.")
    
    # Development dependencies
    if dev or docker:
        dev_packages = [
            "pytest>=6.2.5",
            "pytest-asyncio>=0.15.1",
            "black>=21.5b2",
            "isort>=5.9.1",
            "flake8>=3.9.2",
            "mypy>=0.812",
            "sphinx>=4.0.2",
            "sphinx-rtd-theme>=0.5.2"
        ]
        
        cmd = [sys.executable, "-m", "pip", "install"] + dev_packages
        print(f"Installing development dependencies: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"Error installing development dependencies: {result.stderr}")
            return False
        
        print("Development dependencies installed successfully.")
    
    # Docker dependencies
    if docker:
        docker_packages = [
            "docker>=5.0.0",
            "docker-compose>=1.29.2"
        ]
        
        cmd = [sys.executable, "-m", "pip", "install"] + docker_packages
        print(f"Installing Docker dependencies: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"Error installing Docker dependencies: {result.stderr}")
            return False
        
        print("Docker dependencies installed successfully.")
    
    return True

def install_platform_dependencies():
    """Install platform-specific dependencies."""
    system = platform.system()
    print(f"Installing platform-specific dependencies for {system}...")
    
    if system == "Windows":
        # Install Windows-specific dependencies
        try:
            import win32api
            print("pywin32 is already installed.")
        except ImportError:
            print("Installing pywin32...")
            cmd = [sys.executable, "-m", "pip", "install", "pywin32>=301"]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"Error installing pywin32: {result.stderr}")
                return False
            
            print("pywin32 installed successfully.")
    
    elif system == "Linux":
        # Check if YARA is installed
        print("Checking for YARA installation...")
        result = subprocess.run(["which", "yara"], capture_output=True, text=True)
        
        if result.returncode != 0:
            print("YARA not found. Please install YARA manually:")
            print("  Debian/Ubuntu: sudo apt-get install yara")
            print("  CentOS/RHEL: sudo yum install yara")
            print("  Arch Linux: sudo pacman -S yara")
            print("  Or build from source: https://github.com/VirusTotal/yara")
            return False
        
        print("YARA is installed.")
    
    return True

def create_directories():
    """Create necessary directories."""
    print("Creating necessary directories...")
    
    directories = [
        "logs",
        "output",
        "samples",
        "rules"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"Created directory: {directory}")
    
    return True

def main():
    """Main function."""
    print("=== SBARDS Dependency Installer ===")
    
    # Parse arguments
    args = parse_args()
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Install pip dependencies
    if not install_pip_dependencies(dev=args.dev or args.all, docker=args.docker or args.all):
        return 1
    
    # Install platform-specific dependencies
    if not install_platform_dependencies():
        return 1
    
    # Create necessary directories
    if not create_directories():
        return 1
    
    print("\n=== Installation Complete ===")
    print("You can now run SBARDS using:")
    print("  python run.py")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
