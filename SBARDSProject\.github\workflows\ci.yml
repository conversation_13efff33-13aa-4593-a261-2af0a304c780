name: SBARDS CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest]
        python-version: [3.8, 3.9, '3.10']

    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov flake8 mypy safety
        
    - name: Lint with flake8
      run: |
        # stop the build if there are Python syntax errors or undefined names
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        # exit-zero treats all errors as warnings
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=100 --statistics
        
    - name: Type check with mypy
      run: |
        mypy --ignore-missing-imports phases utils
        
    - name: Check for security vulnerabilities
      run: |
        safety check
        
    - name: Run tests
      run: |
        python -m pytest tests/ --cov=. --cov-report=xml
        
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        fail_ci_if_error: false

  build:
    runs-on: ${{ matrix.os }}
    needs: test
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest]
        python-version: [3.9]

    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pyinstaller
        
    - name: Build executable (Windows)
      if: matrix.os == 'windows-latest'
      run: |
        pyinstaller --onefile --name sbards run_sbards_with_monitoring.py
        
    - name: Build executable (Linux)
      if: matrix.os == 'ubuntu-latest'
      run: |
        pyinstaller --onefile --name sbards run_sbards_with_monitoring.py
        
    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: sbards-${{ matrix.os }}
        path: dist/
