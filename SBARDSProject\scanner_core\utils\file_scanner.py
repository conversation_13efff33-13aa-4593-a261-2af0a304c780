import os
import logging
import platform
from pathlib import Path

class FileScanner:
    """
    File scanner for the SBARDS Project.
    Scans directories recursively for files to analyze.
    """
    
    def __init__(self, config):
        """
        Initialize the file scanner.
        
        Args:
            config (dict): Scanner configuration
        """
        self.config = config
        self.target_directory = os.path.abspath(config["target_directory"])
        self.recursive = config.get("recursive", True)
        self.max_depth = config.get("max_depth", 5)
        self.exclude_dirs = config.get("exclude_dirs", [])
        self.exclude_extensions = config.get("exclude_extensions", [])
        self.max_file_size_mb = config.get("max_file_size_mb", 100)
        
        self.logger = logging.getLogger("SBARDS.FileScanner")
        self.platform = platform.system()
        
        # Convert exclude_dirs to absolute paths if they're not already
        self.exclude_dirs = [
            os.path.abspath(d) if not os.path.isabs(d) else d
            for d in self.exclude_dirs
        ]
    
    def scan(self):
        """
        Scan the target directory for files.
        
        Returns:
            list: List of file paths to scan
        """
        self.logger.info(f"Starting file scan in {self.target_directory}")
        self.logger.info(f"Recursive: {self.recursive}, Max depth: {self.max_depth}")
        
        files_to_scan = []
        
        try:
            if self.recursive:
                files_to_scan = self._scan_recursive(self.target_directory, 0)
            else:
                files_to_scan = self._scan_directory(self.target_directory)
            
            self.logger.info(f"Found {len(files_to_scan)} files to scan")
            return files_to_scan
        
        except Exception as e:
            self.logger.error(f"Error scanning directory: {e}")
            return []
    
    def _scan_recursive(self, directory, current_depth):
        """
        Recursively scan a directory for files.
        
        Args:
            directory (str): Directory to scan
            current_depth (int): Current recursion depth
            
        Returns:
            list: List of file paths
        """
        if current_depth > self.max_depth:
            self.logger.debug(f"Max depth reached at {directory}")
            return []
        
        files = []
        
        try:
            # Check if directory should be excluded
            if self._should_exclude_directory(directory):
                self.logger.debug(f"Skipping excluded directory: {directory}")
                return []
            
            # Scan current directory
            files.extend(self._scan_directory(directory))
            
            # Scan subdirectories
            for item in os.listdir(directory):
                item_path = os.path.join(directory, item)
                if os.path.isdir(item_path):
                    files.extend(self._scan_recursive(item_path, current_depth + 1))
            
            return files
        
        except PermissionError:
            self.logger.warning(f"Permission denied: {directory}")
            return []
        
        except Exception as e:
            self.logger.error(f"Error scanning directory {directory}: {e}")
            return []
    
    def _scan_directory(self, directory):
        """
        Scan a single directory for files.
        
        Args:
            directory (str): Directory to scan
            
        Returns:
            list: List of file paths
        """
        files = []
        
        try:
            for item in os.listdir(directory):
                item_path = os.path.join(directory, item)
                
                if os.path.isfile(item_path):
                    if self._should_include_file(item_path):
                        files.append(item_path)
            
            return files
        
        except PermissionError:
            self.logger.warning(f"Permission denied: {directory}")
            return []
        
        except Exception as e:
            self.logger.error(f"Error scanning directory {directory}: {e}")
            return []
    
    def _should_exclude_directory(self, directory):
        """
        Check if a directory should be excluded from scanning.
        
        Args:
            directory (str): Directory path
            
        Returns:
            bool: True if the directory should be excluded, False otherwise
        """
        # Check if directory is in exclude list
        directory_abs = os.path.abspath(directory)
        directory_name = os.path.basename(directory)
        
        for exclude_dir in self.exclude_dirs:
            # Check for exact match
            if directory_abs == exclude_dir:
                return True
            
            # Check for basename match
            if directory_name == os.path.basename(exclude_dir):
                return True
        
        return False
    
    def _should_include_file(self, file_path):
        """
        Check if a file should be included in the scan.
        
        Args:
            file_path (str): File path
            
        Returns:
            bool: True if the file should be included, False otherwise
        """
        # Check file extension
        _, ext = os.path.splitext(file_path)
        if ext.lower() in self.exclude_extensions:
            return False
        
        # Check file size
        try:
            file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
            if file_size_mb > self.max_file_size_mb:
                self.logger.debug(f"Skipping large file: {file_path} ({file_size_mb:.2f} MB)")
                return False
        except Exception as e:
            self.logger.warning(f"Error checking file size for {file_path}: {e}")
            return False
        
        return True
