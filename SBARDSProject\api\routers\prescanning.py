"""
Pre-Scanning API Router for SBARDS

This module provides API endpoints for the Pre-Scanning phase of the SBARDS project.
"""

import os
import logging
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query, Path
from typing import Dict, List, Any, Optional

# Import models
from api.models.prescanning import ScanRequest, ScanResult, ScanStatus

# Import security
from api.security import get_api_key, get_optional_api_key

# Create router
router = APIRouter(
    prefix="/api/prescanning",
    tags=["prescanning"],
    responses={
        404: {"description": "Not found"},
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        500: {"description": "Internal server error"}
    }
)

# Global variables
prescanning_orchestrator = None

# Dependency to get the orchestrator
def get_orchestrator():
    """
    Get the Pre-Scanning orchestrator.

    Returns:
        Any: Pre-Scanning orchestrator
    """
    if prescanning_orchestrator is None:
        raise HTTPException(status_code=503, detail="Pre-Scanning orchestrator not initialized")
    return prescanning_orchestrator

# Set the orchestrator
def set_orchestrator(orchestrator):
    """
    Set the Pre-Scanning orchestrator.

    Args:
        orchestrator: Pre-Scanning orchestrator
    """
    global prescanning_orchestrator
    prescanning_orchestrator = orchestrator

# Endpoints
@router.post("/scan", response_model=ScanResult)
async def start_scan(
    scan_request: ScanRequest,
    background_tasks: BackgroundTasks,
    orchestrator = Depends(get_orchestrator),
    api_key: str = Depends(get_api_key)
):
    """
    Start a scan.

    This endpoint starts a scan of the specified target directory.
    The scan runs in the background and results can be retrieved using the scan ID.

    Args:
        scan_request (ScanRequest): Scan request
        background_tasks (BackgroundTasks): Background tasks
        orchestrator: Pre-Scanning orchestrator
        api_key: API key for authentication

    Returns:
        ScanResult: Scan result with scan ID and status
    """
    # Create scan ID
    scan_id = f"scan_{len(orchestrator.scan_results) + 1}"

    # Start scan in background
    background_tasks.add_task(orchestrator.run_scan, scan_request.target_directory)

    return {
        "scan_id": scan_id,
        "status": "running",
        "message": f"Scan started for {scan_request.target_directory}"
    }

@router.get("/scan/{scan_id}", response_model=ScanStatus)
async def get_scan_status(
    scan_id: str = Path(..., description="Scan ID"),
    orchestrator = Depends(get_orchestrator),
    api_key: Optional[str] = Depends(get_optional_api_key)
):
    """
    Get scan status.

    This endpoint returns the status of a scan and its results if available.

    Args:
        scan_id: Scan ID
        orchestrator: Pre-Scanning orchestrator
        api_key: Optional API key for authentication

    Returns:
        ScanStatus: Scan status and results
    """
    # Check if scan exists
    if scan_id not in orchestrator.scan_results:
        raise HTTPException(status_code=404, detail=f"Scan {scan_id} not found")

    return {
        "scan_id": scan_id,
        "status": "completed" if orchestrator.scan_results[scan_id] else "running",
        "results": orchestrator.scan_results[scan_id]
    }

@router.get("/scans", response_model=List[ScanResult])
async def list_scans(
    limit: int = Query(100, description="Maximum number of scans to return", ge=1, le=1000),
    status: Optional[str] = Query(None, description="Filter scans by status (running, completed)"),
    orchestrator = Depends(get_orchestrator),
    api_key: Optional[str] = Depends(get_optional_api_key)
):
    """
    List all scans.

    This endpoint returns a list of all scans with their status.

    Args:
        limit: Maximum number of scans to return (1-1000)
        status: Filter scans by status (running, completed)
        orchestrator: Pre-Scanning orchestrator
        api_key: Optional API key for authentication

    Returns:
        List of scan results
    """
    # Get all scans
    scans = []
    for scan_id in orchestrator.scan_results:
        scan_status = "completed" if orchestrator.scan_results[scan_id] else "running"

        # Apply status filter
        if status and scan_status != status:
            continue

        scans.append({
            "scan_id": scan_id,
            "status": scan_status,
            "message": f"Scan {scan_id} is {scan_status}"
        })

    # Apply limit
    scans = scans[:limit]

    return scans

@router.delete("/scan/{scan_id}", response_model=Dict[str, Any])
async def delete_scan(
    scan_id: str = Path(..., description="Scan ID"),
    orchestrator = Depends(get_orchestrator),
    api_key: str = Depends(get_api_key)
):
    """
    Delete a scan.

    This endpoint deletes a scan and its results.

    Args:
        scan_id: Scan ID
        orchestrator: Pre-Scanning orchestrator
        api_key: API key for authentication

    Returns:
        Deletion status
    """
    # Check if scan exists
    if scan_id not in orchestrator.scan_results:
        raise HTTPException(status_code=404, detail=f"Scan {scan_id} not found")

    # Delete scan
    del orchestrator.scan_results[scan_id]

    return {
        "status": "success",
        "message": f"Scan {scan_id} deleted"
    }

@router.get("/rules", response_model=List[Dict[str, Any]])
async def list_rules(
    orchestrator = Depends(get_orchestrator),
    api_key: Optional[str] = Depends(get_optional_api_key)
):
    """
    List all available rules.

    This endpoint returns a list of all available YARA rules.

    Args:
        orchestrator: Pre-Scanning orchestrator
        api_key: Optional API key for authentication

    Returns:
        List of rules
    """
    # Check if the orchestrator has the get_rules method
    if not hasattr(orchestrator, "get_rules"):
        # Return mock data for demonstration
        return [
            {
                "name": "suspicious_file",
                "description": "Detects suspicious files",
                "tags": ["malware", "suspicious"],
                "file": "rules/custom_rules.yar"
            },
            {
                "name": "ransomware",
                "description": "Detects ransomware",
                "tags": ["malware", "ransomware"],
                "file": "rules/custom_rules.yar"
            },
            {
                "name": "backdoor",
                "description": "Detects backdoors",
                "tags": ["malware", "backdoor"],
                "file": "rules/custom_rules.yar"
            }
        ]

    return orchestrator.get_rules()
