"""
Fast Track Manager for SBARDS

This module provides fast-track processing for high-priority items.
"""

import os
import time
import logging
import threading
import queue
from typing import Dict, List, Any, Optional, Set, Tuple, Callable
from datetime import datetime

class FastTrackManager:
    """
    Manages fast-track processing for high-priority items.
    
    This class provides mechanisms for:
    1. Expediting the processing of high-priority files and processes
    2. Bypassing normal processing queues for critical items
    3. Coordinating immediate responses to high-risk threats
    4. Providing direct communication between layers for urgent matters
    """
    
    def __init__(self, config: Dict[str, Any], shared_state=None):
        """
        Initialize the fast track manager.
        
        Args:
            config (Dict[str, Any]): Configuration dictionary
            shared_state: Optional shared state instance
        """
        self.config = config
        self.logger = logging.getLogger("SBARDS.FastTrackManager")
        
        # Initialize shared state if not provided
        if shared_state is None:
            from .shared_state import SharedState
            self.shared_state = SharedState()
        else:
            self.shared_state = shared_state
            
        # Fast track configuration
        self.fast_track_config = config.get("integration", {}).get("fast_track", {})
        self.enabled = self.fast_track_config.get("enabled", True)
        
        # Fast track thresholds
        self.priority_threshold = self.fast_track_config.get("priority_threshold", 75)
        self.max_concurrent_fast_tracks = self.fast_track_config.get("max_concurrent", 5)
        
        # Fast track queues
        self.file_queue = queue.PriorityQueue()
        self.process_queue = queue.PriorityQueue()
        
        # Active fast tracks
        self.active_fast_tracks = set()
        
        # Callback handlers
        self.file_handlers = []
        self.process_handlers = []
        self.network_handlers = []
        
        # Thread safety
        self._lock = threading.RLock()
        
        # Worker thread
        self.worker_thread = None
        self.stop_event = threading.Event()
        
        self.logger.info("Fast Track Manager initialized")
        
    def start(self):
        """Start the fast track manager."""
        if not self.enabled:
            self.logger.warning("Fast track is not enabled in configuration")
            return False
            
        if self.worker_thread and self.worker_thread.is_alive():
            self.logger.warning("Fast track manager is already running")
            return True
            
        self.logger.info("Starting fast track manager")
        self.stop_event.clear()
        
        # Start worker thread
        self.worker_thread = threading.Thread(
            target=self._worker_loop,
            daemon=True
        )
        self.worker_thread.start()
        
        self.logger.info("Fast track manager started")
        return True
        
    def stop(self):
        """Stop the fast track manager."""
        if not self.worker_thread or not self.worker_thread.is_alive():
            self.logger.warning("Fast track manager is not running")
            return True
            
        self.logger.info("Stopping fast track manager")
        self.stop_event.set()
        
        # Wait for worker thread to complete
        self.worker_thread.join(timeout=5.0)
        
        self.logger.info("Fast track manager stopped")
        return True
        
    def fast_track_file(self, file_path: str, priority: int = None, metadata: Optional[Dict[str, Any]] = None):
        """
        Fast-track a file for immediate processing.
        
        Args:
            file_path (str): Path to the file
            priority (int, optional): Priority level (higher is more important)
            metadata (Optional[Dict[str, Any]]): Additional metadata
        """
        if not self.enabled or not file_path:
            return False
            
        # Get priority if not provided
        if priority is None:
            from .priority_manager import PriorityManager
            priority_manager = PriorityManager(self.config, self.shared_state)
            priority = priority_manager.get_file_priority(file_path, metadata)
            
        # Check if priority meets threshold
        if priority < self.priority_threshold:
            self.logger.debug(f"File {file_path} priority {priority} below threshold {self.priority_threshold}")
            return False
            
        # Create fast track request
        request = {
            "type": "file",
            "path": file_path,
            "priority": priority,
            "timestamp": time.time(),
            "metadata": metadata or {}
        }
        
        # Add to shared state
        self.shared_state.add_fast_track_request(request)
        
        # Add to queue (negative priority for priority queue ordering)
        self.file_queue.put((-priority, request))
        
        self.logger.info(f"Fast-tracked file {file_path} with priority {priority}")
        return True
        
    def fast_track_process(self, process_id: str, priority: int = None, metadata: Optional[Dict[str, Any]] = None):
        """
        Fast-track a process for immediate monitoring.
        
        Args:
            process_id (str): Process ID
            priority (int, optional): Priority level (higher is more important)
            metadata (Optional[Dict[str, Any]]): Additional metadata
        """
        if not self.enabled or not process_id:
            return False
            
        # Get priority if not provided
        if priority is None:
            from .priority_manager import PriorityManager
            priority_manager = PriorityManager(self.config, self.shared_state)
            priority = priority_manager.get_process_priority(process_id, metadata)
            
        # Check if priority meets threshold
        if priority < self.priority_threshold:
            self.logger.debug(f"Process {process_id} priority {priority} below threshold {self.priority_threshold}")
            return False
            
        # Create fast track request
        request = {
            "type": "process",
            "process_id": process_id,
            "priority": priority,
            "timestamp": time.time(),
            "metadata": metadata or {}
        }
        
        # Add to shared state
        self.shared_state.add_fast_track_request(request)
        
        # Add to queue (negative priority for priority queue ordering)
        self.process_queue.put((-priority, request))
        
        self.logger.info(f"Fast-tracked process {process_id} with priority {priority}")
        return True
        
    def register_file_handler(self, handler: Callable[[Dict[str, Any]], None]):
        """
        Register a handler for fast-tracked files.
        
        Args:
            handler (Callable): Handler function that takes a request dictionary
        """
        with self._lock:
            self.file_handlers.append(handler)
            
    def register_process_handler(self, handler: Callable[[Dict[str, Any]], None]):
        """
        Register a handler for fast-tracked processes.
        
        Args:
            handler (Callable): Handler function that takes a request dictionary
        """
        with self._lock:
            self.process_handlers.append(handler)
            
    def register_network_handler(self, handler: Callable[[Dict[str, Any]], None]):
        """
        Register a handler for fast-tracked network connections.
        
        Args:
            handler (Callable): Handler function that takes a request dictionary
        """
        with self._lock:
            self.network_handlers.append(handler)
            
    def _worker_loop(self):
        """Main worker loop for processing fast-track requests."""
        while not self.stop_event.is_set():
            try:
                # Check if we can process more fast tracks
                with self._lock:
                    if len(self.active_fast_tracks) >= self.max_concurrent_fast_tracks:
                        # Wait a bit before checking again
                        self.stop_event.wait(0.1)
                        continue
                        
                # Try to get a file request first (file requests have priority over process requests)
                try:
                    _, request = self.file_queue.get(block=False)
                    self._handle_file_request(request)
                    self.file_queue.task_done()
                    continue
                except queue.Empty:
                    pass
                    
                # Try to get a process request
                try:
                    _, request = self.process_queue.get(block=False)
                    self._handle_process_request(request)
                    self.process_queue.task_done()
                    continue
                except queue.Empty:
                    pass
                    
                # No requests, wait a bit
                self.stop_event.wait(0.1)
                
            except Exception as e:
                self.logger.error(f"Error in fast track worker: {e}")
                # Wait a bit before retrying
                self.stop_event.wait(1.0)
                
    def _handle_file_request(self, request: Dict[str, Any]):
        """
        Handle a fast-tracked file request.
        
        Args:
            request (Dict[str, Any]): Fast-track request
        """
        file_path = request.get("path")
        if not file_path:
            return
            
        # Add to active fast tracks
        request_id = f"file_{file_path}_{int(request.get('timestamp', time.time()) * 1000)}"
        with self._lock:
            self.active_fast_tracks.add(request_id)
            
        try:
            # Call handlers
            for handler in self.file_handlers:
                try:
                    handler(request)
                except Exception as e:
                    self.logger.error(f"Error in file handler: {e}")
                    
        finally:
            # Remove from active fast tracks
            with self._lock:
                self.active_fast_tracks.discard(request_id)
                
    def _handle_process_request(self, request: Dict[str, Any]):
        """
        Handle a fast-tracked process request.
        
        Args:
            request (Dict[str, Any]): Fast-track request
        """
        process_id = request.get("process_id")
        if not process_id:
            return
            
        # Add to active fast tracks
        request_id = f"process_{process_id}_{int(request.get('timestamp', time.time()) * 1000)}"
        with self._lock:
            self.active_fast_tracks.add(request_id)
            
        try:
            # Call handlers
            for handler in self.process_handlers:
                try:
                    handler(request)
                except Exception as e:
                    self.logger.error(f"Error in process handler: {e}")
                    
        finally:
            # Remove from active fast tracks
            with self._lock:
                self.active_fast_tracks.discard(request_id)
