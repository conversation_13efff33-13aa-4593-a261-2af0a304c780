#!/usr/bin/env python3
"""
Initialize the database for the SBARDS Backend API.

This script initializes the database for the SBARDS Backend API,
creating the necessary tables and initial data.
"""

import os
import sys
import argparse
import logging
import subprocess
import dotenv

# Load environment variables from .env file if it exists
dotenv.load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("init_db.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("SBARDS.Backend.InitDB")


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="SBARDS Backend Database Initialization",
        usage="%(prog)s [options]"
    )
    
    parser.add_argument("--reset", action="store_true", help="Reset the database (drop all tables)")
    parser.add_argument("--migrate", action="store_true", help="Run database migrations")
    parser.add_argument("--seed", action="store_true", help="Seed the database with initial data")
    parser.add_argument("--all", action="store_true", help="Perform all operations (reset, migrate, seed)")
    parser.add_argument("--database-url", help="Database URL (overrides environment variable)")
    
    return parser.parse_args()


def reset_database(database_url):
    """
    Reset the database by dropping all tables.
    
    Args:
        database_url (str): Database URL.
    
    Returns:
        bool: True if successful, False otherwise.
    """
    try:
        logger.info("Resetting database...")
        
        # If using SQLite, simply delete the database file
        if database_url.startswith("sqlite:///"):
            db_path = database_url.replace("sqlite:///", "")
            if os.path.exists(db_path):
                os.remove(db_path)
                logger.info(f"Deleted SQLite database file: {db_path}")
            return True
        
        # For other databases, use Alembic to drop all tables
        result = subprocess.run(
            ["alembic", "downgrade", "base"],
            check=True,
            capture_output=True,
            text=True
        )
        
        logger.info(f"Database reset successful: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Error resetting database: {e.stderr}")
        return False
    except Exception as e:
        logger.error(f"Error resetting database: {e}")
        return False


def run_migrations():
    """
    Run database migrations using Alembic.
    
    Returns:
        bool: True if successful, False otherwise.
    """
    try:
        logger.info("Running database migrations...")
        
        # Run Alembic migrations
        result = subprocess.run(
            ["alembic", "upgrade", "head"],
            check=True,
            capture_output=True,
            text=True
        )
        
        logger.info(f"Database migrations successful: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Error running database migrations: {e.stderr}")
        return False
    except Exception as e:
        logger.error(f"Error running database migrations: {e}")
        return False


def seed_database():
    """
    Seed the database with initial data.
    
    Returns:
        bool: True if successful, False otherwise.
    """
    try:
        logger.info("Seeding database with initial data...")
        
        # Import database models and session
        from app.db.session import SessionLocal
        from app.db.models import ScanReport, FileResult
        
        # Create a database session
        db = SessionLocal()
        
        # Check if database is already seeded
        if db.query(ScanReport).count() > 0:
            logger.info("Database already contains data, skipping seeding.")
            db.close()
            return True
        
        # Create a sample scan report
        sample_report = ScanReport(
            scan_id="sample_scan_001",
            scan_path="/sample/path",
            files_scanned=5,
            threats_found=1,
            report_path="/sample/path/report.html",
            report_content="<html><body><h1>Sample Scan Report</h1><p>This is a sample scan report.</p></body></html>"
        )
        db.add(sample_report)
        db.commit()
        db.refresh(sample_report)
        
        # Create a sample file result
        sample_file_result = FileResult(
            scan_report_id=sample_report.id,
            file_path="/sample/path/malware.exe",
            file_hash="0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef",
            is_threat=True,
            threat_type="Sample Threat"
        )
        db.add(sample_file_result)
        db.commit()
        
        logger.info("Database seeded successfully.")
        db.close()
        return True
    except Exception as e:
        logger.error(f"Error seeding database: {e}")
        return False


def main():
    """Main function."""
    args = parse_arguments()
    
    # Set database URL from command line argument or environment variable
    if args.database_url:
        os.environ["DATABASE_URL"] = args.database_url
        logger.info(f"Using database URL from command line: {args.database_url}")
    
    database_url = os.environ.get("DATABASE_URL", "sqlite:///./sbards.db")
    
    # Determine operations to perform
    do_reset = args.reset or args.all
    do_migrate = args.migrate or args.all
    do_seed = args.seed or args.all
    
    # If no operations specified, show help
    if not (do_reset or do_migrate or do_seed):
        logger.info("No operations specified. Use --reset, --migrate, --seed, or --all.")
        return 0
    
    # Reset database if requested
    if do_reset:
        if not reset_database(database_url):
            logger.error("Database reset failed.")
            return 1
    
    # Run migrations if requested
    if do_migrate:
        if not run_migrations():
            logger.error("Database migrations failed.")
            return 1
    
    # Seed database if requested
    if do_seed:
        if not seed_database():
            logger.error("Database seeding failed.")
            return 1
    
    logger.info("Database initialization completed successfully.")
    return 0


if __name__ == "__main__":
    sys.exit(main())
