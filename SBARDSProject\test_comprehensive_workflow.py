#!/usr/bin/env python3
"""
SBARDS Comprehensive Workflow Test Script

This script demonstrates the complete SBARDS workflow implementation
and tests all phases with sample files.
"""

import json
import logging
import sys
from pathlib import Path
import tempfile
import os

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

# Import workflow orchestrator
try:
    from phases.integration.workflow_orchestrator import WorkflowOrchestrator
except ImportError as e:
    print(f"Error importing workflow orchestrator: {e}")
    print("Make sure all dependencies are installed and paths are correct.")
    sys.exit(1)


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('workflow_test.log')
        ]
    )


def load_config():
    """Load configuration from config.json."""
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
        return config
    except FileNotFoundError:
        print("Error: config.json not found")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"Error parsing config.json: {e}")
        sys.exit(1)


def create_test_files():
    """Create test files for workflow testing."""
    test_files = {}
    
    # Create temporary directory for test files
    test_dir = Path("test_workflow_files")
    test_dir.mkdir(exist_ok=True)
    
    # 1. Clean text file
    clean_file = test_dir / "clean_sample.txt"
    with open(clean_file, 'w') as f:
        f.write("This is a clean test file for SBARDS workflow testing.\n")
        f.write("It contains no malicious content.\n")
    test_files["clean"] = str(clean_file)
    
    # 2. Suspicious file with high entropy
    suspicious_file = test_dir / "suspicious_sample.bin"
    with open(suspicious_file, 'wb') as f:
        # Write random-looking data to trigger entropy detection
        import random
        random_data = bytes([random.randint(0, 255) for _ in range(1024)])
        f.write(random_data)
    test_files["suspicious"] = str(suspicious_file)
    
    # 3. Simulated malware file
    malware_file = test_dir / "malware_sample.exe"
    with open(malware_file, 'wb') as f:
        # Write PE header and suspicious strings
        f.write(b'MZ')  # PE header
        f.write(b'\x00' * 58)
        f.write(b'This program cannot be run in DOS mode')
        f.write(b'\x00' * 100)
        f.write(b'CreateProcess')  # Suspicious API
        f.write(b'VirtualAlloc')   # Suspicious API
        f.write(b'encrypt')        # Suspicious string
        f.write(b'ransom')         # Suspicious string
    test_files["malware"] = str(malware_file)
    
    return test_files


def test_workflow_phase(orchestrator, file_path, file_type):
    """Test workflow with a specific file."""
    print(f"\n{'='*60}")
    print(f"Testing {file_type.upper()} file: {Path(file_path).name}")
    print(f"{'='*60}")
    
    try:
        # Process file through workflow
        result = orchestrator.process_file(file_path)
        
        # Display results
        print(f"Workflow ID: {result.get('workflow_id', 'N/A')}")
        print(f"Timestamp: {result.get('timestamp', 'N/A')}")
        
        # Show phase results
        phases = result.get('phases', {})
        for phase_name, phase_result in phases.items():
            print(f"\n{phase_name.upper()} Phase:")
            if isinstance(phase_result, dict):
                if 'success' in phase_result:
                    status = "SUCCESS" if phase_result['success'] else "FAILED"
                    print(f"  Status: {status}")
                
                if 'error' in phase_result:
                    print(f"  Error: {phase_result['error']}")
                
                if phase_name == 'static_analysis' and 'risk_assessment' in phase_result:
                    risk = phase_result['risk_assessment']
                    print(f"  Risk Level: {risk.get('risk_level', 'N/A')}")
                    print(f"  Risk Score: {risk.get('risk_score', 'N/A')}")
                
                if phase_name == 'dynamic_analysis' and 'risk_assessment' in phase_result:
                    risk = phase_result['risk_assessment']
                    print(f"  Risk Level: {risk.get('risk_level', 'N/A')}")
                    print(f"  Risk Score: {risk.get('risk_score', 'N/A')}")
        
        # Show final decision
        final_decision = result.get('final_decision', {})
        print(f"\nFINAL DECISION:")
        print(f"  Decision: {final_decision.get('decision', 'N/A')}")
        print(f"  Reason: {final_decision.get('reason', 'N/A')}")
        
        # Show blockchain hash if available
        if result.get('blockchain_hash'):
            print(f"  Blockchain Hash: {result['blockchain_hash']}")
        
        return result
        
    except Exception as e:
        print(f"Error testing {file_type} file: {e}")
        return None


def test_individual_phases():
    """Test individual phases separately."""
    print(f"\n{'='*60}")
    print("TESTING INDIVIDUAL PHASES")
    print(f"{'='*60}")
    
    config = load_config()
    
    # Test Capture Layer
    try:
        from phases.capture.capture import CaptureLayer
        capture_layer = CaptureLayer(config)
        
        # Create a test file
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("Test file for capture layer")
            test_file = f.name
        
        result = capture_layer.capture_file(test_file)
        print(f"Capture Layer: {'SUCCESS' if result.get('success') else 'FAILED'}")
        
        # Cleanup
        os.unlink(test_file)
        if result.get('success') and result.get('file_path'):
            try:
                os.unlink(result['file_path'])
            except:
                pass
                
    except Exception as e:
        print(f"Capture Layer: FAILED - {e}")
    
    # Test Static Analysis
    try:
        from phases.static_analysis.static_analyzer import StaticAnalyzer
        static_analyzer = StaticAnalyzer(config)
        
        # Create a test file
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("Test file for static analysis")
            test_file = f.name
        
        result = static_analyzer.analyze_file(test_file)
        print(f"Static Analysis: {'SUCCESS' if 'error' not in result else 'FAILED'}")
        
        # Cleanup
        os.unlink(test_file)
        
    except Exception as e:
        print(f"Static Analysis: FAILED - {e}")
    
    # Test Response Layer
    try:
        from phases.response.response import ResponseLayer
        response_layer = ResponseLayer(config)
        
        # Test alert sending
        test_results = {
            "workflow_id": "test_workflow",
            "final_decision": {"decision": "ALLOWED", "reason": "Test"},
            "phases": {"capture": {"file_info": {"original_filename": "test.txt"}}}
        }
        
        result = response_layer.send_alerts(test_results)
        print(f"Response Layer: {'SUCCESS' if result.get('alerts_sent') else 'FAILED'}")
        
    except Exception as e:
        print(f"Response Layer: FAILED - {e}")


def main():
    """Main test function."""
    print("SBARDS Comprehensive Workflow Test")
    print("=" * 60)
    
    # Setup logging
    setup_logging()
    
    # Load configuration
    config = load_config()
    
    # Test individual phases first
    test_individual_phases()
    
    # Initialize workflow orchestrator
    try:
        orchestrator = WorkflowOrchestrator(config)
        print("\nWorkflow Orchestrator initialized successfully!")
    except Exception as e:
        print(f"Error initializing workflow orchestrator: {e}")
        return
    
    # Create test files
    print("\nCreating test files...")
    test_files = create_test_files()
    
    # Test workflow with different file types
    results = {}
    
    for file_type, file_path in test_files.items():
        result = test_workflow_phase(orchestrator, file_path, file_type)
        if result:
            results[file_type] = result
    
    # Summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print(f"{'='*60}")
    
    for file_type, result in results.items():
        decision = result.get('final_decision', {}).get('decision', 'ERROR')
        print(f"{file_type.upper()} file: {decision}")
    
    print(f"\nTest files created in: test_workflow_files/")
    print(f"Workflow results saved in: workflow_results/")
    print(f"Test log saved in: workflow_test.log")
    
    # Cleanup test files
    cleanup = input("\nCleanup test files? (y/n): ").lower().strip()
    if cleanup == 'y':
        import shutil
        try:
            shutil.rmtree("test_workflow_files")
            print("Test files cleaned up.")
        except Exception as e:
            print(f"Error cleaning up test files: {e}")


if __name__ == "__main__":
    main()
