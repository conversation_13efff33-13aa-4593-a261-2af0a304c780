"""
Configuration settings for the SBARDS Backend API.

This module provides configuration settings for the SBARDS Backend API,
including database connection, API settings, and external service integrations.
"""

import os
import secrets
from typing import Any, Dict, List, Optional, Union

from pydantic import AnyHttpUrl, field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""

    # API settings
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "SBARDS Backend API"
    PROJECT_DESCRIPTION: str = """
    Backend API for the Security-Based Automated Ransomware Detection System.

    This API provides:
    - Collection of scan reports from SBARDS scanners
    - Integration with VirusTotal for file hash verification
    - Centralized logging and reporting
    - REST API for UIs, dashboards, and further integration
    """
    PROJECT_VERSION: str = "1.0.0"

    # CORS settings
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        """Parse CORS origins from string or list."""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # Security settings
    SECRET_KEY: str = secrets.token_urlsafe(32)
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days
    API_KEY: str = os.getenv("API_KEY", "")
    API_USERNAME: str = os.getenv("API_USERNAME", "admin")
    API_PASSWORD: str = os.getenv("API_PASSWORD", "password")

    # Database settings
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./sbards.db")

    # VirusTotal settings
    VIRUSTOTAL_API_KEY: str = os.getenv("VIRUSTOTAL_API_KEY", "")
    VIRUSTOTAL_API_URL: str = "https://www.virustotal.com/api/v3"

    # File upload settings
    UPLOAD_DIR: str = os.getenv("UPLOAD_DIR", "./uploads")
    MAX_UPLOAD_SIZE: int = 50 * 1024 * 1024  # 50 MB

    # Logging settings
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE: str = os.getenv("LOG_FILE", "backend_api.log")

    class Config:
        """Pydantic config."""
        case_sensitive = True
        env_file = ".env"


# Create settings instance
settings = Settings()
