# SBARDS Pre-scanning Phase: Workflow Diagrams

## Pre-scanning Process Flow

```mermaid
flowchart TD
    A[Start Pre-scanning] --> B[Load Configuration]
    B --> C[Initialize Logging]
    C --> D[Initialize Scanners]
    D --> E[Discover Files]
    E --> F[Compile YARA Rules]
    F --> G[Batch Processing]
    
    G --> H{More Batches?}
    H -- Yes --> I[Process Next Batch]
    H -- No --> N[Generate Reports]
    
    I --> J[Check Memory Usage]
    J --> K[Adjust Thread Count]
    K --> L[Scan Files in Parallel]
    L --> M[Collect Results]
    M --> H
    
    N --> O[Save JSON Report]
    O --> P[Save CSV Report]
    P --> Q[Save HTML Report]
    Q --> R[End Pre-scanning]
```

## Component Interaction Diagram

```mermaid
sequenceDiagram
    participant Main as run_prescanning.py
    participant Orchestrator
    participant FileScanner
    participant YaraScanner
    participant CPPScanner
    participant Reporter
    
    Main->>Orchestrator: Initialize
    Orchestrator->>Orchestrator: Load Configuration
    Orchestrator->>FileScanner: Initialize
    Orchestrator->>YaraScanner: Initialize
    YaraScanner->>YaraScanner: Compile Rules
    
    Main->>Orchestrator: run_scan()
    Orchestrator->>FileScanner: scan()
    FileScanner-->>Orchestrator: Return Files
    
    loop For Each Batch
        Orchestrator->>Orchestrator: Check Memory
        Orchestrator->>Orchestrator: Adjust Threads
        
        par Parallel Scanning
            Orchestrator->>YaraScanner: scan_file(file1)
            Orchestrator->>YaraScanner: scan_file(file2)
            Orchestrator->>CPPScanner: scan_file(file3)
        end
        
        YaraScanner-->>Orchestrator: Return Results
        CPPScanner-->>Orchestrator: Return Results
    end
    
    Orchestrator->>Reporter: Generate Reports
    Reporter->>Reporter: Save JSON
    Reporter->>Reporter: Save CSV
    Reporter->>Reporter: Save HTML
    
    Orchestrator-->>Main: Return Results
```

## Memory Management Workflow

```mermaid
flowchart TD
    A[Start Batch Processing] --> B[Check Current Memory Usage]
    
    B --> C{Memory > 95%?}
    C -- Yes --> D[Emergency Measures]
    C -- No --> E{Memory > 90%?}
    
    D --> F[Force Garbage Collection]
    F --> G[Reduce Batch Size by 75%]
    G --> H[Set Threads to 1]
    H --> L
    
    E -- Yes --> I[Reduce Threads by 75%]
    E -- No --> J{Memory > 75%?}
    
    J -- Yes --> K[Reduce Threads by 50%]
    J -- No --> L[Use Default Thread Count]
    
    L --> M[Process Batch]
    M --> N[Force Garbage Collection]
    
    N --> O{Memory > 85%?}
    O -- Yes --> P[Pause Processing]
    O -- No --> Q{More Batches?}
    
    P --> Q
    Q -- Yes --> A
    Q -- No --> R[End Batch Processing]
```

## File Discovery Process

```mermaid
flowchart TD
    A[Start File Discovery] --> B[Get Target Directory]
    B --> C{Directory Exists?}
    
    C -- No --> D[Log Error]
    D --> E[Return Empty List]
    
    C -- Yes --> F[Walk Directory Tree]
    
    F --> G{Check Directory Depth}
    G -- Too Deep --> F
    
    G -- OK --> H[Filter Excluded Directories]
    H --> I[Process Files in Directory]
    
    I --> J{Check File Extension}
    J -- Excluded --> I
    J -- Included --> K[Add to File List]
    
    K --> L{More Files?}
    L -- Yes --> J
    L -- No --> M{More Directories?}
    
    M -- Yes --> F
    M -- No --> N[Return File List]
```

## YARA Rule Compilation Process

```mermaid
flowchart TD
    A[Start Rule Compilation] --> B[Load Rule Files]
    
    B --> C{Rule Files Exist?}
    C -- No --> D[Log Error]
    D --> E[Return Failure]
    
    C -- Yes --> F[Parse Rule Files]
    
    F --> G{Syntax Valid?}
    G -- No --> H[Log Syntax Error]
    H --> I[Return Failure]
    
    G -- Yes --> J[Compile Rules]
    
    J --> K{Compilation Successful?}
    K -- No --> L[Log Compilation Error]
    L --> M[Return Failure]
    
    K -- Yes --> N[Return Compiled Rules]
```

## Reporting Process

```mermaid
flowchart TD
    A[Start Reporting] --> B[Collect Scan Results]
    
    B --> C[Process Results]
    C --> D[Calculate Statistics]
    
    D --> E{Generate JSON?}
    E -- Yes --> F[Format JSON Data]
    F --> G[Save JSON Report]
    E -- No --> H
    
    G --> H{Generate CSV?}
    H -- Yes --> I[Format CSV Data]
    I --> J[Save CSV Report]
    H -- No --> K
    
    J --> K{Generate HTML?}
    K -- Yes --> L[Format HTML Data]
    L --> M[Create HTML Template]
    M --> N[Generate Charts]
    N --> O[Save HTML Report]
    K -- No --> P
    
    O --> P[End Reporting]
```

## Cross-Platform Compatibility Workflow

```mermaid
flowchart TD
    A[Start Cross-Platform Handling] --> B{Detect Platform}
    
    B -- Windows --> C[Windows-Specific Setup]
    B -- Linux --> D[Linux-Specific Setup]
    
    C --> E[Set Windows Path Separator]
    E --> F[Configure Windows Unicode Support]
    F --> G[Set Windows Environment Variables]
    
    D --> H[Set Linux Path Separator]
    H --> I[Configure Linux File Permissions]
    I --> J[Set Linux Environment Variables]
    
    G --> K[Common Path Handling]
    J --> K
    
    K --> L[Use os.path.join for Paths]
    L --> M[Use UTF-8 Encoding for Files]
    M --> N[Use Binary Mode for File I/O]
    
    N --> O[End Cross-Platform Handling]
```

## Unicode Support Implementation

```mermaid
flowchart TD
    A[Start Unicode Handling] --> B{Detect Platform}
    
    B -- Windows --> C[Windows Unicode Setup]
    B -- Linux --> D[Linux Unicode Setup]
    
    C --> E[Set Console Code Page to UTF-8]
    E --> F[Set PYTHONUTF8=1 Environment Variable]
    F --> G[Set PYTHONIOENCODING=utf-8]
    
    D --> H[Set LANG Environment Variable]
    H --> I[Set LC_ALL Environment Variable]
    I --> J[Set PYTHONIOENCODING=utf-8]
    
    G --> K[Common Unicode Handling]
    J --> K
    
    K --> L[Use UTF-8 Encoding for File I/O]
    L --> M[Use File-Based Communication]
    M --> N[Avoid Console Output Capture]
    
    N --> O[End Unicode Handling]
```

## Performance Optimization Workflow

```mermaid
flowchart TD
    A[Start Performance Optimization] --> B[Analyze File Characteristics]
    
    B --> C[Sort Files by Priority]
    C --> D[Sort Files by Size]
    
    D --> E[Calculate Optimal Batch Size]
    E --> F[Calculate Initial Thread Count]
    
    F --> G[Monitor System Resources]
    
    G --> H{CPU Usage High?}
    H -- Yes --> I[Reduce Thread Count]
    H -- No --> J{Memory Usage High?}
    
    J -- Yes --> K[Reduce Batch Size]
    J -- No --> L[Use Optimal Settings]
    
    I --> L
    K --> L
    
    L --> M[Process Batch]
    
    M --> N{More Batches?}
    N -- Yes --> G
    N -- No --> O[End Performance Optimization]
```

## Error Handling Workflow

```mermaid
flowchart TD
    A[Start Error Handling] --> B{Error Type?}
    
    B -- File Access --> C[Handle File Access Error]
    B -- YARA Compilation --> D[Handle YARA Error]
    B -- Memory --> E[Handle Memory Error]
    B -- Unicode --> F[Handle Unicode Error]
    B -- Other --> G[Handle Generic Error]
    
    C --> H[Log File Access Error]
    H --> I[Skip File]
    I --> N
    
    D --> J[Log YARA Error]
    J --> K[Use Fallback Scanner]
    K --> N
    
    E --> L[Log Memory Error]
    L --> M[Trigger Garbage Collection]
    M --> N
    
    F --> O[Log Unicode Error]
    O --> P[Use File-Based Communication]
    P --> N
    
    G --> Q[Log Generic Error]
    Q --> R[Continue with Next Item]
    R --> N
    
    N[Continue Processing] --> S[End Error Handling]
```
