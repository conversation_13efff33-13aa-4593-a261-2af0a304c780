"""
Pydantic schemas for scan operations.

This module provides Pydantic schemas for scan operations.
"""

from typing import Dict, List, Optional, Any

from pydantic import BaseModel, Field


class ScanRequest(BaseModel):
    """Schema for scan requests."""
    
    path: Optional[str] = Field(None, description="Path to scan (local to the server)")
    recursive: bool = Field(False, description="Scan directories recursively")
    max_depth: Optional[int] = Field(None, description="Maximum directory depth to scan")
    exclude_dirs: Optional[List[str]] = Field(None, description="Directories to exclude from scan")
    exclude_extensions: Optional[List[str]] = Field(None, description="File extensions to exclude from scan")
    max_file_size_mb: Optional[int] = Field(None, description="Maximum file size to scan in MB")


class ScanResponse(BaseModel):
    """Schema for scan responses."""
    
    scan_id: str = Field(..., description="Unique identifier for the scan")
    status: str = Field(..., description="Status of the scan")
    message: Optional[str] = Field(None, description="Additional message about the scan")


class ScanStatus(BaseModel):
    """Schema for scan status."""
    
    scan_id: str = Field(..., description="Unique identifier for the scan")
    status: str = Field(..., description="Status of the scan")
    files_scanned: Optional[int] = Field(None, description="Number of files scanned")
    threats_found: Optional[int] = Field(None, description="Number of threats found")
    report_id: Optional[int] = Field(None, description="ID of the scan report")
    message: Optional[str] = Field(None, description="Additional message about the scan")
