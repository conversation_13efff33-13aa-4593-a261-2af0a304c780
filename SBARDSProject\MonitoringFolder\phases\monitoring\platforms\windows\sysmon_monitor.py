"""
Sysmon Monitor for Windows

This module provides Sysmon-based monitoring for Windows systems.
"""

import os
import time
import logging
import threading
import win32evtlog
import win32con
import win32evtlogutil
import winerror
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set

class SysmonMonitor:
    """
    Sysmon-based monitoring for Windows systems.

    This class monitors Sysmon events from the Windows Event Log.
    """

    def __init__(self, config: Dict[str, Any], alert_manager=None):
        """
        Initialize the Sysmon monitor.

        Args:
            config (Dict[str, Any]): Configuration dictionary
            alert_manager: Alert manager instance
        """
        self.config = config
        self.alert_manager = alert_manager
        self.logger = logging.getLogger("SBARDS.Windows.SysmonMonitor")

        # Check if Sysmon is installed
        self.sysmon_installed = self._check_sysmon_installed()
        if not self.sysmon_installed:
            self.logger.warning("Sysmon is not installed. Some monitoring features will be disabled.")

        # Monitoring thread
        self.monitoring_thread = None
        self.stop_event = threading.Event()
        self.is_running = False

        # Event log handle
        self.event_log_handle = None

        # Last event time
        self.last_event_time = datetime.now() - timedelta(minutes=5)

        # Event handlers
        self.event_handlers = {
            1: self._handle_process_creation,
            2: self._handle_file_creation_time,
            3: self._handle_network_connection,
            5: self._handle_process_termination,
            11: self._handle_file_creation,
            12: self._handle_registry_event,
            13: self._handle_registry_event,
            14: self._handle_registry_event
        }

        self.logger.info("Sysmon Monitor initialized")

    def _check_sysmon_installed(self) -> bool:
        """
        Check if Sysmon is installed.

        Returns:
            bool: True if installed, False otherwise
        """
        try:
            # Try to open the Sysmon event log
            handle = win32evtlog.OpenEventLog(None, "Microsoft-Windows-Sysmon/Operational")
            win32evtlog.CloseEventLog(handle)
            return True
        except Exception:
            return False

    def start_monitoring(self, stop_event: Optional[threading.Event] = None) -> bool:
        """
        Start Sysmon monitoring.

        Args:
            stop_event (Optional[threading.Event]): Event to signal stopping

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.sysmon_installed:
            self.logger.error("Sysmon is not installed. Cannot start monitoring.")
            return False

        if self.is_running:
            self.logger.warning("Sysmon monitoring is already running")
            return True

        self.logger.info("Starting Sysmon monitoring")

        # Use provided stop event or internal one
        self.stop_event = stop_event or self.stop_event
        self.stop_event.clear()

        try:
            # Open the Sysmon event log
            self.event_log_handle = win32evtlog.OpenEventLog(None, "Microsoft-Windows-Sysmon/Operational")
        except Exception as e:
            self.logger.error(f"Error opening Sysmon event log: {e}")
            return False

        # Start monitoring thread
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self.monitoring_thread.start()

        self.is_running = True
        return True

    def stop_monitoring(self) -> bool:
        """
        Stop Sysmon monitoring.

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.is_running:
            return True

        self.logger.info("Stopping Sysmon monitoring")
        self.stop_event.set()

        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=10.0)

        # Close the event log handle
        if self.event_log_handle:
            try:
                win32evtlog.CloseEventLog(self.event_log_handle)
            except Exception as e:
                self.logger.error(f"Error closing Sysmon event log: {e}")

        self.is_running = False
        return True

    def _monitoring_loop(self) -> None:
        """Sysmon monitoring loop."""
        while not self.stop_event.is_set():
            try:
                # Read events from the Sysmon event log
                events = self._read_events()

                # Process events
                for event in events:
                    self._process_event(event)

                # Wait a bit before reading more events
                self.stop_event.wait(1.0)

            except Exception as e:
                self.logger.error(f"Error during Sysmon monitoring: {e}")
                # Wait a bit before retrying
                self.stop_event.wait(1.0)

        self.logger.info("Sysmon monitoring stopped")

    def _read_events(self) -> List[Dict[str, Any]]:
        """
        Read events from the Sysmon event log.

        Returns:
            List[Dict[str, Any]]: List of events
        """
        events = []

        try:
            # Read events from the event log
            flags = win32evtlog.EVENTLOG_BACKWARDS_READ | win32evtlog.EVENTLOG_SEQUENTIAL_READ
            events_raw = win32evtlog.ReadEventLog(self.event_log_handle, flags, 0)

            # Process events
            for event_raw in events_raw:
                # Convert event time to datetime
                event_time = datetime.fromtimestamp(int(event_raw.TimeGenerated))

                # Skip events older than last_event_time
                if event_time <= self.last_event_time:
                    continue

                # Update last event time
                if event_time > self.last_event_time:
                    self.last_event_time = event_time

                # Extract event data
                event = {
                    "event_id": event_raw.EventID,
                    "time_generated": event_time,
                    "source_name": event_raw.SourceName,
                    "event_category": event_raw.EventCategory,
                    "event_type": event_raw.EventType,
                    "data": self._parse_event_data(event_raw)
                }

                events.append(event)

        except Exception as e:
            if e.args[0] != winerror.ERROR_HANDLE_EOF:
                self.logger.error(f"Error reading Sysmon events: {e}")

        return events

    def _parse_event_data(self, event_raw) -> Dict[str, Any]:
        """
        Parse event data from a raw event.

        Args:
            event_raw: Raw event

        Returns:
            Dict[str, Any]: Parsed event data
        """
        data = {}

        try:
            # Get event description
            desc = win32evtlogutil.SafeFormatMessage(event_raw, "Microsoft-Windows-Sysmon/Operational")

            # Parse description into key-value pairs
            for line in desc.split("\n"):
                if ":" in line:
                    key, value = line.split(":", 1)
                    data[key.strip()] = value.strip()

        except Exception as e:
            self.logger.error(f"Error parsing event data: {e}")

        return data

    def _process_event(self, event: Dict[str, Any]) -> None:
        """
        Process a Sysmon event.

        Args:
            event (Dict[str, Any]): Event to process
        """
        # Get event ID
        event_id = event["event_id"]

        # Call the appropriate handler
        handler = self.event_handlers.get(event_id)
        if handler:
            handler(event)

    def _handle_process_creation(self, event: Dict[str, Any]) -> None:
        """
        Handle process creation event (Event ID 1).

        Args:
            event (Dict[str, Any]): Event to handle
        """
        data = event["data"]

        # Extract process information
        process_info = {
            "image": data.get("Image", ""),
            "command_line": data.get("CommandLine", ""),
            "current_directory": data.get("CurrentDirectory", ""),
            "user": data.get("User", ""),
            "logon_guid": data.get("LogonGuid", ""),
            "logon_id": data.get("LogonId", ""),
            "terminal_session_id": data.get("TerminalSessionId", ""),
            "integrity_level": data.get("IntegrityLevel", ""),
            "hashes": data.get("Hashes", ""),
            "parent_process_guid": data.get("ParentProcessGuid", ""),
            "parent_process_id": data.get("ParentProcessId", ""),
            "parent_image": data.get("ParentImage", ""),
            "parent_command_line": data.get("ParentCommandLine", "")
        }

        self.logger.debug(f"Process creation: {process_info['image']}")

        # Check for suspicious processes
        if self.alert_manager:
            # Check against suspicious patterns
            suspicious_patterns = self.config.get("process_monitoring", {}).get(
                "suspicious_process_patterns", []
            )

            for pattern in suspicious_patterns:
                if (pattern.lower() in process_info["image"].lower() or
                    pattern.lower() in process_info["command_line"].lower()):
                    self.alert_manager.add_alert(
                        source="SysmonMonitor",
                        alert_type="suspicious_process",
                        message=f"Suspicious process created: {process_info['image']}",
                        severity="warning",
                        details=process_info
                    )
                    break

    def _handle_file_creation_time(self, event: Dict[str, Any]) -> None:
        """
        Handle file creation time change event (Event ID 2).

        Args:
            event (Dict[str, Any]): Event to handle
        """
        data = event["data"]

        # Extract file information
        file_info = {
            "image": data.get("Image", ""),
            "target_filename": data.get("TargetFilename", ""),
            "creation_utc_time": data.get("CreationUtcTime", ""),
            "previous_creation_utc_time": data.get("PreviousCreationUtcTime", "")
        }

        self.logger.debug(f"File creation time change: {file_info['target_filename']}")

        # Check for suspicious file time changes
        if self.alert_manager:
            # Check file extension
            suspicious_extensions = self.config.get("filesystem_monitoring", {}).get(
                "suspicious_extensions", []
            )

            _, ext = os.path.splitext(file_info["target_filename"])
            if ext.lower() in suspicious_extensions:
                self.alert_manager.add_alert(
                    source="SysmonMonitor",
                    alert_type="suspicious_file_time_change",
                    message=f"Suspicious file time change: {file_info['target_filename']}",
                    severity="warning",
                    details=file_info
                )

    def _handle_network_connection(self, event: Dict[str, Any]) -> None:
        """
        Handle network connection event (Event ID 3).

        Args:
            event (Dict[str, Any]): Event to handle
        """
        data = event["data"]

        # Extract connection information
        connection_info = {
            "image": data.get("Image", ""),
            "user": data.get("User", ""),
            "protocol": data.get("Protocol", ""),
            "source_ip": data.get("SourceIp", ""),
            "source_port": data.get("SourcePort", ""),
            "destination_ip": data.get("DestinationIp", ""),
            "destination_port": data.get("DestinationPort", ""),
            "destination_hostname": data.get("DestinationHostname", "")
        }

        self.logger.debug(f"Network connection: {connection_info['image']} -> {connection_info['destination_ip']}:{connection_info['destination_port']}")

        # Check for suspicious connections
        if self.alert_manager:
            # Check against suspicious domains and ports
            suspicious_domains = self.config.get("network_monitoring", {}).get(
                "suspicious_domains", []
            )

            suspicious_ports = self.config.get("network_monitoring", {}).get(
                "suspicious_ports", []
            )

            is_suspicious = False
            reason = ""

            # Check domain
            for domain in suspicious_domains:
                if (domain in connection_info["destination_hostname"] or
                    domain in connection_info["destination_ip"]):
                    is_suspicious = True
                    reason = f"Suspicious domain: {domain}"
                    break

            # Check port
            if not is_suspicious and connection_info["destination_port"].isdigit():
                port = int(connection_info["destination_port"])
                if port in suspicious_ports:
                    is_suspicious = True
                    reason = f"Suspicious port: {port}"

            if is_suspicious:
                self.alert_manager.add_alert(
                    source="SysmonMonitor",
                    alert_type="suspicious_connection",
                    message=f"Suspicious network connection detected: {connection_info['destination_ip']}:{connection_info['destination_port']}",
                    severity="warning",
                    details={
                        **connection_info,
                        "reason": reason
                    }
                )

    def _handle_process_termination(self, event: Dict[str, Any]) -> None:
        """
        Handle process termination event (Event ID 5).

        Args:
            event (Dict[str, Any]): Event to handle
        """
        data = event["data"]

        # Extract process information
        process_info = {
            "image": data.get("Image", ""),
            "process_guid": data.get("ProcessGuid", ""),
            "process_id": data.get("ProcessId", "")
        }

        self.logger.debug(f"Process termination: {process_info['image']}")

    def _handle_file_creation(self, event: Dict[str, Any]) -> None:
        """
        Handle file creation event (Event ID 11).

        Args:
            event (Dict[str, Any]): Event to handle
        """
        data = event["data"]

        # Extract file information
        file_info = {
            "image": data.get("Image", ""),
            "target_filename": data.get("TargetFilename", ""),
            "creation_utc_time": data.get("CreationUtcTime", "")
        }

        self.logger.debug(f"File creation: {file_info['target_filename']}")

        # Check for suspicious files
        if self.alert_manager:
            # Check file extension
            suspicious_extensions = self.config.get("filesystem_monitoring", {}).get(
                "suspicious_extensions", []
            )

            _, ext = os.path.splitext(file_info["target_filename"])
            if ext.lower() in suspicious_extensions:
                self.alert_manager.add_alert(
                    source="SysmonMonitor",
                    alert_type="suspicious_file",
                    message=f"Suspicious file created: {file_info['target_filename']}",
                    severity="warning",
                    details=file_info
                )

    def _handle_registry_event(self, event: Dict[str, Any]) -> None:
        """
        Handle registry event (Event ID 12, 13, 14).

        Args:
            event (Dict[str, Any]): Event to handle
        """
        data = event["data"]

        # Extract registry information
        registry_info = {
            "event_type": event["event_id"],
            "image": data.get("Image", ""),
            "target_object": data.get("TargetObject", ""),
            "details": data.get("Details", "")
        }

        # Map event ID to event type
        event_type_map = {
            12: "registry_object_create_or_delete",
            13: "registry_value_set",
            14: "registry_key_and_value_rename"
        }

        registry_info["event_type_name"] = event_type_map.get(event["event_id"], "registry_event")

        self.logger.debug(f"Registry event: {registry_info['target_object']}")

        # Check for suspicious registry changes
        if self.alert_manager:
            # Check for autorun registry keys
            autorun_keys = [
                "\\REGISTRY\\MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run",
                "\\REGISTRY\\MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunOnce",
                "\\REGISTRY\\USER\\.*\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run",
                "\\REGISTRY\\USER\\.*\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunOnce"
            ]

            for key in autorun_keys:
                if key in registry_info["target_object"]:
                    self.alert_manager.add_alert(
                        source="SysmonMonitor",
                        alert_type="registry_autorun",
                        message=f"Autorun registry key modified: {registry_info['target_object']}",
                        severity="warning",
                        details=registry_info
                    )
                    break
