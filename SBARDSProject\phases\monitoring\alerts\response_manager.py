"""
Response Manager for SBARDS

This module provides response management for the SBARDS project.
"""

import os
import time
import json
import logging
import threading
import subprocess
from typing import Dict, List, Any, Optional, Set

class ResponseManager:
    """
    Response Manager for SBARDS.
    
    This class manages responses to alerts generated by monitoring components.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the response manager.
        
        Args:
            config (Dict[str, Any]): Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger("SBARDS.ResponseManager")
        
        # Response thread
        self.response_thread = None
        self.stop_event = threading.Event()
        self.is_running = False
        
        # Response queue
        self.response_queue = []
        self.queue_lock = threading.Lock()
        
        # Response interval
        self.response_interval = config.get("response", {}).get("interval_seconds", 5)
        
        # Response level
        self.response_level = config.get("response", {}).get("response_level", "medium")
        
        # Auto respond
        self.auto_respond = config.get("response", {}).get("auto_respond", True)
        
        self.logger.info("Response Manager initialized")
        
    def start(self) -> bool:
        """
        Start response processing.
        
        Returns:
            bool: True if successful, False otherwise
        """
        if self.is_running:
            self.logger.warning("Response processing is already running")
            return True
            
        self.logger.info("Starting response processing")
        self.stop_event.clear()
        
        # Start response thread
        self.response_thread = threading.Thread(
            target=self._response_loop,
            daemon=True
        )
        self.response_thread.start()
        
        self.is_running = True
        return True
        
    def stop(self) -> bool:
        """
        Stop response processing.
        
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.is_running:
            return True
            
        self.logger.info("Stopping response processing")
        self.stop_event.set()
        
        if self.response_thread:
            self.response_thread.join(timeout=10.0)
            
        self.is_running = False
        return True
        
    def _response_loop(self) -> None:
        """Response processing loop."""
        while not self.stop_event.is_set():
            try:
                # Process response queue
                self._process_response_queue()
                
                # Wait for next response processing cycle
                self.stop_event.wait(self.response_interval)
                
            except Exception as e:
                self.logger.error(f"Error during response processing: {e}")
                # Wait a bit before retrying
                self.stop_event.wait(1.0)
                
        self.logger.info("Response processing stopped")
        
    def _process_response_queue(self) -> None:
        """Process response queue."""
        with self.queue_lock:
            # Get response queue
            queue = self.response_queue
            self.response_queue = []
            
        # Process queue
        for alert in queue:
            self._handle_alert_response(alert)
            
    def handle_alert(self, alert: Dict[str, Any]) -> None:
        """
        Handle an alert.
        
        Args:
            alert (Dict[str, Any]): Alert to handle
        """
        # Add to response queue
        with self.queue_lock:
            self.response_queue.append(alert)
            
    def _handle_alert_response(self, alert: Dict[str, Any]) -> None:
        """
        Handle alert response.
        
        Args:
            alert (Dict[str, Any]): Alert to handle
        """
        # Check if auto respond is enabled
        if not self.auto_respond:
            return
            
        # Get alert severity
        severity = alert.get("severity", "info")
        
        # Check response level
        if self._should_respond(severity):
            # Get alert type
            alert_type = alert.get("type", "")
            
            # Handle different alert types
            if alert_type == "suspicious_process":
                self._handle_suspicious_process(alert)
            elif alert_type == "suspicious_file":
                self._handle_suspicious_file(alert)
            elif alert_type == "suspicious_network":
                self._handle_suspicious_network(alert)
            else:
                self.logger.debug(f"No specific handler for alert type: {alert_type}")
                
    def _should_respond(self, severity: str) -> bool:
        """
        Check if should respond to alert.
        
        Args:
            severity (str): Alert severity
            
        Returns:
            bool: True if should respond, False otherwise
        """
        # Response level thresholds
        thresholds = {
            "low": ["critical"],
            "medium": ["critical", "error"],
            "high": ["critical", "error", "warning"],
            "all": ["critical", "error", "warning", "info"]
        }
        
        # Get threshold for current response level
        threshold = thresholds.get(self.response_level, ["critical"])
        
        return severity.lower() in threshold
        
    def _handle_suspicious_process(self, alert: Dict[str, Any]) -> None:
        """
        Handle suspicious process alert.
        
        Args:
            alert (Dict[str, Any]): Alert to handle
        """
        # Check if should terminate processes
        if not self.config.get("response", {}).get("actions", {}).get("terminate_processes", False):
            return
            
        # Get process details
        details = alert.get("details", {})
        process_id = details.get("process_id")
        
        if process_id:
            try:
                # Terminate process
                self.logger.warning(f"Terminating suspicious process: {process_id}")
                
                if os.name == "nt":
                    subprocess.run(["taskkill", "/F", "/PID", str(process_id)], check=False)
                else:
                    subprocess.run(["kill", "-9", str(process_id)], check=False)
                    
            except Exception as e:
                self.logger.error(f"Error terminating process {process_id}: {e}")
                
    def _handle_suspicious_file(self, alert: Dict[str, Any]) -> None:
        """
        Handle suspicious file alert.
        
        Args:
            alert (Dict[str, Any]): Alert to handle
        """
        # Check if should quarantine files
        if not self.config.get("response", {}).get("actions", {}).get("quarantine_files", False):
            return
            
        # Get file details
        details = alert.get("details", {})
        file_path = details.get("file_path")
        
        if file_path and os.path.exists(file_path):
            try:
                # Quarantine file
                self.logger.warning(f"Quarantining suspicious file: {file_path}")
                
                # Create quarantine directory
                quarantine_dir = os.path.join(
                    self.config.get("output", {}).get("output_directory", "output"),
                    "quarantine"
                )
                os.makedirs(quarantine_dir, exist_ok=True)
                
                # Move file to quarantine
                quarantine_path = os.path.join(
                    quarantine_dir,
                    os.path.basename(file_path) + ".quarantine"
                )
                os.rename(file_path, quarantine_path)
                
            except Exception as e:
                self.logger.error(f"Error quarantining file {file_path}: {e}")
                
    def _handle_suspicious_network(self, alert: Dict[str, Any]) -> None:
        """
        Handle suspicious network alert.
        
        Args:
            alert (Dict[str, Any]): Alert to handle
        """
        # Check if should block connections
        if not self.config.get("response", {}).get("actions", {}).get("block_connections", False):
            return
            
        # Get connection details
        details = alert.get("details", {})
        remote_address = details.get("remote_address")
        
        if remote_address:
            try:
                # Block connection
                self.logger.warning(f"Blocking suspicious connection to: {remote_address}")
                
                if os.name == "nt":
                    # Use Windows Firewall
                    subprocess.run([
                        "netsh", "advfirewall", "firewall", "add", "rule",
                        "name=SBARDS_Block", "dir=out", "action=block",
                        f"remoteip={remote_address}"
                    ], check=False)
                else:
                    # Use iptables
                    subprocess.run([
                        "iptables", "-A", "OUTPUT", "-d", remote_address, "-j", "DROP"
                    ], check=False)
                    
            except Exception as e:
                self.logger.error(f"Error blocking connection to {remote_address}: {e}")
