rule Ransomware_Encryption_Operations {
    meta:
        description = "Detects common ransomware encryption operations"
        author = "SBARDS Project"
        date = "2023-06-15"
        category = "ransomware"
        severity = "critical"

    strings:
        // Encryption API calls
        $enc_api1 = "CryptEncrypt" ascii wide
        $enc_api2 = "CryptGenKey" ascii wide
        $enc_api3 = "CryptDeriveKey" ascii wide
        $enc_api4 = "CryptCreateHash" ascii wide
        $enc_api5 = "EVP_EncryptInit" ascii wide
        $enc_api6 = "EVP_EncryptUpdate" ascii wide
        
        // Encryption algorithms
        $enc_alg1 = "AES" ascii wide
        $enc_alg2 = "RSA" ascii wide
        $enc_alg3 = "Rijndael" ascii wide
        $enc_alg4 = "Twofish" ascii wide
        $enc_alg5 = "Blowfish" ascii wide
        
        // File operations
        $file_op1 = "CreateFile" ascii wide
        $file_op2 = "ReadFile" ascii wide
        $file_op3 = "WriteFile" ascii wide
        $file_op4 = "DeleteFile" ascii wide
        $file_op5 = "MoveFile" ascii wide
        $file_op6 = "FindFirstFile" ascii wide
        $file_op7 = "FindNextFile" ascii wide

    condition:
        (2 of ($enc_api*)) and (1 of ($enc_alg*)) and (3 of ($file_op*))
}

rule Ransomware_File_Extensions {
    meta:
        description = "Detects common ransomware file extensions"
        author = "SBARDS Project"
        date = "2023-06-15"
        category = "ransomware"
        severity = "high"

    strings:
        $ext1 = ".locked" nocase
        $ext2 = ".crypt" nocase
        $ext3 = ".encrypted" nocase
        $ext4 = ".crypted" nocase
        $ext5 = ".cerber" nocase
        $ext6 = ".locky" nocase
        $ext7 = ".zepto" nocase
        $ext8 = ".thor" nocase
        $ext9 = ".aesir" nocase
        $ext10 = ".zzzzz" nocase
        $ext11 = ".osiris" nocase
        $ext12 = ".cryptolocker" nocase
        $ext13 = ".cryptowall" nocase
        $ext14 = ".crypz" nocase
        $ext15 = ".cryp1" nocase

    condition:
        any of them
}

rule Ransomware_Bitcoin_Addresses {
    meta:
        description = "Detects Bitcoin addresses commonly used in ransomware"
        author = "SBARDS Project"
        date = "2023-06-15"
        category = "ransomware"
        severity = "medium"

    strings:
        // Bitcoin address regex pattern
        $btc_addr = /[13][a-km-zA-HJ-NP-Z1-9]{25,34}/ ascii wide
        
        // Common ransom words near Bitcoin addresses
        $ransom1 = "payment" nocase ascii wide
        $ransom2 = "bitcoin" nocase ascii wide
        $ransom3 = "wallet" nocase ascii wide
        $ransom4 = "address" nocase ascii wide
        $ransom5 = "transfer" nocase ascii wide
        $ransom6 = "send" nocase ascii wide
        $ransom7 = "money" nocase ascii wide
        $ransom8 = "pay" nocase ascii wide
        $ransom9 = "BTC" nocase ascii wide

    condition:
        $btc_addr and 2 of ($ransom*)
}