# Enhanced SBARDS - Directory Scanning & Continuous Phases Guide

## 🚀 New Features Overview

The SBARDS system has been significantly enhanced with:

1. **Directory Scanning Support** - All phases can now process directories and subdirectories
2. **Continuous Phases Mode** - New integrated workflow that runs all phases sequentially
3. **File Type Filtering** - Scan specific file types (e.g., .exe, .dll, .pdf)
4. **Scalable Processing** - Handle large directories with configurable limits
5. **Comprehensive Reporting** - Detailed analysis summaries and threat assessments

## 📁 Directory Scanning Features

### Supported Phases
- ✅ **Capture Layer** - Captures all files from directories
- ✅ **Pre-Scanning** - Quick threat detection across multiple files
- ✅ **Static Analysis** - Comprehensive analysis of directory contents
- ✅ **Dynamic Analysis** - Behavioral analysis (single file representative)
- ✅ **Complete Workflow** - Full workflow with directory support
- ✅ **Continuous Phases** - All phases in sequence with directory support

### Configuration Options
- **Recursive Scanning** - Include subdirectories (`--recursive`)
- **File Limits** - Maximum files to process (`--max-files`)
- **File Type Filtering** - Specific extensions (`--file-types`)

## 🔄 Continuous Phases Mode

The new **Continuous Phases** mode runs all phases sequentially as a single integrated project:

### Phase Execution Order
1. **Capture Layer** - File/directory ingestion and validation
2. **Pre-Scanning** - Quick threat detection with YARA rules
3. **Static Analysis** - Comprehensive static analysis
4. **Dynamic Analysis** - Behavioral analysis (if available)
5. **Response Layer** - Automated response actions
6. **Monitoring** - Short-term continuous monitoring

### Benefits
- **Integrated Workflow** - All phases work together seamlessly
- **Comprehensive Analysis** - Complete threat assessment
- **Automated Decision Making** - Intelligent threat level determination
- **Detailed Reporting** - Executive summary with recommendations

## 💻 Command Line Usage

### Directory Scanning Examples

```bash
# Pre-scan a directory recursively
python main.py --phase prescanning --file "E:\WA" --recursive --max-files 100

# Static analysis on directory with file type filtering
python main.py --phase static --file "/path/to/directory" --file-types .exe .dll .pdf

# Capture layer with limited file count
python main.py --phase capture --file "C:\Temp" --max-files 50 --recursive

# Continuous phases on directory
python main.py --phase continuous --file "E:\WA" --max-files 20 --recursive
```

### Single File Examples

```bash
# Traditional single file analysis
python main.py --phase static --file "suspicious.exe"
python main.py --phase workflow --file "malware.dll"
```

## 🎯 Interactive Mode Enhancements

The interactive mode now supports:
- Directory path input for all compatible phases
- Recursive scanning options
- File count limits
- File type filtering
- Continuous phases execution

### Interactive Menu Options
```
1. Capture Layer (طبقة الالتقاط)
2. Pre-Scanning Quick Check (فحص أولي سريع)  
3. Static Analysis Layer (طبقة التحليل الثابت)
4. Dynamic Analysis Layer (طبقة التحليل الديناميكي)
5. Response Layer (طبقة الاستجابة)
6. External Integration Layer (طبقة التكامل الخارجي)
7. Memory Protection Layer (طبقة حماية الذاكرة)
8. Continuous Monitoring Layer (طبقة المراقبة المستمرة)
9. Complete Workflow (جميع المراحل)
10. Continuous Phases (المراحل المتواصلة) ← NEW!
11. Interactive Mode (الوضع التفاعلي)
```

## 📊 Enhanced Reporting

### Directory Scan Results
- **Files Processed** - Total count of analyzed files
- **Threat Summary** - Aggregated risk scores and YARA matches
- **High-Risk Files** - List of files requiring attention
- **Performance Metrics** - Scan times and throughput

### Continuous Phases Summary
```
CONTINUOUS PHASES EXECUTION SUMMARY
================================================================================
Execution ID: continuous_1732567890
Target: E:\WA
Execution Time: 15.3 minutes
Phases Completed: 6/6

THREAT ANALYSIS SUMMARY:
  - Threat Level: HIGH
  - YARA Matches: 5
  - Average Risk Score: 65.2/100
  - High Risk Files: 3

RECOMMENDATION: QUARANTINE - Malware signatures detected

PHASE EXECUTION DETAILS:
  ✓ CAPTURE: COMPLETED
  ✓ PRESCANNING: COMPLETED  
  ✓ STATIC_ANALYSIS: COMPLETED
  ✓ DYNAMIC_ANALYSIS: COMPLETED
  ✓ RESPONSE: COMPLETED
  ✓ MONITORING: COMPLETED
```

## 🔧 Best Practices

### Directory Scanning
1. **Start Small** - Test with limited file counts first
2. **Use File Filtering** - Focus on executable and suspicious file types
3. **Monitor Resources** - Large directories may require significant processing time
4. **Review Results** - Check aggregated reports for threat indicators

### Continuous Phases
1. **Comprehensive Analysis** - Use for thorough security assessments
2. **Automated Workflows** - Ideal for scheduled security scans
3. **Incident Response** - Quick assessment of compromised directories
4. **Compliance Audits** - Complete documentation trail

### Performance Optimization
- Use `--max-files` to limit processing scope
- Filter file types with `--file-types` for targeted analysis
- Consider non-recursive scanning for large directory trees
- Monitor system resources during large scans

## 🛡️ Security Considerations

### File Type Priorities
- **High Priority**: `.exe`, `.dll`, `.scr`, `.bat`, `.cmd`, `.ps1`
- **Medium Priority**: `.pdf`, `.doc`, `.xls`, `.zip`, `.rar`
- **Low Priority**: `.txt`, `.log`, `.cfg`, `.ini`

### Threat Level Interpretation
- **HIGH**: Immediate action required (quarantine/investigate)
- **MEDIUM**: Review and monitor closely
- **LOW**: Standard monitoring sufficient

## 📈 Performance Metrics

### Typical Processing Rates
- **Pre-Scanning**: ~100-500 files/minute
- **Static Analysis**: ~50-200 files/minute  
- **Directory Capture**: ~200-1000 files/minute
- **Continuous Phases**: ~20-100 files/minute (complete analysis)

### Resource Requirements
- **Memory**: 2-8 GB depending on file sizes and analysis depth
- **CPU**: Multi-core recommended for large directory scans
- **Storage**: Temporary space for captured files and analysis results
- **Network**: Required for VirusTotal integration (if enabled)

## 🔍 Troubleshooting

### Common Issues
1. **Permission Errors** - Ensure read access to target directories
2. **Large Directory Timeouts** - Use `--max-files` to limit scope
3. **Memory Issues** - Process directories in smaller batches
4. **Docker Unavailable** - Dynamic analysis will be skipped automatically

### Error Recovery
- Failed files are logged but don't stop the overall process
- Partial results are saved even if some phases fail
- Detailed error logs help identify specific issues
- Graceful degradation when optional components unavailable

## 🎉 Success Examples

### Real-World Usage
```bash
# Security audit of Windows directory
python main.py --phase continuous --file "C:\Windows\System32" --max-files 100 --file-types .exe .dll

# Malware investigation
python main.py --phase static --file "E:\Quarantine" --recursive --max-files 50

# Quick threat assessment
python main.py --phase prescanning --file "Downloads" --recursive --max-files 200
```

The enhanced SBARDS system provides enterprise-grade malware detection and analysis capabilities with comprehensive directory scanning and integrated workflow management.
