"""
Optimized Inspector for SBARDS

This module provides a high-performance inspection engine that works in conjunction
with the pre-inspection and monitoring layers.
"""

import os
import io
import time
import hashlib
import logging
import threading
import multiprocessing
import concurrent.futures
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, ProcessPoolExecutor
from typing import Dict, List, Any, Optional, Set, Tuple, Callable
from datetime import datetime
import mmap
import functools
import queue

# Import SBARDS components
from scanner_core.python.yara_wrapper import YaraScanner
from scanner_core.integration import PriorityManager

# Cache for file hashes and scan results
file_hash_cache = {}
scan_result_cache = {}
cache_lock = threading.RLock()

# Minimum file size for memory mapping (4KB)
MIN_MMAP_SIZE = 4 * 1024

# Maximum number of processes to use
MAX_PROCESSES = max(1, multiprocessing.cpu_count() - 1)

# Maximum number of threads per process
MAX_THREADS_PER_PROCESS = 4

class OptimizedInspector:
    """
    High-performance file inspection engine.

    This class provides optimized file inspection capabilities:
    1. Parallel processing with both threads and processes
    2. Memory-mapped file I/O for large files
    3. Buffered I/O for small files
    4. Result caching based on file hashes
    5. Priority-based scanning
    6. Incremental scanning
    7. Early termination for obvious matches
    """

    def __init__(self, config: Dict[str, Any], yara_scanner: Optional[YaraScanner] = None):
        """
        Initialize the optimized inspector.

        Args:
            config (Dict[str, Any]): Configuration dictionary
            yara_scanner (Optional[YaraScanner]): YARA scanner instance
        """
        self.config = config
        self.logger = logging.getLogger("SBARDS.OptimizedInspector")

        # Initialize YARA scanner if not provided
        if yara_scanner is None:
            rule_files = config["rules"]["rule_files"]
            categories = config["rules"]["enable_categories"]
            self.yara_scanner = YaraScanner(rule_files, categories)
        else:
            self.yara_scanner = yara_scanner

        # Get inspection configuration
        self.inspection_config = config.get("inspection", {})

        # Performance settings
        self.use_parallel = self.inspection_config.get("use_parallel", True)
        self.use_mmap = self.inspection_config.get("use_mmap", True)
        self.use_caching = self.inspection_config.get("use_caching", True)
        self.use_early_termination = self.inspection_config.get("use_early_termination", True)
        self.max_processes = min(self.inspection_config.get("max_processes", MAX_PROCESSES), MAX_PROCESSES)
        self.max_threads = min(self.inspection_config.get("max_threads", MAX_THREADS_PER_PROCESS), MAX_THREADS_PER_PROCESS)

        # File size thresholds
        self.max_file_size_mb = self.config["scanner"].get("max_file_size_mb", 100)
        self.small_file_threshold = self.inspection_config.get("small_file_threshold", 1024 * 1024)  # 1MB

        # Priority manager for prioritized scanning
        self.priority_manager = None
        if config.get("integration", {}).get("priority", {}).get("enabled", False):
            from scanner_core.integration import SharedState
            shared_state = SharedState()
            self.priority_manager = PriorityManager(config, shared_state)

        # Scan result queue for monitoring layer
        self.result_queue = queue.Queue()

        # Statistics
        self.stats = {
            "files_scanned": 0,
            "files_matched": 0,
            "cache_hits": 0,
            "early_terminations": 0,
            "total_scan_time": 0,
            "bytes_scanned": 0
        }

        # Thread safety
        self._lock = threading.RLock()

        self.logger.info("Optimized Inspector initialized")

    def scan_files(self, file_paths: List[str], callback: Optional[Callable] = None) -> Dict[str, List[Dict]]:
        """
        Scan multiple files with optimized performance.

        Args:
            file_paths (List[str]): List of file paths to scan
            callback (Optional[Callable]): Optional callback function for each result

        Returns:
            Dict[str, List[Dict]]: Dictionary mapping file paths to scan results
        """
        start_time = time.time()
        results = {}

        # Skip if no files
        if not file_paths:
            return results

        # Sort files by priority if priority manager is available
        if self.priority_manager:
            prioritized_files = self._prioritize_files(file_paths)
        else:
            # Otherwise, sort by size (smallest first for quick wins)
            prioritized_files = sorted(
                [(path, os.path.getsize(path) if os.path.exists(path) else 0) for path in file_paths],
                key=lambda x: x[1]
            )
            prioritized_files = [path for path, _ in prioritized_files]

        # Use parallel processing if enabled and we have multiple files
        if self.use_parallel and len(prioritized_files) > 1:
            results = self._parallel_scan(prioritized_files, callback)
        else:
            # Sequential scanning
            for file_path in prioritized_files:
                try:
                    file_results = self.scan_file(file_path)
                    results[file_path] = file_results

                    # Call callback if provided
                    if callback and callable(callback):
                        callback(file_path, file_results)

                except Exception as e:
                    self.logger.error(f"Error scanning file {file_path}: {e}")
                    results[file_path] = []

        # Update statistics
        scan_time = time.time() - start_time
        with self._lock:
            self.stats["total_scan_time"] += scan_time
            self.stats["files_scanned"] += len(file_paths)
            self.stats["files_matched"] += sum(1 for matches in results.values() if matches)

        self.logger.info(f"Scanned {len(file_paths)} files in {scan_time:.2f} seconds")
        return results

    def scan_file(self, file_path: str) -> List[Dict]:
        """
        Scan a single file with optimized performance.

        Args:
            file_path (str): Path to the file to scan

        Returns:
            List[Dict]: List of scan results
        """
        # Check if file exists
        if not os.path.exists(file_path) or not os.path.isfile(file_path):
            self.logger.warning(f"File not found or not a regular file: {file_path}")
            return []

        try:
            # Get file size
            file_size = os.path.getsize(file_path)

            # Skip files that exceed the maximum size
            if file_size > (self.max_file_size_mb * 1024 * 1024):
                self.logger.warning(f"Skipping large file: {file_path} ({file_size / (1024 * 1024):.2f} MB)")
                return []

            # Check cache if enabled
            if self.use_caching:
                cached_result = self._check_cache(file_path)
                if cached_result is not None:
                    with self._lock:
                        self.stats["cache_hits"] += 1
                    return cached_result

            # Choose scanning method based on file size
            if file_size >= MIN_MMAP_SIZE and self.use_mmap:
                # Use memory-mapped I/O for large files
                results = self._scan_with_mmap(file_path)
            else:
                # Use buffered I/O for small files
                results = self._scan_with_buffer(file_path)

            # Update cache
            if self.use_caching:
                self._update_cache(file_path, results)

            # Update statistics
            with self._lock:
                self.stats["bytes_scanned"] += file_size

            # Add to result queue for monitoring layer
            self.result_queue.put({
                "file_path": file_path,
                "results": results,
                "timestamp": time.time()
            })

            return results

        except Exception as e:
            self.logger.error(f"Error scanning file {file_path}: {e}")
            return []

    def _scan_with_mmap(self, file_path: str) -> List[Dict]:
        """
        Scan a file using memory-mapped I/O.

        Args:
            file_path (str): Path to the file to scan

        Returns:
            List[Dict]: List of scan results
        """
        try:
            with open(file_path, 'rb') as f:
                # Create memory map
                with mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ) as mm:
                    # Scan the memory-mapped file
                    return self.yara_scanner.scan_data(mm)
        except Exception as e:
            self.logger.error(f"Error scanning with mmap {file_path}: {e}")
            # Fall back to buffered I/O
            return self._scan_with_buffer(file_path)

    def _scan_with_buffer(self, file_path: str) -> List[Dict]:
        """
        Scan a file using buffered I/O.

        Args:
            file_path (str): Path to the file to scan

        Returns:
            List[Dict]: List of scan results
        """
        try:
            # For small files, read the entire content into memory
            with open(file_path, 'rb') as f:
                file_data = f.read()

            # Early termination check for obvious malware signatures
            if self.use_early_termination and self._check_early_termination(file_data):
                with self._lock:
                    self.stats["early_terminations"] += 1
                return [{"rule": "EARLY_TERMINATION", "meta": {"description": "Early termination due to obvious malware signature"}}]

            # Scan the file data
            return self.yara_scanner.scan_data(file_data)

        except Exception as e:
            self.logger.error(f"Error scanning with buffer {file_path}: {e}")
            return []

    def _parallel_scan(self, file_paths: List[str], callback: Optional[Callable] = None) -> Dict[str, List[Dict]]:
        """
        Scan files in parallel using process and thread pools.

        Args:
            file_paths (List[str]): List of file paths to scan
            callback (Optional[Callable]): Optional callback function for each result

        Returns:
            Dict[str, List[Dict]]: Dictionary mapping file paths to scan results
        """
        results = {}

        # Group files by size for optimal processing
        small_files = []
        large_files = []

        for file_path in file_paths:
            try:
                file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
                if file_size <= self.small_file_threshold:
                    small_files.append(file_path)
                else:
                    large_files.append(file_path)
            except Exception:
                # If we can't get the size, assume it's a small file
                small_files.append(file_path)

        # Process small files with threads (I/O bound)
        if small_files:
            with ThreadPoolExecutor(max_workers=self.max_threads) as executor:
                future_to_file = {executor.submit(self.scan_file, file_path): file_path for file_path in small_files}

                for future in concurrent.futures.as_completed(future_to_file):
                    file_path = future_to_file[future]
                    try:
                        file_results = future.result()
                        results[file_path] = file_results

                        # Call callback if provided
                        if callback and callable(callback):
                            callback(file_path, file_results)

                    except Exception as e:
                        self.logger.error(f"Error in thread pool scanning {file_path}: {e}")
                        results[file_path] = []

        # Process large files with processes (CPU bound)
        if large_files and self.max_processes > 1:
            # We need to create a new YaraScanner for each process
            with ProcessPoolExecutor(max_workers=self.max_processes) as executor:
                # We can't pass the YaraScanner directly, so we'll pass the configuration
                scan_func = functools.partial(self._process_scan_wrapper,
                                             rule_files=self.config["rules"]["rule_files"],
                                             categories=self.config["rules"]["enable_categories"])

                future_to_file = {executor.submit(scan_func, file_path): file_path for file_path in large_files}

                for future in concurrent.futures.as_completed(future_to_file):
                    file_path = future_to_file[future]
                    try:
                        file_results = future.result()
                        results[file_path] = file_results

                        # Call callback if provided
                        if callback and callable(callback):
                            callback(file_path, file_results)

                    except Exception as e:
                        self.logger.error(f"Error in process pool scanning {file_path}: {e}")
                        results[file_path] = []
        else:
            # If we can't use processes, use threads for large files too
            for file_path in large_files:
                try:
                    file_results = self.scan_file(file_path)
                    results[file_path] = file_results

                    # Call callback if provided
                    if callback and callable(callback):
                        callback(file_path, file_results)

                except Exception as e:
                    self.logger.error(f"Error scanning large file {file_path}: {e}")
                    results[file_path] = []

        return results

    @staticmethod
    def _process_scan_wrapper(file_path: str, rule_files: List[str], categories: List[str]) -> List[Dict]:
        """
        Wrapper for scanning files in a separate process.

        Args:
            file_path (str): Path to the file to scan
            rule_files (List[str]): List of YARA rule files
            categories (List[str]): List of enabled categories

        Returns:
            List[Dict]: List of scan results
        """
        try:
            # Create a new YaraScanner instance for this process
            scanner = YaraScanner(rule_files, categories)

            # Scan the file
            with open(file_path, 'rb') as f:
                file_data = f.read()
                return scanner.scan_data(file_data)
        except Exception as e:
            logging.error(f"Error in process scan wrapper for {file_path}: {e}")
            return []

    def _check_cache(self, file_path: str) -> Optional[List[Dict]]:
        """
        Check if a file's scan results are in the cache.

        Args:
            file_path (str): Path to the file to check

        Returns:
            Optional[List[Dict]]: Cached scan results or None if not found
        """
        try:
            # Get file modification time and size
            file_stat = os.stat(file_path)
            file_mtime = file_stat.st_mtime
            file_size = file_stat.st_size

            # Generate a cache key based on file path, size, and modification time
            cache_key = f"{file_path}:{file_size}:{file_mtime}"

            with cache_lock:
                if cache_key in scan_result_cache:
                    return scan_result_cache[cache_key]

            # If we have a hash cache, check if the file hash matches
            with cache_lock:
                if file_path in file_hash_cache:
                    cached_hash, cached_mtime, cached_size = file_hash_cache[file_path]

                    # If the file hasn't changed, use the cached hash
                    if file_mtime == cached_mtime and file_size == cached_size:
                        # Check if we have results for this hash
                        if cached_hash in scan_result_cache:
                            return scan_result_cache[cached_hash]

            return None

        except Exception as e:
            self.logger.error(f"Error checking cache for {file_path}: {e}")
            return None

    def _update_cache(self, file_path: str, results: List[Dict]):
        """
        Update the cache with scan results.

        Args:
            file_path (str): Path to the file
            results (List[Dict]): Scan results
        """
        try:
            # Get file modification time and size
            file_stat = os.stat(file_path)
            file_mtime = file_stat.st_mtime
            file_size = file_stat.st_size

            # Generate a cache key based on file path, size, and modification time
            cache_key = f"{file_path}:{file_size}:{file_mtime}"

            # Calculate file hash for more reliable caching
            file_hash = self._calculate_file_hash(file_path)

            with cache_lock:
                # Update hash cache
                if file_hash:
                    file_hash_cache[file_path] = (file_hash, file_mtime, file_size)
                    scan_result_cache[file_hash] = results

                # Update result cache
                scan_result_cache[cache_key] = results

                # Limit cache size
                if len(scan_result_cache) > 1000:
                    # Remove oldest entries
                    keys_to_remove = list(scan_result_cache.keys())[:-1000]
                    for key in keys_to_remove:
                        scan_result_cache.pop(key, None)

                if len(file_hash_cache) > 1000:
                    # Remove oldest entries
                    keys_to_remove = list(file_hash_cache.keys())[:-1000]
                    for key in keys_to_remove:
                        file_hash_cache.pop(key, None)

        except Exception as e:
            self.logger.error(f"Error updating cache for {file_path}: {e}")

    def _calculate_file_hash(self, file_path: str) -> Optional[str]:
        """
        Calculate a hash for a file.

        Args:
            file_path (str): Path to the file

        Returns:
            Optional[str]: File hash or None if error
        """
        try:
            # For large files, use a faster hash of the first and last blocks
            file_size = os.path.getsize(file_path)

            if file_size > 1024 * 1024:  # 1MB
                with open(file_path, 'rb') as f:
                    # Read first 4KB
                    first_block = f.read(4096)

                    # Read last 4KB
                    f.seek(-4096, os.SEEK_END)
                    last_block = f.read(4096)

                # Hash the combination of first and last blocks plus file size
                hasher = hashlib.md5()
                hasher.update(first_block)
                hasher.update(last_block)
                hasher.update(str(file_size).encode())
                return hasher.hexdigest()
            else:
                # For small files, hash the entire content
                with open(file_path, 'rb') as f:
                    file_data = f.read()
                    return hashlib.md5(file_data).hexdigest()

        except Exception as e:
            self.logger.error(f"Error calculating hash for {file_path}: {e}")
            return None

    def _prioritize_files(self, file_paths: List[str]) -> List[str]:
        """
        Sort files by priority for optimal scanning order.

        Args:
            file_paths (List[str]): List of file paths to sort

        Returns:
            List[str]: Sorted list of file paths
        """
        if not self.priority_manager:
            return file_paths

        # Get priorities for all files
        file_priorities = []
        for file_path in file_paths:
            try:
                # Get file info for better prioritization
                file_info = {}
                if os.path.exists(file_path):
                    file_stat = os.stat(file_path)
                    file_info["size"] = file_stat.st_size
                    file_info["mtime"] = file_stat.st_mtime

                # Get priority from priority manager
                priority = self.priority_manager.get_file_priority(file_path, file_info)
                file_priorities.append((file_path, priority))
            except Exception as e:
                self.logger.error(f"Error getting priority for {file_path}: {e}")
                file_priorities.append((file_path, 0))

        # Sort by priority (highest first)
        sorted_files = [file_path for file_path, _ in sorted(file_priorities, key=lambda x: x[1], reverse=True)]
        return sorted_files

    def _check_early_termination(self, file_data: bytes) -> bool:
        """
        Check if a file contains obvious malware signatures for early termination.

        Args:
            file_data (bytes): File data to check

        Returns:
            bool: True if file contains obvious malware signatures
        """
        # Check for common malware signatures
        signatures = [
            b"This program cannot be run in DOS mode",  # PE header
            b"MZ",  # PE header
            b"TVqQAAMAAAAEAAAA",  # Base64 encoded MZ header
            b"powershell -e ",  # PowerShell encoded command
            b"cmd.exe /c ",  # Command prompt
            b"CreateObject(\"WScript.Shell\")",  # VBScript
            b"WScript.Shell",  # WScript
            b"ActiveXObject",  # JavaScript
            b"eval(",  # JavaScript eval
            b"document.write(unescape(",  # JavaScript unescape
            b"function() { return /",  # JavaScript regex
            b"\\x",  # Hex encoding
            b"kernel32.dll",  # Windows API
            b"user32.dll",  # Windows API
            b"advapi32.dll",  # Windows API
            b"CreateProcess",  # Windows API
            b"VirtualAlloc",  # Windows API
            b"WriteProcessMemory",  # Windows API
            b"ShellExecute",  # Windows API
            b"DownloadFile",  # PowerShell
            b"Invoke-Expression",  # PowerShell
            b"iex(",  # PowerShell
            b"New-Object System.Net.WebClient",  # PowerShell
            b"Start-Process",  # PowerShell
            b"hidden",  # PowerShell window style
            b"vssadmin delete shadows",  # Ransomware
            b"bcdedit /set {default}",  # Ransomware
            b"wbadmin delete catalog",  # Ransomware
            b"wmic shadowcopy delete",  # Ransomware
            b"YOUR FILES HAVE BEEN ENCRYPTED",  # Ransomware
            b"All your files have been encrypted",  # Ransomware
            b"Your files are encrypted",  # Ransomware
            b"Your files are locked",  # Ransomware
            b"Your files are no longer accessible",  # Ransomware
            b"To decrypt your files, you need to pay",  # Ransomware
            b"Bitcoin",  # Ransomware
            b"BTC",  # Ransomware
            b"Monero",  # Ransomware
            b"XMR",  # Ransomware
            b"Ethereum",  # Ransomware
            b"ETH",  # Ransomware
            b"wallet",  # Ransomware
            b"ransom",  # Ransomware
            b"README.txt",  # Ransomware
            b"HOW_TO_DECRYPT",  # Ransomware
            b"DECRYPT_INSTRUCTION",  # Ransomware
            b"RECOVERY_KEY",  # Ransomware
            b"HELP_DECRYPT",  # Ransomware
            b"HELP_YOUR_FILES",  # Ransomware
            b"HELP_TO_DECRYPT",  # Ransomware
            b"RESTORE_FILES",  # Ransomware
            b"UNLOCK_FILES",  # Ransomware
            b"RECOVER_FILES",  # Ransomware
            b"RECOVER_DATA",  # Ransomware
            b"RECOVER_FILE",  # Ransomware
            b"RECOVER_ALL_FILES",  # Ransomware
            b"RECOVER_ALL_DATA",  # Ransomware
            b"RECOVER_ALL_YOUR_FILES",  # Ransomware
            b"RECOVER_YOUR_FILES",  # Ransomware
        ]

        # Check for signatures
        for signature in signatures:
            if signature in file_data:
                return True

        return False

    def get_results_queue(self) -> queue.Queue:
        """
        Get the results queue for monitoring layer integration.

        Returns:
            queue.Queue: Results queue
        """
        return self.result_queue

    def get_stats(self) -> Dict[str, Any]:
        """
        Get inspection statistics.

        Returns:
            Dict[str, Any]: Statistics dictionary
        """
        with self._lock:
            stats = self.stats.copy()

            # Calculate additional metrics
            if stats["files_scanned"] > 0:
                stats["match_rate"] = stats["files_matched"] / stats["files_scanned"]
                stats["cache_hit_rate"] = stats["cache_hits"] / stats["files_scanned"] if stats["cache_hits"] > 0 else 0
                stats["early_termination_rate"] = stats["early_terminations"] / stats["files_scanned"] if stats["early_terminations"] > 0 else 0
                stats["avg_scan_time"] = stats["total_scan_time"] / stats["files_scanned"] if stats["total_scan_time"] > 0 else 0
            else:
                stats["match_rate"] = 0
                stats["cache_hit_rate"] = 0
                stats["early_termination_rate"] = 0
                stats["avg_scan_time"] = 0

            # Format byte counts
            stats["bytes_scanned_mb"] = stats["bytes_scanned"] / (1024 * 1024)

            return stats

    def clear_cache(self):
        """Clear the scan result cache."""
        with cache_lock:
            scan_result_cache.clear()
            file_hash_cache.clear()
            self.logger.info("Scan cache cleared")