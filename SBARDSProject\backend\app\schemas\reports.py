"""
Pydantic schemas for scan reports.

This module provides Pydantic schemas for scan reports.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any

from pydantic import BaseModel, Field


class FileResultBase(BaseModel):
    """Base schema for file results."""
    
    file_path: str = Field(..., description="Path to the file")
    file_hash: str = Field(..., description="SHA-256 hash of the file")
    is_threat: bool = Field(False, description="Whether the file is a threat")
    threat_type: Optional[str] = Field(None, description="Type of threat")


class FileResultCreate(FileResultBase):
    """Schema for creating file results."""
    pass


class FileResultResponse(FileResultBase):
    """Schema for file result responses."""
    
    id: int = Field(..., description="File result ID")
    virustotal_result: Optional[Dict[str, Any]] = Field(None, description="VirusTotal result")
    
    class Config:
        """Pydantic config."""
        orm_mode = True


class ScanReportBase(BaseModel):
    """Base schema for scan reports."""
    
    scan_id: str = Field(..., description="Unique identifier for the scan")
    scan_path: str = Field(..., description="Path that was scanned")
    files_scanned: int = Field(..., description="Number of files scanned")
    threats_found: int = Field(..., description="Number of threats found")
    report_path: str = Field(..., description="Path to the HTML report file")


class ScanReportCreate(ScanReportBase):
    """Schema for creating scan reports."""
    
    report_content: str = Field(..., description="Content of the HTML report")
    file_results: List[FileResultCreate] = Field([], description="List of file results")


class ScanReportResponse(ScanReportBase):
    """Schema for scan report responses."""
    
    id: int = Field(..., description="Scan report ID")
    timestamp: datetime = Field(..., description="Timestamp of the scan")
    report_content: Optional[str] = Field(None, description="Content of the HTML report")
    file_results: List[FileResultResponse] = Field([], description="List of file results")
    
    class Config:
        """Pydantic config."""
        orm_mode = True


class ScanReportList(BaseModel):
    """Schema for scan report list responses."""
    
    total: int = Field(..., description="Total number of scan reports")
    items: List[ScanReportResponse] = Field(..., description="List of scan reports")
    
    class Config:
        """Pydantic config."""
        orm_mode = True
