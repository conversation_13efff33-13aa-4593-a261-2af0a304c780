{"monitoring": {"enabled": true, "interval_seconds": 5, "enable_osquery": true, "enable_sysmon": true, "enable_etw": true, "process_monitoring": {"enabled": true, "suspicious_process_patterns": ["encrypt", "ransom", "crypt", "wncry", "wcry", "lockfiles", "cryptolocker", "bitlocker", "filecoder", "hidden_tear", "locky", "cerber", "petya"], "check_interval_seconds": 5, "max_history_entries": 2000, "memory_usage_threshold_percent": 80, "cpu_usage_threshold_percent": 90, "track_parent_child": true}, "filesystem_monitoring": {"enabled": true, "watch_directories": ["samples", "C:\\Users\\<USER>", "C:\\Users\\<USER>\\Documents", "C:\\Users\\<USER>\\Desktop", "C:\\Users\\<USER>\\Downloads"], "detect_mass_operations": true, "mass_operation_threshold": 5, "mass_operation_time_window_seconds": 10, "entropy_check": true, "entropy_threshold": 7.5, "suspicious_extensions": [".encrypted", ".locked", ".crypted", ".crypt", ".crypto", ".enc", ".ransomware", ".wcry", ".wncry", ".locky", ".zepto", ".cerber", ".<PERSON>on", ".enigma", ".pays"], "monitor_file_permissions": true}, "network_monitoring": {"enabled": true, "suspicious_domains": ["pastebin.com", "github.io", "example.com", "test-malicious-domain.com", "ransomware-command-server.net", "iplogger.org", "ngrok.io", "bitly.com", "tinyurl.com"], "check_interval_seconds": 15, "log_connections": true, "detect_data_exfiltration": true, "data_exfiltration_threshold_kb": 1000, "detect_unusual_protocols": true, "detect_unusual_ports": true, "suspicious_ports": [4444, 8080, 1337, 31337, 6666]}}, "osquery": {"interval_seconds": 10}, "etw": {"providers": ["Microsoft-Windows-Security-Auditing", "Microsoft-Windows-PowerShell", "Microsoft-Windows-WMI-Activity", "Microsoft-Windows-Kernel-Process", "Microsoft-Windows-Kernel-File"]}, "windows": {"registry_keys": ["HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Run", "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run", "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\RunOnce", "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\RunOnce", "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\StartupApproved\\Run", "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\StartupApproved\\Run"], "sysmon": {"event_log": "Microsoft-Windows-Sysmon/Operational", "interesting_event_ids": [1, 2, 3, 5, 11, 12, 13, 14]}}}