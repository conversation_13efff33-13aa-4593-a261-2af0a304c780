"""
Tests for the reports API endpoints.

This module provides tests for the reports API endpoints.
"""

import json
from datetime import datetime

import pytest
from fastapi import status

from app.db.models import ScanReport, FileResult
from app.core.config import settings


def test_create_scan_report(client, db):
    """Test creating a scan report."""
    # Create a scan report
    report_data = {
        "scan_id": "test_scan_123",
        "scan_path": "/test/path",
        "files_scanned": 10,
        "threats_found": 2,
        "report_path": "/test/path/report.html",
        "report_content": "<html><body>Test Report</body></html>",
        "file_results": [
            {
                "file_path": "/test/path/file1.txt",
                "file_hash": "abcdef1234567890",
                "is_threat": True,
                "threat_type": "Test Threat"
            }
        ]
    }
    
    response = client.post(f"{settings.API_V1_STR}/reports/", json=report_data)
    
    # Check response
    assert response.status_code == status.HTTP_201_CREATED
    data = response.json()
    assert data["scan_id"] == "test_scan_123"
    assert data["files_scanned"] == 10
    assert data["threats_found"] == 2
    assert len(data["file_results"]) == 1
    assert data["file_results"][0]["file_path"] == "/test/path/file1.txt"
    assert data["file_results"][0]["is_threat"] == True
    
    # Check database
    db_report = db.query(ScanReport).filter(ScanReport.scan_id == "test_scan_123").first()
    assert db_report is not None
    assert db_report.scan_path == "/test/path"
    assert db_report.files_scanned == 10
    assert db_report.threats_found == 2
    
    # Check file results
    db_file_results = db.query(FileResult).filter(FileResult.scan_report_id == db_report.id).all()
    assert len(db_file_results) == 1
    assert db_file_results[0].file_path == "/test/path/file1.txt"
    assert db_file_results[0].is_threat == True
    assert db_file_results[0].threat_type == "Test Threat"


def test_get_scan_reports(client, db):
    """Test getting scan reports."""
    # Create some scan reports
    for i in range(3):
        db_report = ScanReport(
            scan_id=f"test_scan_{i}",
            scan_path=f"/test/path_{i}",
            files_scanned=10,
            threats_found=i,
            report_path=f"/test/path_{i}/report.html",
            report_content=f"<html><body>Test Report {i}</body></html>"
        )
        db.add(db_report)
    db.commit()
    
    # Get scan reports
    response = client.get(f"{settings.API_V1_STR}/reports/")
    
    # Check response
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert len(data) == 3
    
    # Check that reports are ordered by timestamp (newest first)
    assert data[0]["scan_id"] == "test_scan_2"
    assert data[1]["scan_id"] == "test_scan_1"
    assert data[2]["scan_id"] == "test_scan_0"
    
    # Check that report_content is not included by default
    assert data[0]["report_content"] is None


def test_get_scan_report(client, db):
    """Test getting a specific scan report."""
    # Create a scan report
    db_report = ScanReport(
        scan_id="test_scan_123",
        scan_path="/test/path",
        files_scanned=10,
        threats_found=2,
        report_path="/test/path/report.html",
        report_content="<html><body>Test Report</body></html>"
    )
    db.add(db_report)
    
    # Add file results
    db_file_result = FileResult(
        scan_report_id=db_report.id,
        file_path="/test/path/file1.txt",
        file_hash="abcdef1234567890",
        is_threat=True,
        threat_type="Test Threat"
    )
    db.add(db_file_result)
    db.commit()
    
    # Get scan report
    response = client.get(f"{settings.API_V1_STR}/reports/test_scan_123")
    
    # Check response
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["scan_id"] == "test_scan_123"
    assert data["files_scanned"] == 10
    assert data["threats_found"] == 2
    assert data["report_content"] == "<html><body>Test Report</body></html>"
    assert len(data["file_results"]) == 1
    assert data["file_results"][0]["file_path"] == "/test/path/file1.txt"
    assert data["file_results"][0]["is_threat"] == True
    assert data["file_results"][0]["threat_type"] == "Test Threat"


def test_get_scan_report_not_found(client):
    """Test getting a non-existent scan report."""
    response = client.get(f"{settings.API_V1_STR}/reports/non_existent_scan")
    assert response.status_code == status.HTTP_404_NOT_FOUND
